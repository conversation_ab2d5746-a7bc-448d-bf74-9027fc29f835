import { api } from './api';

// Todo types based on Django model
export type TodoStatus = 'pending' | 'in_progress' | 'completed' | 'deferred';
export type TodoPriority = 'low' | 'medium' | 'high';

export interface TodoItem {
  id: number;
  title: string;
  description?: string | null;
  due_date?: string | null; // YYYY-MM-DD format
  status: TodoStatus;
  priority: TodoPriority;
  is_current_focus: boolean;
  completed_at?: string | null;
  created_at: string;
  updated_at: string;
  user: number;
}

export interface CreateTodoData {
  title: string;
  description?: string;
  due_date?: string;
  priority?: TodoPriority;
  is_current_focus?: boolean;
}

export interface UpdateTodoData {
  title?: string;
  description?: string;
  due_date?: string;
  status?: TodoStatus;
  priority?: TodoPriority;
  is_current_focus?: boolean;
}

export const todoService = {
  async getTodos(): Promise<TodoItem[]> {
    const response = await api.get<TodoItem[]>('/todo/todos/');
    return response.data;
  },

  async getTodo(id: number): Promise<TodoItem> {
    const response = await api.get<TodoItem>(`/todo/todos/${id}/`);
    return response.data;
  },

  async createTodo(data: CreateTodoData): Promise<TodoItem> {
    const response = await api.post<TodoItem>('/todo/todos/', data);
    return response.data;
  },

  async updateTodo(id: number, data: UpdateTodoData): Promise<TodoItem> {
    const response = await api.put<TodoItem>(`/todo/todos/${id}/`, data);
    return response.data;
  },

  async deleteTodo(id: number): Promise<void> {
    await api.delete(`/todo/todos/${id}/`);
  },

  async markCompleted(id: number): Promise<TodoItem> {
    const response = await api.post<TodoItem>(`/todo/todos/${id}/mark_completed/`);
    return response.data;
  },

  async setCurrentFocus(id: number): Promise<TodoItem> {
    // First, remove current focus from all todos
    const todos = await this.getTodos();
    const currentFocus = todos.find(todo => todo.is_current_focus);
    if (currentFocus && currentFocus.id !== id) {
      await this.updateTodo(currentFocus.id, { is_current_focus: false });
    }
    
    // Set new focus
    return await this.updateTodo(id, { is_current_focus: true });
  },

  async getCurrentFocus(): Promise<TodoItem | null> {
    const todos = await this.getTodos();
    return todos.find(todo => todo.is_current_focus) || null;
  },
};
