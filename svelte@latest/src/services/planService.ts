import { api } from './api';

// Plan types based on Django model
export type GoalType = 'short_term' | 'long_term' | 'skill_development' | 'career' | 'personal' | 'project';
export type PlanStatus = 'active' | 'achieved' | 'deferred' | 'abandoned';

export interface FuturePlan {
  id: number;
  goal_type?: GoalType | null;
  title: string;
  description: string;
  target_date?: string | null; // YYYY-MM-DD format
  status: PlanStatus;
  created_at: string;
  updated_at: string;
  user: number;
}

export interface CreatePlanData {
  goal_type?: GoalType;
  title: string;
  description: string;
  target_date?: string;
}

export interface UpdatePlanData {
  goal_type?: GoalType;
  title?: string;
  description?: string;
  target_date?: string;
  status?: PlanStatus;
}

export const planService = {
  async getPlans(): Promise<FuturePlan[]> {
    const response = await api.get<FuturePlan[]>('/plans/');
    return response.data;
  },

  async getPlan(id: number): Promise<FuturePlan> {
    const response = await api.get<FuturePlan>(`/plans/${id}/`);
    return response.data;
  },

  async createPlan(data: CreatePlanData): Promise<FuturePlan> {
    const response = await api.post<FuturePlan>('/plans/', data);
    return response.data;
  },

  async updatePlan(id: number, data: UpdatePlanData): Promise<FuturePlan> {
    const response = await api.put<FuturePlan>(`/plans/${id}/`, data);
    return response.data;
  },

  async deletePlan(id: number): Promise<void> {
    await api.delete(`/plans/${id}/`);
  },
};
