import { api } from './api';

// Achievement types based on Django model
export interface Achievement {
  id: number;
  title: string;
  description?: string | null;
  quantifiable_results?: string | null;
  core_skills_json: string[];
  date_achieved?: string | null; // YYYY-MM-DD format
  created_at: string;
  updated_at: string;
  user: number;
}

export interface CreateAchievementData {
  title: string;
  description?: string;
  quantifiable_results?: string;
  core_skills_json?: string[];
  date_achieved?: string;
}

export interface UpdateAchievementData {
  title?: string;
  description?: string;
  quantifiable_results?: string;
  core_skills_json?: string[];
  date_achieved?: string;
}

export const achievementService = {
  async getAchievements(): Promise<Achievement[]> {
    const response = await api.get<Achievement[]>('/achievements/');
    return response.data;
  },

  async getAchievement(id: number): Promise<Achievement> {
    const response = await api.get<Achievement>(`/achievements/${id}/`);
    return response.data;
  },

  async createAchievement(data: CreateAchievementData): Promise<Achievement> {
    const response = await api.post<Achievement>('/achievements/', data);
    return response.data;
  },

  async updateAchievement(id: number, data: UpdateAchievementData): Promise<Achievement> {
    const response = await api.put<Achievement>(`/achievements/${id}/`, data);
    return response.data;
  },

  async deleteAchievement(id: number): Promise<void> {
    await api.delete(`/achievements/${id}/`);
  },
};
