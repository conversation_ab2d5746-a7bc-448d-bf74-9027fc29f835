import { createBrowserRouter, Navigate } from 'react-router-dom';
import { MainLayout } from './components/layout/MainLayout';
import { LoginPage } from './pages/LoginPage';
import { RegisterPage } from './pages/RegisterPage';
import { AchievementPage } from './pages/AchievementPage';
import { TodoPage } from './pages/TodoPage';
import { PlanPage } from './pages/PlanPage';
import { AnchorPage } from './pages/AnchorPage';

export const router = createBrowserRouter([
  {
    path: '/',
    element: <Navigate to="/todo" replace />,
  },
  {
    path: '/login',
    element: <LoginPage />,
  },
  {
    path: '/register',
    element: <RegisterPage />,
  },
  {
    path: '/achievement',
    element: (
      <MainLayout>
        <AchievementPage />
      </MainLayout>
    ),
  },
  {
    path: '/todo',
    element: (
      <MainLayout>
        <TodoPage />
      </MainLayout>
    ),
  },
  {
    path: '/plan',
    element: (
      <MainLayout>
        <PlanPage />
      </MainLayout>
    ),
  },
  {
    path: '/anchor',
    element: (
      <MainLayout>
        <AnchorPage />
      </MainLayout>
    ),
  },
]);
