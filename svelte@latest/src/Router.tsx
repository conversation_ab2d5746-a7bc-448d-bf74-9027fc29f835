import { createBrowserRouter, Navigate } from 'react-router-dom';
import { MainLayout } from './components/layout/MainLayout';
import { LoginPage } from './pages/LoginPage';
import { RegisterPage } from './pages/RegisterPage';
import { AchievementPage } from './pages/AchievementPage';
import { TodoPage } from './pages/TodoPage';
import { PlanPage } from './pages/PlanPage';
import { AnchorPage } from './pages/AnchorPage';

// Placeholder components for new routes
const ProfilePage = () => (
  <div className="p-6">
    <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">Profile</h1>
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <p className="text-gray-600 dark:text-gray-400">
        Profile page - Coming soon!
      </p>
    </div>
  </div>
);

const BlogPage = () => (
  <div className="p-6">
    <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">Blog</h1>
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <p className="text-gray-600 dark:text-gray-400">
        Blog page - Coming soon!
      </p>
    </div>
  </div>
);

export const router = createBrowserRouter([
  {
    path: '/',
    element: <Navigate to="/todo" replace />,
  },
  {
    path: '/login',
    element: <LoginPage />,
  },
  {
    path: '/register',
    element: <RegisterPage />,
  },
  // Task Arrange routes
  {
    path: '/achievements',
    element: (
      <MainLayout>
        <AchievementPage />
      </MainLayout>
    ),
  },
  {
    path: '/todo',
    element: (
      <MainLayout>
        <TodoPage />
      </MainLayout>
    ),
  },
  {
    path: '/plans',
    element: (
      <MainLayout>
        <PlanPage />
      </MainLayout>
    ),
  },
  // Other routes
  {
    path: '/profile',
    element: (
      <MainLayout>
        <ProfilePage />
      </MainLayout>
    ),
  },
  {
    path: '/blog',
    element: (
      <MainLayout>
        <BlogPage />
      </MainLayout>
    ),
  },
  // Legacy routes for backward compatibility
  {
    path: '/achievement',
    element: <Navigate to="/achievements" replace />,
  },
  {
    path: '/plan',
    element: <Navigate to="/plans" replace />,
  },
  {
    path: '/anchor',
    element: <Navigate to="/profile" replace />,
  },
]);
