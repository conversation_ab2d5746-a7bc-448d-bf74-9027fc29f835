import { create } from 'zustand';
import { achievementService, type Achievement, type CreateAchievementData, type UpdateAchievementData } from '../services/achievementService';

interface AchievementState {
  achievements: Achievement[];
  selectedAchievement: Achievement | null;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  fetchAchievements: () => Promise<void>;
  selectAchievement: (achievement: Achievement | null) => void;
  addAchievement: (data: CreateAchievementData) => Promise<void>;
  updateAchievement: (id: number, data: UpdateAchievementData) => Promise<void>;
  deleteAchievement: (id: number) => Promise<void>;
}

export const useAchievementStore = create<AchievementState>((set, get) => ({
  achievements: [],
  selectedAchievement: null,
  isLoading: false,
  error: null,

  fetchAchievements: async () => {
    set({ isLoading: true, error: null });
    try {
      const achievements = await achievementService.getAchievements();
      set({ achievements, isLoading: false });
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to fetch achievements', isLoading: false });
    }
  },

  selectAchievement: (achievement: Achievement | null) => {
    set({ selectedAchievement: achievement });
  },

  addAchievement: async (data: CreateAchievementData) => {
    set({ isLoading: true, error: null });
    try {
      const newAchievement = await achievementService.createAchievement(data);
      set((state) => ({ 
        achievements: [...state.achievements, newAchievement], 
        isLoading: false 
      }));
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to create achievement', isLoading: false });
    }
  },

  updateAchievement: async (id: number, data: UpdateAchievementData) => {
    set({ isLoading: true, error: null });
    try {
      const updatedAchievement = await achievementService.updateAchievement(id, data);
      set((state) => ({
        achievements: state.achievements.map(achievement => 
          achievement.id === id ? updatedAchievement : achievement
        ),
        selectedAchievement: state.selectedAchievement?.id === id ? updatedAchievement : state.selectedAchievement,
        isLoading: false
      }));
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to update achievement', isLoading: false });
    }
  },

  deleteAchievement: async (id: number) => {
    set({ isLoading: true, error: null });
    try {
      await achievementService.deleteAchievement(id);
      set((state) => ({
        achievements: state.achievements.filter(achievement => achievement.id !== id),
        selectedAchievement: state.selectedAchievement?.id === id ? null : state.selectedAchievement,
        isLoading: false
      }));
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to delete achievement', isLoading: false });
    }
  },
}));
