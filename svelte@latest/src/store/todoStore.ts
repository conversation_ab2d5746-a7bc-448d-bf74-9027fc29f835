import { create } from 'zustand';
import { todoService, type TodoItem, type CreateTodoData, type UpdateTodoData } from '../services/todoService';

interface TodoState {
  todos: TodoItem[];
  currentFocus: TodoItem | null;
  isLoading: boolean;
  error: string | null;

  // Actions
  fetchTodos: () => Promise<void>;
  addTodo: (data: CreateTodoData) => Promise<void>;
  updateTodo: (id: number, data: UpdateTodoData) => Promise<void>;
  deleteTodo: (id: number) => Promise<void>;
  setCurrentFocus: (id: number) => Promise<void>;
  markCompleted: (id: number) => Promise<void>;
  clearCompleted: () => Promise<void>;
}

export const useTodoStore = create<TodoState>((set, get) => ({
  todos: [],
  currentFocus: null,
  isLoading: false,
  error: null,

  fetchTodos: async () => {
    set({ isLoading: true, error: null });
    try {
      const todos = await todoService.getTodos();
      const currentFocus = todos.find(todo => todo.is_current_focus) || null;
      set({ todos, currentFocus, isLoading: false });
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to fetch todos', isLoading: false });
    }
  },

  addTodo: async (data: CreateTodoData) => {
    set({ isLoading: true, error: null });
    try {
      const newTodo = await todoService.createTodo(data);
      set((state) => ({
        todos: [...state.todos, newTodo],
        isLoading: false
      }));
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to create todo', isLoading: false });
    }
  },

  updateTodo: async (id: number, data: UpdateTodoData) => {
    set({ isLoading: true, error: null });
    try {
      const updatedTodo = await todoService.updateTodo(id, data);
      set((state) => ({
        todos: state.todos.map(todo => todo.id === id ? updatedTodo : todo),
        currentFocus: updatedTodo.is_current_focus ? updatedTodo :
                     (state.currentFocus?.id === id ? null : state.currentFocus),
        isLoading: false
      }));
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to update todo', isLoading: false });
    }
  },

  deleteTodo: async (id: number) => {
    set({ isLoading: true, error: null });
    try {
      await todoService.deleteTodo(id);
      set((state) => ({
        todos: state.todos.filter(todo => todo.id !== id),
        currentFocus: state.currentFocus?.id === id ? null : state.currentFocus,
        isLoading: false
      }));
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to delete todo', isLoading: false });
    }
  },

  setCurrentFocus: async (id: number) => {
    set({ isLoading: true, error: null });
    try {
      const updatedTodo = await todoService.setCurrentFocus(id);
      set((state) => ({
        todos: state.todos.map(todo => ({
          ...todo,
          is_current_focus: todo.id === id
        })),
        currentFocus: updatedTodo,
        isLoading: false
      }));
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to set focus', isLoading: false });
    }
  },

  markCompleted: async (id: number) => {
    set({ isLoading: true, error: null });
    try {
      const updatedTodo = await todoService.markCompleted(id);
      set((state) => ({
        todos: state.todos.map(todo => todo.id === id ? updatedTodo : todo),
        currentFocus: state.currentFocus?.id === id ? null : state.currentFocus,
        isLoading: false
      }));
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to mark completed', isLoading: false });
    }
  },

  clearCompleted: async () => {
    set({ isLoading: true, error: null });
    try {
      const { todos } = get();
      const completedTodos = todos.filter(todo => todo.status === 'completed');

      await Promise.all(completedTodos.map(todo => todoService.deleteTodo(todo.id)));

      set((state) => ({
        todos: state.todos.filter(todo => todo.status !== 'completed'),
        isLoading: false
      }));
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to clear completed', isLoading: false });
    }
  },
}));
