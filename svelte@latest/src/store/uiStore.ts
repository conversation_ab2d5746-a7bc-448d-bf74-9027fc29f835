import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

type Theme = 'light' | 'dark';
type TaskArrangePage = 'achievements' | 'todo' | 'plans';

interface UiState {
  // Theme
  theme: Theme;
  toggleTheme: () => void;
  
  // Navigation
  isSidebarOpen: boolean;
  setSidebarOpen: (open: boolean) => void;
  toggleSidebar: () => void;
  
  // Task Arrange Navigation
  currentTaskPage: TaskArrangePage;
  setCurrentTaskPage: (page: TaskArrangePage) => void;
  
  // Background customization
  backgroundImage: string | null;
  setBackgroundImage: (image: string | null) => void;
}

export const useUiStore = create<UiState>()(
  persist(
    (set, get) => ({
      // Theme
      theme: 'light',
      toggleTheme: () => {
        const newTheme = get().theme === 'light' ? 'dark' : 'light';
        set({ theme: newTheme });
        
        // Apply theme to document
        if (newTheme === 'dark') {
          document.documentElement.classList.add('dark');
        } else {
          document.documentElement.classList.remove('dark');
        }
      },
      
      // Navigation
      isSidebarOpen: false,
      setSidebarOpen: (open: boolean) => set({ isSidebarOpen: open }),
      toggleSidebar: () => set((state) => ({ isSidebarOpen: !state.isSidebarOpen })),
      
      // Task Arrange Navigation
      currentTaskPage: 'todo',
      setCurrentTaskPage: (page: TaskArrangePage) => set({ currentTaskPage: page }),
      
      // Background customization
      backgroundImage: null,
      setBackgroundImage: (image: string | null) => set({ backgroundImage: image }),
    }),
    {
      name: 'ui-store',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        theme: state.theme,
        currentTaskPage: state.currentTaskPage,
        backgroundImage: state.backgroundImage,
      }),
    }
  )
);

// Initialize theme on app start
export const initializeTheme = () => {
  const { theme } = useUiStore.getState();
  if (theme === 'dark') {
    document.documentElement.classList.add('dark');
  } else {
    document.documentElement.classList.remove('dark');
  }
};
