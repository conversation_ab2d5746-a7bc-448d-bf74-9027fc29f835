import { create } from 'zustand';
import { planService, type FuturePlan, type CreatePlanData, type UpdatePlanData } from '../services/planService';

interface PlanState {
  plans: FuturePlan[];
  selectedPlan: FuturePlan | null;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  fetchPlans: () => Promise<void>;
  selectPlan: (plan: FuturePlan | null) => void;
  addPlan: (data: CreatePlanData) => Promise<void>;
  updatePlan: (id: number, data: UpdatePlanData) => Promise<void>;
  deletePlan: (id: number) => Promise<void>;
}

export const usePlanStore = create<PlanState>((set, get) => ({
  plans: [],
  selectedPlan: null,
  isLoading: false,
  error: null,

  fetchPlans: async () => {
    set({ isLoading: true, error: null });
    try {
      const plans = await planService.getPlans();
      set({ plans, isLoading: false });
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to fetch plans', isLoading: false });
    }
  },

  selectPlan: (plan: FuturePlan | null) => {
    set({ selectedPlan: plan });
  },

  addPlan: async (data: CreatePlanData) => {
    set({ isLoading: true, error: null });
    try {
      const newPlan = await planService.createPlan(data);
      set((state) => ({ 
        plans: [...state.plans, newPlan], 
        isLoading: false 
      }));
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to create plan', isLoading: false });
    }
  },

  updatePlan: async (id: number, data: UpdatePlanData) => {
    set({ isLoading: true, error: null });
    try {
      const updatedPlan = await planService.updatePlan(id, data);
      set((state) => ({
        plans: state.plans.map(plan => 
          plan.id === id ? updatedPlan : plan
        ),
        selectedPlan: state.selectedPlan?.id === id ? updatedPlan : state.selectedPlan,
        isLoading: false
      }));
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to update plan', isLoading: false });
    }
  },

  deletePlan: async (id: number) => {
    set({ isLoading: true, error: null });
    try {
      await planService.deletePlan(id);
      set((state) => ({
        plans: state.plans.filter(plan => plan.id !== id),
        selectedPlan: state.selectedPlan?.id === id ? null : state.selectedPlan,
        isLoading: false
      }));
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to delete plan', isLoading: false });
    }
  },
}));
