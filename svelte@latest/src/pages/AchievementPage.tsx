import React, { useEffect, useState } from 'react';
import { TaskLayout } from '../components/layout/TaskLayout';
import { useAchievementStore } from '../store/achievementStore';
import { Button } from '../components/ui/Button';
import { Modal } from '../components/ui/Modal';
import { formatDate } from '../lib/utils';
import type { Achievement, CreateAchievementData } from '../services/achievementService';

// Achievement List Item Component
interface AchievementListItemProps {
  achievement: Achievement;
  isSelected: boolean;
  onClick: () => void;
}

const AchievementListItem: React.FC<AchievementListItemProps> = ({
  achievement,
  isSelected,
  onClick,
}) => {
  return (
    <div
      onClick={onClick}
      className={`p-4 rounded-lg border cursor-pointer transition-all ${
        isSelected
          ? 'border-purple-500 bg-purple-50 dark:bg-purple-900/20 dark:border-purple-400'
          : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 bg-white dark:bg-gray-800'
      }`}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          <h3 className="font-medium text-gray-900 dark:text-white truncate">
            {achievement.title}
          </h3>
          {achievement.date_achieved && (
            <div className="flex items-center mt-1">
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                Achieved on {formatDate(achievement.date_achieved)}
              </span>
            </div>
          )}
          {achievement.core_skills_json && achievement.core_skills_json.length > 0 && (
            <div className="mt-2">
              <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Core Skills:</p>
              <div className="flex flex-wrap gap-1 mt-1">
                {achievement.core_skills_json.slice(0, 3).map((skill, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"
                  >
                    {skill}
                  </span>
                ))}
                {achievement.core_skills_json.length > 3 && (
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    +{achievement.core_skills_json.length - 3} more
                  </span>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Achievement Details Component
interface AchievementDetailsProps {
  achievement: Achievement;
  onEdit: () => void;
  onDelete: () => void;
}

const AchievementDetails: React.FC<AchievementDetailsProps> = ({
  achievement,
  onEdit,
  onDelete,
}) => {
  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {achievement.title}
            </h1>
            {achievement.date_achieved && (
              <div className="mt-2">
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                  Achieved on {formatDate(achievement.date_achieved)}
                </span>
              </div>
            )}
          </div>
          <div className="flex space-x-2 ml-4">
            <Button onClick={onEdit} variant="secondary" size="sm">
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
              Edit
            </Button>
            <Button onClick={onDelete} variant="danger" size="sm">
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
              Delete
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-6 space-y-6">
        {/* Core Skills */}
        {achievement.core_skills_json && achievement.core_skills_json.length > 0 && (
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
              Core Skills
            </h3>
            <div className="flex flex-wrap gap-2">
              {achievement.core_skills_json.map((skill, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                >
                  {skill}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Description */}
        {achievement.description && (
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
              Description
            </h3>
            <div className="prose dark:prose-invert max-w-none">
              <p className="text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                {achievement.description}
              </p>
            </div>
          </div>
        )}

        {/* Quantifiable Results */}
        {achievement.quantifiable_results && (
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
              Quantifiable Results
            </h3>
            <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
              <p className="text-green-800 dark:text-green-200 whitespace-pre-wrap">
                {achievement.quantifiable_results}
              </p>
            </div>
          </div>
        )}

        {/* Metadata */}
        <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
            Details
          </h3>
          <dl className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div>
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Created</dt>
              <dd className="text-sm text-gray-900 dark:text-white">
                {formatDate(achievement.created_at)}
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Last Updated</dt>
              <dd className="text-sm text-gray-900 dark:text-white">
                {formatDate(achievement.updated_at)}
              </dd>
            </div>
          </dl>
        </div>
      </div>
    </div>
  );
};

export const AchievementPage: React.FC = () => {
  const {
    achievements,
    selectedAchievement,
    isLoading,
    error,
    fetchAchievements,
    selectAchievement,
    addAchievement,
    updateAchievement,
    deleteAchievement,
  } = useAchievementStore();

  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  useEffect(() => {
    fetchAchievements();
  }, [fetchAchievements]);

  const handleAddAchievement = () => {
    setIsAddModalOpen(true);
  };

  const handleEditAchievement = () => {
    if (selectedAchievement) {
      setIsEditModalOpen(true);
    }
  };

  const handleDeleteAchievement = async () => {
    if (selectedAchievement && window.confirm('Are you sure you want to delete this achievement?')) {
      await deleteAchievement(selectedAchievement.id);
      selectAchievement(null);
    }
  };

  // Left Panel - Achievement List
  const leftPanel = (
    <div className="space-y-2 p-4">
      {achievements.length === 0 ? (
        <div className="text-center py-8 text-gray-500 dark:text-gray-400">
          <p>No achievements yet.</p>
          <p className="text-sm">Add your first achievement to get started!</p>
        </div>
      ) : (
        achievements.map((achievement) => (
          <AchievementListItem
            key={achievement.id}
            achievement={achievement}
            isSelected={selectedAchievement?.id === achievement.id}
            onClick={() => selectAchievement(achievement)}
          />
        ))
      )}
    </div>
  );

  // Right Panel - Achievement Details
  const rightPanel = selectedAchievement ? (
    <AchievementDetails
      achievement={selectedAchievement}
      onEdit={handleEditAchievement}
      onDelete={handleDeleteAchievement}
    />
  ) : (
    <div className="flex items-center justify-center h-full text-gray-500 dark:text-gray-400">
      <div className="text-center">
        <svg className="w-16 h-16 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
        </svg>
        <p className="text-lg font-medium">Select an achievement</p>
        <p className="text-sm">Choose an achievement from the list to view details</p>
      </div>
    </div>
  );

  return (
    <>
      <TaskLayout
        title="Achievements"
        icon="🏆"
        leftPanel={leftPanel}
        rightPanel={rightPanel}
        isLoading={isLoading}
        error={error}
        onAddNew={handleAddAchievement}
        addButtonText="Add Achievement"
      />

      {/* Add Achievement Modal */}
      <AchievementModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onSubmit={async (data) => {
          await addAchievement(data);
          setIsAddModalOpen(false);
        }}
        title="Add New Achievement"
      />

      {/* Edit Achievement Modal */}
      {selectedAchievement && (
        <AchievementModal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          onSubmit={async (data) => {
            await updateAchievement(selectedAchievement.id, data);
            setIsEditModalOpen(false);
          }}
          title="Edit Achievement"
          initialData={selectedAchievement}
        />
      )}
    </>
  );
};

// Achievement Modal Component
interface AchievementModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: CreateAchievementData) => Promise<void>;
  title: string;
  initialData?: Achievement;
}

const AchievementModal: React.FC<AchievementModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  title,
  initialData,
}) => {
  const [formData, setFormData] = useState<CreateAchievementData>({
    title: initialData?.title || '',
    description: initialData?.description || '',
    quantifiable_results: initialData?.quantifiable_results || '',
    core_skills_json: initialData?.core_skills_json || [],
    date_achieved: initialData?.date_achieved || '',
  });
  const [skillInput, setSkillInput] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.title.trim()) return;

    setIsSubmitting(true);
    try {
      await onSubmit(formData);
      setFormData({
        title: '',
        description: '',
        quantifiable_results: '',
        core_skills_json: [],
        date_achieved: '',
      });
      setSkillInput('');
    } catch (error) {
      console.error('Failed to save achievement:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const addSkill = () => {
    if (skillInput.trim() && !formData.core_skills_json?.includes(skillInput.trim())) {
      setFormData(prev => ({
        ...prev,
        core_skills_json: [...(prev.core_skills_json || []), skillInput.trim()]
      }));
      setSkillInput('');
    }
  };

  const removeSkill = (skillToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      core_skills_json: prev.core_skills_json?.filter(skill => skill !== skillToRemove) || []
    }));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addSkill();
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title={title} size="lg">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Title */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Title *
          </label>
          <input
            type="text"
            value={formData.title}
            onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white"
            placeholder="Enter achievement title"
            required
          />
        </div>

        {/* Date Achieved */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Date Achieved
          </label>
          <input
            type="date"
            value={formData.date_achieved}
            onChange={(e) => setFormData(prev => ({ ...prev, date_achieved: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white"
          />
        </div>

        {/* Core Skills */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Core Skills
          </label>
          <div className="flex space-x-2 mb-2">
            <input
              type="text"
              value={skillInput}
              onChange={(e) => setSkillInput(e.target.value)}
              onKeyPress={handleKeyPress}
              className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white"
              placeholder="Add a skill"
            />
            <Button type="button" onClick={addSkill} size="sm">
              Add
            </Button>
          </div>
          {formData.core_skills_json && formData.core_skills_json.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {formData.core_skills_json.map((skill, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2 py-1 rounded-full text-sm bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                >
                  {skill}
                  <button
                    type="button"
                    onClick={() => removeSkill(skill)}
                    className="ml-1 text-blue-600 hover:text-blue-800 dark:text-blue-300 dark:hover:text-blue-100"
                  >
                    ×
                  </button>
                </span>
              ))}
            </div>
          )}
        </div>

        {/* Description */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Description
          </label>
          <textarea
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white"
            placeholder="Describe your achievement"
          />
        </div>

        {/* Quantifiable Results */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Quantifiable Results
          </label>
          <textarea
            value={formData.quantifiable_results}
            onChange={(e) => setFormData(prev => ({ ...prev, quantifiable_results: e.target.value }))}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white"
            placeholder="Measurable outcomes and results"
          />
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
          <Button type="button" onClick={onClose} variant="secondary">
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting || !formData.title.trim()}>
            {isSubmitting ? 'Saving...' : 'Save Achievement'}
          </Button>
        </div>
      </form>
    </Modal>
  );
};
