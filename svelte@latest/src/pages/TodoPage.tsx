import React, { useState } from 'react';
import { Button } from '../components/ui/Button';
import { useTodoStore } from '../store/todoStore';

export const TodoPage: React.FC = () => {
  const { todos, addTodo, toggleTodo, deleteTodo, clearCompleted } = useTodoStore();
  const [newTodo, setNewTodo] = useState('');

  const handleAddTodo = () => {
    if (newTodo.trim()) {
      addTodo(newTodo);
      setNewTodo('');
    }
  };

  return (
    <div>
      <h1 className="text-3xl font-bold text-gray-900 mb-6">Todo List</h1>
      
      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <div className="flex gap-2 mb-4">
          <input
            type="text"
            value={newTodo}
            onChange={(e) => setNewTodo(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleAddTodo()}
            placeholder="Add a new task..."
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          <Button onClick={handleAddTodo}>Add Task</Button>
        </div>

        {todos.length > 0 && (
          <div className="flex justify-between items-center mb-4">
            <span className="text-sm text-gray-600">
              {todos.filter(t => !t.completed).length} of {todos.length} tasks remaining
            </span>
            <Button variant="secondary" size="sm" onClick={clearCompleted}>
              Clear Completed
            </Button>
          </div>
        )}

        <div className="space-y-2">
          {todos.map((todo) => (
            <div key={todo.id} className="flex items-center gap-3 p-3 border border-gray-200 rounded-md">
              <input
                type="checkbox"
                checked={todo.completed}
                onChange={() => toggleTodo(todo.id)}
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <span className={`flex-1 ${todo.completed ? 'line-through text-gray-500' : 'text-gray-900'}`}>
                {todo.text}
              </span>
              <Button
                variant="danger"
                size="sm"
                onClick={() => deleteTodo(todo.id)}
              >
                Delete
              </Button>
            </div>
          ))}
          {todos.length === 0 && (
            <p className="text-gray-500 text-center py-8">No tasks yet. Add one above!</p>
          )}
        </div>
      </div>
    </div>
  );
};
