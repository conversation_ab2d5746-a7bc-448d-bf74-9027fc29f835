import React, { useEffect, useState } from 'react';
import { TaskLayout } from '../components/layout/TaskLayout';
import { useTodoStore } from '../store/todoStore';
import { Button } from '../components/ui/Button';
import { TodoModal } from '../components/todo/TodoModal';
import { formatDate } from '../lib/utils';
import type { TodoItem, CreateTodoData, UpdateTodoData, TodoStatus, TodoPriority } from '../services/todoService';

// Priority colors
const getPriorityColor = (priority: TodoPriority) => {
  switch (priority) {
    case 'high':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
    case 'medium':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
    case 'low':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
  }
};

// Status colors
const getStatusColor = (status: TodoStatus) => {
  switch (status) {
    case 'completed':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
    case 'in_progress':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
    case 'deferred':
      return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
    default:
      return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
  }
};

// Todo List Item Component
interface TodoListItemProps {
  todo: TodoItem;
  onEdit: () => void;
  onDelete: () => void;
  onToggleStatus: () => void;
  onSetFocus: () => void;
  isFocused: boolean;
  isCompleted?: boolean;
}

const TodoListItem: React.FC<TodoListItemProps> = ({
  todo,
  onEdit,
  onDelete,
  onToggleStatus,
  onSetFocus,
  isFocused,
  isCompleted = false,
}) => {
  return (
    <div
      className={`p-3 rounded-lg border transition-all ${
        isFocused
          ? 'border-yellow-400 bg-yellow-50 dark:bg-yellow-900/20 dark:border-yellow-500'
          : isCompleted
          ? 'border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50'
          : 'border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 hover:border-gray-300 dark:hover:border-gray-600'
      }`}
    >
      <div className="flex items-start space-x-3">
        {/* Checkbox */}
        <button
          onClick={onToggleStatus}
          className={`mt-1 w-4 h-4 rounded border-2 flex items-center justify-center transition-colors ${
            todo.status === 'completed'
              ? 'bg-green-500 border-green-500 text-white'
              : 'border-gray-300 dark:border-gray-600 hover:border-green-400'
          }`}
        >
          {todo.status === 'completed' && (
            <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          )}
        </button>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h3 className={`font-medium ${
                todo.status === 'completed'
                  ? 'line-through text-gray-500 dark:text-gray-400'
                  : 'text-gray-900 dark:text-white'
              }`}>
                {todo.title}
              </h3>

              {todo.description && (
                <p className={`text-sm mt-1 ${
                  todo.status === 'completed'
                    ? 'line-through text-gray-400 dark:text-gray-500'
                    : 'text-gray-600 dark:text-gray-400'
                }`}>
                  {todo.description}
                </p>
              )}

              {/* Tags */}
              <div className="flex flex-wrap gap-1 mt-2">
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(todo.priority)}`}>
                  {todo.priority} priority
                </span>

                {todo.due_date && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                    Due {formatDate(todo.due_date)}
                  </span>
                )}
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center space-x-1 ml-2">
              {!isCompleted && (
                <button
                  onClick={onSetFocus}
                  className={`p-1 rounded transition-colors ${
                    isFocused
                      ? 'text-yellow-600 hover:text-yellow-700'
                      : 'text-gray-400 hover:text-yellow-500'
                  }`}
                  title={isFocused ? 'Remove from focus' : 'Set as main focus'}
                >
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                  </svg>
                </button>
              )}

              <button
                onClick={onEdit}
                className="p-1 text-gray-400 hover:text-blue-500 transition-colors"
                title="Edit task"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              </button>

              <button
                onClick={onDelete}
                className="p-1 text-gray-400 hover:text-red-500 transition-colors"
                title="Delete task"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export const TodoPage: React.FC = () => {
  const {
    todos,
    currentFocus,
    isLoading,
    error,
    fetchTodos,
    addTodo,
    updateTodo,
    deleteTodo,
    setCurrentFocus,
    markCompleted,
    clearCompleted,
  } = useTodoStore();

  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [editingTodo, setEditingTodo] = useState<TodoItem | null>(null);

  useEffect(() => {
    fetchTodos();
  }, [fetchTodos]);

  const handleAddTodo = () => {
    setIsAddModalOpen(true);
  };

  const handleEditTodo = (todo: TodoItem) => {
    setEditingTodo(todo);
  };

  const handleDeleteTodo = async (todo: TodoItem) => {
    if (window.confirm('Are you sure you want to delete this task?')) {
      await deleteTodo(todo.id);
    }
  };

  const handleToggleStatus = async (todo: TodoItem) => {
    const newStatus: TodoStatus = todo.status === 'completed' ? 'pending' : 'completed';
    await updateTodo(todo.id, { status: newStatus });
  };

  const handleSetFocus = async (todo: TodoItem) => {
    if (todo.status !== 'completed') {
      await setCurrentFocus(todo.id);
    }
  };

  // Filter todos by status
  const activeTodos = todos.filter(todo => todo.status !== 'completed');
  const completedTodos = todos.filter(todo => todo.status === 'completed');

  // Left Panel - Todo List
  const leftPanel = (
    <div className="h-full flex flex-col">
      {/* Active Todos */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-4 space-y-2">
          {activeTodos.length === 0 ? (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <p>No active tasks.</p>
              <p className="text-sm">Add your first task to get started!</p>
            </div>
          ) : (
            activeTodos.map((todo) => (
              <TodoListItem
                key={todo.id}
                todo={todo}
                onEdit={() => handleEditTodo(todo)}
                onDelete={() => handleDeleteTodo(todo)}
                onToggleStatus={() => handleToggleStatus(todo)}
                onSetFocus={() => handleSetFocus(todo)}
                isFocused={currentFocus?.id === todo.id}
              />
            ))
          )}
        </div>
      </div>

      {/* Completed Section */}
      {completedTodos.length > 0 && (
        <div className="border-t border-gray-200 dark:border-gray-700">
          <div className="p-4">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                PAST ({completedTodos.length})
              </h3>
              <Button
                variant="secondary"
                size="sm"
                onClick={clearCompleted}
                className="text-xs"
              >
                Clear Completed
              </Button>
            </div>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {completedTodos.slice(0, 5).map((todo) => (
                <TodoListItem
                  key={todo.id}
                  todo={todo}
                  onEdit={() => handleEditTodo(todo)}
                  onDelete={() => handleDeleteTodo(todo)}
                  onToggleStatus={() => handleToggleStatus(todo)}
                  onSetFocus={() => {}}
                  isFocused={false}
                  isCompleted
                />
              ))}
              {completedTodos.length > 5 && (
                <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
                  +{completedTodos.length - 5} more completed tasks
                </p>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );

  // Right Panel - Main Focus
  const rightPanel = currentFocus ? (
    <MainFocusPanel
      todo={currentFocus}
      onEdit={() => handleEditTodo(currentFocus)}
      onComplete={() => handleToggleStatus(currentFocus)}
    />
  ) : (
    <div className="flex items-center justify-center h-full text-gray-500 dark:text-gray-400">
      <div className="text-center">
        <svg className="w-16 h-16 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
        </svg>
        <p className="text-lg font-medium">Main Focus:</p>
        <p className="text-sm mb-4">No task is currently in focus</p>
        <p className="text-xs">Click the star icon on any task to set it as your main focus</p>
      </div>
    </div>
  );

  return (
    <>
      <TaskLayout
        title="Todo"
        icon="✅"
        leftPanel={leftPanel}
        rightPanel={rightPanel}
        isLoading={isLoading}
        error={error}
        onAddNew={handleAddTodo}
        addButtonText="Add Task"
      />

      {/* Add Todo Modal */}
      <TodoModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onSubmit={async (data) => {
          await addTodo(data);
          setIsAddModalOpen(false);
        }}
        title="Add New Task"
      />

      {/* Edit Todo Modal */}
      {editingTodo && (
        <TodoModal
          isOpen={!!editingTodo}
          onClose={() => setEditingTodo(null)}
          onSubmit={async (data) => {
            await updateTodo(editingTodo.id, data);
            setEditingTodo(null);
          }}
          title="Edit Task"
          initialData={editingTodo}
        />
      )}
    </>
  );
};

// Main Focus Panel Component
interface MainFocusPanelProps {
  todo: TodoItem;
  onEdit: () => void;
  onComplete: () => void;
}

const MainFocusPanel: React.FC<MainFocusPanelProps> = ({
  todo,
  onEdit,
  onComplete,
}) => {
  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20">
        <div className="flex items-center space-x-3 mb-4">
          <svg className="w-8 h-8 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
          </svg>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Main Focus:</h1>
        </div>

        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              {todo.title}
            </h2>

            {todo.description && (
              <p className="text-gray-700 dark:text-gray-300 mb-3">
                {todo.description}
              </p>
            )}

            {/* Tags */}
            <div className="flex flex-wrap gap-2">
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getPriorityColor(todo.priority)}`}>
                {todo.priority} priority
              </span>

              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(todo.status)}`}>
                {todo.status.replace('_', ' ')}
              </span>

              {todo.due_date && (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                  Due {formatDate(todo.due_date)}
                </span>
              )}
            </div>
          </div>

          <div className="flex space-x-2 ml-4">
            <Button onClick={onEdit} variant="secondary" size="sm">
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
              Edit
            </Button>
            <Button onClick={onComplete} className="bg-green-600 hover:bg-green-700">
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              Complete
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 p-6 overflow-y-auto">
        <div className="space-y-6">
          {/* Progress Section */}
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
              Focus Session
            </h3>
            <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
              This task is currently your main focus. Complete it to move forward with your goals.
            </p>

            {/* Motivational message */}
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
              <p className="text-blue-800 dark:text-blue-200 text-sm">
                💪 You've got this! Focus on one task at a time for maximum productivity.
              </p>
            </div>
          </div>

          {/* Task Details */}
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
              Task Details
            </h3>
            <dl className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div>
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Created</dt>
                <dd className="text-sm text-gray-900 dark:text-white">
                  {formatDate(todo.created_at)}
                </dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Last Updated</dt>
                <dd className="text-sm text-gray-900 dark:text-white">
                  {formatDate(todo.updated_at)}
                </dd>
              </div>
              {todo.due_date && (
                <div>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Due Date</dt>
                  <dd className="text-sm text-gray-900 dark:text-white">
                    {formatDate(todo.due_date)}
                  </dd>
                </div>
              )}
              <div>
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Priority</dt>
                <dd className="text-sm text-gray-900 dark:text-white capitalize">
                  {todo.priority}
                </dd>
              </div>
            </dl>
          </div>
        </div>
      </div>
    </div>
  );
};
