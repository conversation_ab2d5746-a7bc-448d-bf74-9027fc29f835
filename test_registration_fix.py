#!/usr/bin/env python3
"""
Test script to verify the registration API fix
"""
import requests
import json

def test_registration():
    """Test the registration endpoint with proper data"""
    url = "http://localhost:8000/auth/register/"
    
    # Test data with all required fields
    test_data = {
        "username": "testuser123",
        "email": "<EMAIL>", 
        "password": "TestPass123",
        "password_confirm": "TestPass123"
    }
    
    print("Testing registration with data:")
    print(json.dumps(test_data, indent=2))
    
    try:
        response = requests.post(url, json=test_data, headers={
            'Content-Type': 'application/json'
        })
        
        print(f"\nResponse Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        try:
            response_data = response.json()
            print(f"Response Data: {json.dumps(response_data, indent=2)}")
        except:
            print(f"Response Text: {response.text}")
            
        if response.status_code == 201:
            print("✅ Registration successful!")
            return True
        else:
            print("❌ Registration failed!")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to Django server. Make sure it's running on localhost:8000")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_login():
    """Test login with the registered user"""
    url = "http://localhost:8000/auth/login/"
    
    test_data = {
        "email": "<EMAIL>",
        "password": "TestPass123"
    }
    
    print("\n" + "="*50)
    print("Testing login with data:")
    print(json.dumps(test_data, indent=2))
    
    try:
        response = requests.post(url, json=test_data, headers={
            'Content-Type': 'application/json'
        })
        
        print(f"\nResponse Status: {response.status_code}")
        
        try:
            response_data = response.json()
            print(f"Response Data: {json.dumps(response_data, indent=2)}")
        except:
            print(f"Response Text: {response.text}")
            
        if response.status_code == 200:
            print("✅ Login successful!")
            return True
        else:
            print("❌ Login failed!")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Registration and Login API")
    print("="*50)
    
    # Test registration
    registration_success = test_registration()
    
    # Test login if registration was successful
    if registration_success:
        login_success = test_login()
        
        if login_success:
            print("\n🎉 All tests passed! Registration and login are working correctly.")
        else:
            print("\n⚠️  Registration works but login failed.")
    else:
        print("\n❌ Registration failed, skipping login test.")
