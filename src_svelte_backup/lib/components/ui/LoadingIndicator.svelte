<script lang="ts">
  import { isLoading } from '$lib/store/errorStore';
  
  export let show: boolean = false;
  export let message: string = 'Loading...';
  export let overlay: boolean = false;
  export let size: 'sm' | 'md' | 'lg' = 'md';
  
  // Use global loading state if show is not explicitly set
  $: showLoading = show || $isLoading;
  
  // Size classes
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6', 
    lg: 'w-8 h-8'
  };
  
  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  };
</script>

{#if showLoading}
  {#if overlay}
    <!-- Full overlay loading -->
    <div class="fixed inset-0 bg-black bg-opacity-30 z-50 flex items-center justify-center">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 flex items-center space-x-3">
        <div class="animate-spin rounded-full border-2 border-gray-300 border-t-blue-600 {sizeClasses[size]}"></div>
        <span class="text-gray-700 dark:text-gray-300 {textSizeClasses[size]}">{message}</span>
      </div>
    </div>
  {:else}
    <!-- Inline loading -->
    <div class="flex items-center space-x-2">
      <div class="animate-spin rounded-full border-2 border-gray-300 border-t-blue-600 {sizeClasses[size]}"></div>
      <span class="text-gray-600 dark:text-gray-400 {textSizeClasses[size]}">{message}</span>
    </div>
  {/if}
{/if}

<style>
  /* Custom spinner animation */
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
  
  .animate-spin {
    animation: spin 1s linear infinite;
  }
</style>
