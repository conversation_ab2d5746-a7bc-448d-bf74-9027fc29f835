/**
 * Unified user type definitions
 */

// Base user information from Django User model
export interface BaseUser {
  id: number;
  username: string;
  email: string;
  first_name?: string;
  last_name?: string;
  is_active?: boolean;
  date_joined?: string;
}

// User profile information
export interface UserProfileData {
  user_id?: number;
  professional_title?: string | null;
  one_liner_bio?: string | null;
  skill?: string | null;
  summary?: string | null;
  created_at?: string;
  updated_at?: string;
}

// Complete user profile combining base user and profile data
export interface UserProfile extends BaseUser {
  profile?: UserProfileData | null;
  created_at?: string;
  updated_at?: string;
}

// Simplified user profile for auth store (backward compatibility)
export interface AuthUserProfile {
  id: number | null;
  username: string | null;
  email: string | null;
  professional_title?: string | null;
  one_liner_bio?: string | null;
  skill?: string | null;
  summary?: string | null;
  created_at?: string;
  updated_at?: string;
}

// Helper function to convert UserProfile to AuthUserProfile
export function toAuthUserProfile(user: UserProfile): AuthUserProfile {
  return {
    id: user.id,
    username: user.username,
    email: user.email,
    professional_title: user.profile?.professional_title || null,
    one_liner_bio: user.profile?.one_liner_bio || null,
    skill: user.profile?.skill || null,
    summary: user.profile?.summary || null,
    created_at: user.created_at,
    updated_at: user.updated_at,
  };
}

// Helper function to get user display name
export function getUserDisplayName(user: UserProfile | AuthUserProfile | null): string {
  if (!user) return 'Guest';

  // Check if it's a full UserProfile with first_name/last_name
  if ('first_name' in user && (user.first_name || user.last_name)) {
    return `${user.first_name || ''} ${user.last_name || ''}`.trim();
  }

  return user.username || 'Unknown User';
}

// Helper function to get user initials
export function getUserInitials(user: UserProfile | AuthUserProfile | null): string {
  if (!user) return 'G';

  // Check if it's a full UserProfile with first_name/last_name
  if ('first_name' in user && (user.first_name || user.last_name)) {
    const firstInitial = user.first_name ? user.first_name[0] : '';
    const lastInitial = user.last_name ? user.last_name[0] : '';
    return (firstInitial + lastInitial).toUpperCase();
  }

  const username = user.username || 'U';
  return username.substring(0, 2).toUpperCase();
}
