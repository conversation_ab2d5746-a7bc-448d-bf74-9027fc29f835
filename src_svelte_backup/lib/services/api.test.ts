import { describe, it, expect, beforeEach, vi, type MockedFunction } from 'vitest';
import { api } from './api';
import { authStore } from '../store/authStore';
import { mockFetchResponse, mockFetchError } from '../../test/utils';

// Mock authStore
const mockAuthState = {
  user: null,
  accessToken: null,
  refreshToken: null,
};

vi.mock('../store/authStore', () => {
  const mockStore = {
    subscribe: vi.fn((callback) => {
      callback(mockAuthState);
      return vi.fn(); // unsubscribe function
    }),
    set: vi.fn(),
    update: vi.fn(),
    login: vi.fn(),
    logout: vi.fn(),
    setUserProfile: vi.fn(),
    setTokens: vi.fn(),
  };

  return {
    authStore: mockStore,
    isAuthenticated: {
      subscribe: vi.fn(),
    },
  };
});

// Mock svelte/store get function
vi.mock('svelte/store', async () => {
  const actual = await vi.importActual('svelte/store');
  return {
    ...actual,
    get: vi.fn(() => mockAuthState),
  };
});

describe('api service', () => {
  const mockAuthStore = authStore as {
    getAccessToken: MockedFunction<any>;
    logout: MockedFunction<any>;
    subscribe: MockedFunction<any>;
  };

  beforeEach(() => {
    vi.clearAllMocks();
    // Reset fetch mock
    (global.fetch as MockedFunction<typeof fetch>).mockClear();
    // Reset mock auth state
    mockAuthState.user = null;
    mockAuthState.accessToken = null;
    mockAuthState.refreshToken = null;
  });

  describe('get method', () => {
    it('should make GET request with correct headers', async () => {
      const testData = { message: 'success' };
      const accessToken = 'test-access-token';

      mockAuthState.accessToken = accessToken;
      mockFetchResponse(testData);

      const result = await api.get('/test-endpoint');

      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/test-endpoint'),
        expect.objectContaining({
          method: 'GET',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            Authorization: `Token ${accessToken}`,
          }),
        })
      );
      expect(result).toEqual(testData);
    });

    it('should make request without auth header when no token', async () => {
      const testData = { message: 'success' };

      mockAuthState.accessToken = null;
      mockFetchResponse(testData);

      await api.get('/test-endpoint');

      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/test-endpoint'),
        expect.objectContaining({
          headers: expect.not.objectContaining({
            Authorization: expect.any(String),
          }),
        })
      );
    });

    it('should handle query parameters', async () => {
      const testData = { message: 'success' };

      mockAuthState.accessToken = null;
      mockFetchResponse(testData);

      await api.get('/test-endpoint', { param1: 'value1', param2: 'value2' });

      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('param1=value1&param2=value2'),
        expect.any(Object)
      );
    });
  });

  describe('post method', () => {
    it('should make POST request with correct body and headers', async () => {
      const testData = { message: 'created' };
      const requestBody = { name: 'test' };
      const accessToken = 'test-access-token';
      
      mockAuthState.accessToken = accessToken;
      mockFetchResponse(testData, 201);

      const result = await api.post('/test-endpoint', requestBody);

      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/test-endpoint'),
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            Authorization: `Bearer ${accessToken}`,
          }),
          body: JSON.stringify(requestBody),
        })
      );
      expect(result).toEqual(testData);
    });
  });

  describe('put method', () => {
    it('should make PUT request with correct body and headers', async () => {
      const testData = { message: 'updated' };
      const requestBody = { name: 'updated test' };
      
      mockAuthState.accessToken = 'token';
      mockFetchResponse(testData);

      const result = await api.put('/test-endpoint/1', requestBody);

      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/test-endpoint/1'),
        expect.objectContaining({
          method: 'PUT',
          body: JSON.stringify(requestBody),
        })
      );
      expect(result).toEqual(testData);
    });
  });

  describe('delete method', () => {
    it('should make DELETE request', async () => {
      mockAuthState.accessToken = 'token';
      mockFetchResponse(null, 204);

      await api.delete('/test-endpoint/1');

      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/test-endpoint/1'),
        expect.objectContaining({
          method: 'DELETE',
        })
      );
    });
  });

  describe('error handling', () => {
    it('should handle 401 errors and trigger logout', async () => {
      mockAuthState.accessToken = 'token';
      mockFetchError('Unauthorized', 401);

      await expect(api.get('/test-endpoint')).rejects.toThrow('Unauthorized');
      expect(mockAuthStore.logout).toHaveBeenCalled();
    });

    it('should handle 400 errors without logout', async () => {
      mockAuthState.accessToken = 'token';
      mockFetchError('Bad Request', 400);

      await expect(api.get('/test-endpoint')).rejects.toThrow('Bad Request');
      expect(mockAuthStore.logout).not.toHaveBeenCalled();
    });

    it('should handle network errors', async () => {
      mockAuthState.accessToken = 'token';
      (global.fetch as MockedFunction<typeof fetch>).mockRejectedValue(
        new Error('Network error')
      );

      await expect(api.get('/test-endpoint')).rejects.toThrow('Network error');
    });

    it('should handle non-JSON responses', async () => {
      mockAuthState.accessToken = 'token';
      (global.fetch as MockedFunction<typeof fetch>).mockResolvedValue({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        json: () => Promise.reject(new Error('Not JSON')),
        text: () => Promise.resolve('Server Error'),
      } as Response);

      await expect(api.get('/test-endpoint')).rejects.toThrow();
    });
  });

  describe('base URL handling', () => {
    it('should use correct base URL for relative paths', async () => {
      mockAuthState.accessToken = null;
      mockFetchResponse({});

      await api.get('/api/test');

      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/test'),
        expect.any(Object)
      );
    });

    it('should handle absolute URLs', async () => {
      mockAuthState.accessToken = null;
      mockFetchResponse({});

      await api.get('https://external-api.com/test');

      expect(global.fetch).toHaveBeenCalledWith(
        'https://external-api.com/test',
        expect.any(Object)
      );
    });
  });
});
