/**
 * User Store - Manages user profile data and state
 */

import { writable, derived } from 'svelte/store';
import { type UserProfile, getUserDisplayName, getUserInitials } from '$lib/types/user';

// Re-export for convenience
export { type UserProfile, getUserDisplayName, getUserInitials };

interface UserState {
  user: UserProfile | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: UserState = {
  user: null,
  isLoading: false,
  error: null,
};

// Create the store
function createUserStore() {
  const { subscribe, set, update } = writable<UserState>(initialState);

  return {
    subscribe,

    // Set user data
    setUser: (user: UserProfile) => {
      update(state => ({
        ...state,
        user,
        error: null,
        isLoading: false,
      }));
    },

    // Update user profile
    updateUser: (updates: Partial<UserProfile>) => {
      update(state => ({
        ...state,
        user: state.user ? { ...state.user, ...updates } : null,
      }));
    },

    // Update user profile data
    updateProfile: (profileUpdates: Partial<UserProfile['profile']>) => {
      update(state => ({
        ...state,
        user: state.user
          ? {
              ...state.user,
              profile: state.user.profile
                ? { ...state.user.profile, ...profileUpdates }
                : (profileUpdates as UserProfile['profile']),
            }
          : null,
      }));
    },

    // Set loading state
    setLoading: (isLoading: boolean) => {
      update(state => ({
        ...state,
        isLoading,
      }));
    },

    // Set error
    setError: (error: string | null) => {
      update(state => ({
        ...state,
        error,
        isLoading: false,
      }));
    },

    // Clear user data
    clearUser: () => {
      set(initialState);
    },

    // Reset store to initial state
    reset: () => {
      set(initialState);
    },
  };
}

export const userStore = createUserStore();

// Derived stores for easy access

export const currentUser = derived(userStore, $userStore => $userStore.user);
export const userProfile = derived(userStore, $userStore => $userStore.user?.profile);
export const isUserLoading = derived(userStore, $userStore => $userStore.isLoading);
export const userError = derived(userStore, $userStore => $userStore.error);

// Helper functions are now imported from $lib/types/user
