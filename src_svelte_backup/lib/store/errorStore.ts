/**
 * Global error state management
 */

import { writable, derived, type Writable, type Readable } from 'svelte/store';
import { type ApiError, ErrorSeverity, getDisplayMessage } from '$lib/utils/errorHandler';

// Error notification interface
export interface ErrorNotification {
  id: string;
  error: ApiError;
  message: string;
  timestamp: Date;
  dismissed: boolean;
  autoHide: boolean;
  duration: number; // in milliseconds
}

// Error store state
interface ErrorState {
  notifications: ErrorNotification[];
  globalError: ApiError | null;
  isLoading: boolean;
}

// Initial state
const initialState: ErrorState = {
  notifications: [],
  globalError: null,
  isLoading: false,
};

// Create the writable store
const { subscribe, set, update } = writable<ErrorState>(initialState);

// Generate unique ID for notifications
function generateId(): string {
  return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Get auto-hide duration based on severity
function getAutoHideDuration(severity: ErrorSeverity): number {
  switch (severity) {
    case ErrorSeverity.LOW:
      return 3000; // 3 seconds
    case ErrorSeverity.MEDIUM:
      return 5000; // 5 seconds
    case ErrorSeverity.HIGH:
      return 8000; // 8 seconds
    case ErrorSeverity.CRITICAL:
      return 0; // Don't auto-hide critical errors
    default:
      return 5000;
  }
}

// Store methods
const errorStore = {
  subscribe,
  
  /**
   * Add an error notification
   */
  addError(error: ApiError, autoHide: boolean = true): string {
    const id = generateId();
    const duration = autoHide ? getAutoHideDuration(error.severity) : 0;
    
    const notification: ErrorNotification = {
      id,
      error,
      message: getDisplayMessage(error),
      timestamp: new Date(),
      dismissed: false,
      autoHide,
      duration,
    };
    
    update(state => ({
      ...state,
      notifications: [...state.notifications, notification],
    }));
    
    // Auto-hide if duration is set
    if (duration > 0) {
      setTimeout(() => {
        this.dismissError(id);
      }, duration);
    }
    
    return id;
  },
  
  /**
   * Dismiss a specific error notification
   */
  dismissError(id: string): void {
    update(state => ({
      ...state,
      notifications: state.notifications.map(notification =>
        notification.id === id
          ? { ...notification, dismissed: true }
          : notification
      ),
    }));
    
    // Remove dismissed notifications after animation
    setTimeout(() => {
      update(state => ({
        ...state,
        notifications: state.notifications.filter(n => n.id !== id),
      }));
    }, 300); // Allow time for exit animation
  },
  
  /**
   * Clear all error notifications
   */
  clearAll(): void {
    update(state => ({
      ...state,
      notifications: [],
    }));
  },
  
  /**
   * Set global error (for critical errors that should block the UI)
   */
  setGlobalError(error: ApiError | null): void {
    update(state => ({
      ...state,
      globalError: error,
    }));
  },
  
  /**
   * Set loading state
   */
  setLoading(isLoading: boolean): void {
    update(state => ({
      ...state,
      isLoading,
    }));
  },
  
  /**
   * Handle API error with appropriate notification
   */
  handleError(error: ApiError, options: {
    showNotification?: boolean;
    setAsGlobal?: boolean;
    autoHide?: boolean;
  } = {}): string | null {
    const {
      showNotification = true,
      setAsGlobal = error.severity === ErrorSeverity.CRITICAL,
      autoHide = error.severity !== ErrorSeverity.CRITICAL,
    } = options;
    
    if (setAsGlobal) {
      this.setGlobalError(error);
    }
    
    if (showNotification) {
      return this.addError(error, autoHide);
    }
    
    return null;
  },
  
  /**
   * Retry an action and handle errors
   */
  async retryAction<T>(
    action: () => Promise<T>,
    maxRetries: number = 3,
    baseDelay: number = 1000
  ): Promise<T> {
    let lastError: ApiError;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        this.setLoading(true);
        const result = await action();
        this.setLoading(false);
        return result;
      } catch (error: any) {
        lastError = error as ApiError;
        
        if (attempt === maxRetries || !lastError.retryable) {
          this.setLoading(false);
          this.handleError(lastError);
          throw lastError;
        }
        
        // Wait before retry with exponential backoff
        const delay = Math.min(baseDelay * Math.pow(2, attempt), 10000);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    this.setLoading(false);
    throw lastError!;
  },
};

// Derived stores for convenience
export const activeNotifications: Readable<ErrorNotification[]> = derived(
  { subscribe },
  $state => $state.notifications.filter(n => !n.dismissed)
);

export const hasErrors: Readable<boolean> = derived(
  { subscribe },
  $state => $state.notifications.length > 0 || $state.globalError !== null
);

export const isLoading: Readable<boolean> = derived(
  { subscribe },
  $state => $state.isLoading
);

export const globalError: Readable<ApiError | null> = derived(
  { subscribe },
  $state => $state.globalError
);

export const criticalErrors: Readable<ErrorNotification[]> = derived(
  { subscribe },
  $state => $state.notifications.filter(n => 
    !n.dismissed && n.error.severity === ErrorSeverity.CRITICAL
  )
);

export { errorStore };
