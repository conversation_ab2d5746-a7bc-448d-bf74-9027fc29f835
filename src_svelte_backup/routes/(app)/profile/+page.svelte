<script lang="ts">
  import { onMount } from 'svelte';
  import { currentUser, getUserDisplayName, getUserInitials } from '$lib/store/userStore';
  import {
    pageContainer,
    colorSchemes,
    layouts,
    columnSpans,
    headings,
    scrollArea,
    cardBase,
    combineClasses,
  } from '$lib/styles/pageStyles';

  // Use a consistent color scheme for the profile page
  const pageStyle = colorSchemes.indigo;

  // Profile data (placeholder - would typically come from API)
  let profileData = $state({
    personalInfo: {
      firstName: '',
      lastName: '',
      email: '',
      username: '',
      joinDate: '',
      bio: '',
    },
    stats: {
      totalTasks: 0,
      completedTasks: 0,
      activePlans: 0,
      achievements: 0,
    },
    recentActivity: [
      {
        id: 1,
        type: 'task_completed',
        description: 'Completed task: Review project proposal',
        timestamp: '2024-01-15T10:30:00Z',
      },
      {
        id: 2,
        type: 'plan_created',
        description: 'Created new plan: Q1 Goals',
        timestamp: '2024-01-14T15:45:00Z',
      },
    ],
  });

  let isEditing = $state(false);
  let editForm = $state({
    firstName: '',
    lastName: '',
    bio: '',
  });

  // Load user data when component mounts
  onMount(() => {
    if ($currentUser) {
      profileData.personalInfo = {
        firstName: $currentUser.first_name || '',
        lastName: $currentUser.last_name || '',
        email: $currentUser.email || '',
        username: $currentUser.username || '',
        joinDate: $currentUser.date_joined || '',
        bio: $currentUser.profile?.summary || '',
      };

      // Initialize edit form
      editForm = {
        firstName: profileData.personalInfo.firstName,
        lastName: profileData.personalInfo.lastName,
        bio: profileData.personalInfo.bio,
      };
    }
  });

  function startEditing() {
    isEditing = true;
    editForm = {
      firstName: profileData.personalInfo.firstName,
      lastName: profileData.personalInfo.lastName,
      bio: profileData.personalInfo.bio,
    };
  }

  function cancelEditing() {
    isEditing = false;
    editForm = {
      firstName: profileData.personalInfo.firstName,
      lastName: profileData.personalInfo.lastName,
      bio: profileData.personalInfo.bio,
    };
  }

  function saveProfile() {
    // Update profile data
    profileData.personalInfo.firstName = editForm.firstName;
    profileData.personalInfo.lastName = editForm.lastName;
    profileData.personalInfo.bio = editForm.bio;

    // Here you would typically call an API to save the profile
    console.log('Saving profile:', editForm);

    isEditing = false;
  }

  function formatDate(dateString: string) {
    if (!dateString) return 'Unknown';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  }

  function formatTimestamp(timestamp: string) {
    return new Date(timestamp).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  }

  function getActivityIcon(type: string) {
    switch (type) {
      case 'task_completed':
        return '✅';
      case 'plan_created':
        return '📋';
      case 'achievement_earned':
        return '🏆';
      default:
        return '📝';
    }
  }
</script>

<svelte:head>
  <title>Profile - Personal Workspace</title>
  <meta name="description" content="View and manage your personal profile" />
</svelte:head>

<div class={combineClasses(pageContainer, 'h-[calc(100vh-180px)] flex flex-col')}>
  <!-- Page header -->
  <div class="mb-6">
    <div class="flex justify-between items-center">
      <div>
        <h1 class={combineClasses(headings.h1, pageStyle.text)}>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class={combineClasses('h-8 w-8 mr-3 inline', pageStyle.icon)}
            viewBox="0 0 24 24"
            fill="currentColor"
          >
            <path
              d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"
            />
          </svg>
          Profile
        </h1>
        <p class="text-gray-600 dark:text-gray-400 mt-2">
          Manage your personal information and view your activity
        </p>
      </div>

      {#if !isEditing}
        <button
          onclick={startEditing}
          class={combineClasses(
            'inline-flex items-center px-4 py-2 rounded-lg font-medium transition-colors',
            pageStyle.button.primary
          )}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 mr-2"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
            />
          </svg>
          Edit Profile
        </button>
      {:else}
        <div class="space-x-2">
          <button
            onclick={cancelEditing}
            class="px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
          >
            Cancel
          </button>
          <button
            onclick={saveProfile}
            class={combineClasses(
              'px-4 py-2 rounded-lg font-medium transition-colors',
              pageStyle.button.primary
            )}
          >
            Save Changes
          </button>
        </div>
      {/if}
    </div>
  </div>

  <!-- Main content container -->
  <div class="flex-grow overflow-hidden">
    <div class={combineClasses(layouts.twoColumnOneThree, 'h-full')}>
      <!-- Left sidebar - Profile info -->
      <div class={combineClasses(columnSpans.oneFourth, 'h-full flex flex-col')}>
        <div class={combineClasses(cardBase, pageStyle.border, 'h-full flex flex-col')}>
          <!-- Profile card -->
          <div class="p-6 text-center border-b border-gray-200 dark:border-gray-700">
            <!-- Avatar -->
            <div
              class="w-24 h-24 bg-indigo-500 rounded-full flex items-center justify-center text-white font-bold text-2xl mx-auto mb-4"
            >
              {getUserInitials($currentUser)}
            </div>

            <!-- Name and username -->
            <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-1">
              {getUserDisplayName($currentUser)}
            </h2>
            <p class="text-gray-500 dark:text-gray-400 text-sm mb-2">
              @{profileData.personalInfo.username}
            </p>
            <p class="text-gray-500 dark:text-gray-400 text-sm">
              Member since {formatDate(profileData.personalInfo.joinDate)}
            </p>
          </div>

          <!-- Stats -->
          <div class="p-6">
            <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-4">Statistics</h3>
            <div class="space-y-3">
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600 dark:text-gray-400">Total Tasks</span>
                <span class="text-sm font-medium text-gray-900 dark:text-gray-100"
                  >{profileData.stats.totalTasks}</span
                >
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600 dark:text-gray-400">Completed</span>
                <span class="text-sm font-medium text-gray-900 dark:text-gray-100"
                  >{profileData.stats.completedTasks}</span
                >
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600 dark:text-gray-400">Active Plans</span>
                <span class="text-sm font-medium text-gray-900 dark:text-gray-100"
                  >{profileData.stats.activePlans}</span
                >
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600 dark:text-gray-400">Achievements</span>
                <span class="text-sm font-medium text-gray-900 dark:text-gray-100"
                  >{profileData.stats.achievements}</span
                >
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right content area -->
      <div class={combineClasses(columnSpans.threeFourths, 'h-full flex flex-col')}>
        <div class={combineClasses(cardBase, pageStyle.border, 'h-full flex flex-col')}>
          {#if isEditing}
            <!-- Edit profile form -->
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
              <h2 class={combineClasses(headings.h2, pageStyle.text)}>Edit Profile</h2>
              <p class="text-gray-600 dark:text-gray-400 mt-2">Update your personal information</p>
            </div>

            <div class="flex-grow p-6 space-y-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                    >First Name</label
                  >
                  <input
                    type="text"
                    bind:value={editForm.firstName}
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-800 dark:text-gray-100"
                    placeholder="Enter your first name"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                    >Last Name</label
                  >
                  <input
                    type="text"
                    bind:value={editForm.lastName}
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-800 dark:text-gray-100"
                    placeholder="Enter your last name"
                  />
                </div>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                  >Bio</label
                >
                <textarea
                  bind:value={editForm.bio}
                  rows="4"
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-800 dark:text-gray-100"
                  placeholder="Tell us about yourself..."
                ></textarea>
              </div>

              <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                  Read-only Information
                </h3>
                <div class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                  <p><strong>Email:</strong> {profileData.personalInfo.email}</p>
                  <p><strong>Username:</strong> {profileData.personalInfo.username}</p>
                  <p>
                    <strong>Member since:</strong>
                    {formatDate(profileData.personalInfo.joinDate)}
                  </p>
                </div>
              </div>
            </div>
          {:else}
            <!-- View profile -->
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
              <h2 class={combineClasses(headings.h2, pageStyle.text)}>Personal Information</h2>
            </div>

            <div class="flex-grow p-6 space-y-6">
              <!-- Personal info -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    Contact Information
                  </h3>
                  <div class="space-y-2 text-sm">
                    <p>
                      <span class="text-gray-600 dark:text-gray-400">Email:</span>
                      {profileData.personalInfo.email}
                    </p>
                    <p>
                      <span class="text-gray-600 dark:text-gray-400">Username:</span>
                      {profileData.personalInfo.username}
                    </p>
                  </div>
                </div>

                <div>
                  <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    Account Details
                  </h3>
                  <div class="space-y-2 text-sm">
                    <p>
                      <span class="text-gray-600 dark:text-gray-400">Member since:</span>
                      {formatDate(profileData.personalInfo.joinDate)}
                    </p>
                    <p>
                      <span class="text-gray-600 dark:text-gray-400">Status:</span>
                      <span class="text-green-600 dark:text-green-400">Active</span>
                    </p>
                  </div>
                </div>
              </div>

              <!-- Bio -->
              {#if profileData.personalInfo.bio}
                <div>
                  <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Bio</h3>
                  <p class="text-gray-700 dark:text-gray-300 text-sm leading-relaxed">
                    {profileData.personalInfo.bio}
                  </p>
                </div>
              {/if}

              <!-- Recent activity -->
              <div>
                <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-4">
                  Recent Activity
                </h3>
                <div class="space-y-3">
                  {#each profileData.recentActivity as activity}
                    <div
                      class="flex items-start space-x-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
                    >
                      <span class="text-lg">{getActivityIcon(activity.type)}</span>
                      <div class="flex-1 min-w-0">
                        <p class="text-sm text-gray-900 dark:text-gray-100">
                          {activity.description}
                        </p>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          {formatTimestamp(activity.timestamp)}
                        </p>
                      </div>
                    </div>
                  {/each}

                  {#if profileData.recentActivity.length === 0}
                    <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-12 w-12 mx-auto mb-4 opacity-50"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                        />
                      </svg>
                      <p>No recent activity</p>
                      <p class="text-xs mt-1">
                        Start using the workspace to see your activity here
                      </p>
                    </div>
                  {/if}
                </div>
              </div>
            </div>
          {/if}
        </div>
      </div>
    </div>
  </div>
</div>
