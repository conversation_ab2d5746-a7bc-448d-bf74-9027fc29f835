<script lang="ts">
  import { captureError, captureMessage, addBreadcrumb } from '$lib/sentry';
  import { onMount } from 'svelte';

  let testResults: string[] = [];

  function addResult(message: string) {
    testResults = [...testResults, `${new Date().toLocaleTimeString()}: ${message}`];
  }

  function testSentryError() {
    try {
      addBreadcrumb('Testing Sentry error capture', 'test');
      throw new Error('This is a test error for Sentry');
    } catch (error) {
      captureError(error as Error, {
        test: {
          type: 'manual_error_test',
          component: 'sentry-test-page',
        },
      });
      addResult('✅ Error captured and sent to Sentry');
    }
  }

  function testSentryMessage() {
    captureMessage('Test message from Svelte frontend', 'info', {
      test: {
        type: 'manual_message_test',
        component: 'sentry-test-page',
      },
    });
    addResult('✅ Message sent to Sentry');
  }

  function testApiBreadcrumb() {
    addBreadcrumb('Testing API call breadcrumb', 'http', {
      url: '/api/v1/test',
      method: 'GET',
      status: 200,
    });
    addResult('✅ Breadcrumb added');
  }

  function testApiError() {
    // Simulate an API error
    fetch('/api/v1/nonexistent-endpoint').catch(error => {
      captureError(new Error('API endpoint not found'), {
        api: {
          endpoint: '/api/v1/nonexistent-endpoint',
          method: 'GET',
          error: error.message,
        },
      });
      addResult('✅ API error captured');
    });
  }

  onMount(() => {
    addResult('🚀 Sentry test page loaded');
    addBreadcrumb('Sentry test page loaded', 'navigation');
  });
</script>

<div class="container mx-auto p-6 max-w-4xl">
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
    <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-6">
      🔍 Sentry Error Tracking Test
    </h1>

    <div class="mb-8">
      <p class="text-gray-600 dark:text-gray-300 mb-4">
        This page allows you to test Sentry error tracking integration. Click the buttons below to
        generate different types of events that will be sent to Sentry.
      </p>

      <div
        class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-6"
      >
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
              <path
                fill-rule="evenodd"
                d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                clip-rule="evenodd"
              />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">Note</h3>
            <div class="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
              <p>
                Make sure Sentry DSN is configured in your environment variables to see these events
                in your Sentry dashboard.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
      <button
        on:click={testSentryError}
        class="bg-red-600 hover:bg-red-700 text-white font-medium py-3 px-4 rounded-lg transition-colors"
      >
        🚨 Test Error Capture
      </button>

      <button
        on:click={testSentryMessage}
        class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors"
      >
        📝 Test Message Capture
      </button>

      <button
        on:click={testApiBreadcrumb}
        class="bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg transition-colors"
      >
        🍞 Test Breadcrumb
      </button>

      <button
        on:click={testApiError}
        class="bg-orange-600 hover:bg-orange-700 text-white font-medium py-3 px-4 rounded-lg transition-colors"
      >
        🌐 Test API Error
      </button>
    </div>

    <div class="bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
      <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">Test Results</h2>

      {#if testResults.length === 0}
        <p class="text-gray-500 dark:text-gray-400 italic">
          No tests run yet. Click the buttons above to start testing.
        </p>
      {:else}
        <div class="space-y-2">
          {#each testResults as result}
            <div class="bg-white dark:bg-gray-800 rounded px-3 py-2 text-sm font-mono">
              {result}
            </div>
          {/each}
        </div>
      {/if}
    </div>

    <div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
      <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">Integration Status</h2>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div
          class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4"
        >
          <h3 class="font-medium text-green-800 dark:text-green-200 mb-2">
            ✅ Frontend Integration
          </h3>
          <ul class="text-sm text-green-700 dark:text-green-300 space-y-1">
            <li>• Sentry SDK installed</li>
            <li>• Error capture configured</li>
            <li>• User context tracking</li>
            <li>• Breadcrumb logging</li>
          </ul>
        </div>

        <div
          class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4"
        >
          <h3 class="font-medium text-blue-800 dark:text-blue-200 mb-2">✅ Backend Integration</h3>
          <ul class="text-sm text-blue-700 dark:text-blue-300 space-y-1">
            <li>• Django Sentry integration</li>
            <li>• API error tracking</li>
            <li>• Performance monitoring</li>
            <li>• Release tracking</li>
          </ul>
        </div>
      </div>
    </div>

    <div class="mt-6 text-center">
      <a
        href="/"
        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-600 bg-indigo-100 hover:bg-indigo-200 dark:bg-indigo-900 dark:text-indigo-200 dark:hover:bg-indigo-800 transition-colors"
      >
        ← Back to Home
      </a>
    </div>
  </div>
</div>
