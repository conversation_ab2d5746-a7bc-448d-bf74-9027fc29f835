import { render, type RenderResult } from '@testing-library/svelte';
import { vi, type MockedFunction } from 'vitest';

/**
 * Enhanced render function with common test setup
 */
export function renderComponent(
  component: any,
  props: Record<string, any> = {},
  options: any = {}
): RenderResult<any> {
  return render(component, { props, ...options });
}

/**
 * Mock API response helper
 */
export function mockApiResponse<T>(data: T, status = 200): Promise<T> {
  return Promise.resolve(data);
}

/**
 * Mock API error helper
 */
export function mockApiError(message: string, status = 400): Promise<never> {
  const error = new Error(message) as any;
  error.status = status;
  return Promise.reject(error);
}

/**
 * Mock fetch response
 */
export function mockFetchResponse<T>(data: T, status = 200, ok = true) {
  const mockFetch = global.fetch as MockedFunction<typeof fetch>;
  mockFetch.mockResolvedValueOnce({
    ok,
    status,
    headers: {
      get: vi.fn((name: string) => {
        if (name === 'content-type') {
          return 'application/json';
        }
        return null;
      }),
    },
    json: () => Promise.resolve(data),
    text: () => Promise.resolve(JSON.stringify(data)),
  } as Response);
}

/**
 * Mock fetch error
 */
export function mockFetchError(message: string, status = 400) {
  const mockFetch = global.fetch as MockedFunction<typeof fetch>;
  const error = new Error(message) as any;
  error.status = status;
  mockFetch.mockRejectedValueOnce(error);
}

/**
 * Wait for next tick (useful for reactive updates)
 */
export function nextTick(): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, 0));
}

/**
 * Mock localStorage for tests
 */
export function mockLocalStorage() {
  const store: Record<string, string> = {};
  
  return {
    getItem: vi.fn((key: string) => store[key] || null),
    setItem: vi.fn((key: string, value: string) => {
      store[key] = value;
    }),
    removeItem: vi.fn((key: string) => {
      delete store[key];
    }),
    clear: vi.fn(() => {
      Object.keys(store).forEach(key => delete store[key]);
    }),
    get store() {
      return { ...store };
    },
  };
}

/**
 * Create a mock user profile for testing
 */
export function createMockUser(overrides: Partial<any> = {}) {
  return {
    id: 1,
    username: 'testuser',
    email: '<EMAIL>',
    first_name: 'Test',
    last_name: 'User',
    is_active: true,
    date_joined: '2024-01-01T00:00:00Z',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    profile: {
      user_id: 1,
      professional_title: 'Developer',
      one_liner_bio: 'Test bio',
      skill: 'Testing',
      summary: 'Test summary',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
    },
    ...overrides,
  };
}

/**
 * Create a mock todo item for testing
 */
export function createMockTodo(overrides: Partial<any> = {}) {
  return {
    id: 1,
    title: 'Test Todo',
    description: 'Test description',
    status: 'pending',
    priority: 'medium',
    is_current_focus: false,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    due_date: null,
    completed_at: null,
    user: 1,
    ...overrides,
  };
}

/**
 * Wait for store updates to complete
 */
export async function waitForStoreUpdate(store: any, timeout = 1000): Promise<void> {
  return new Promise((resolve, reject) => {
    const timer = setTimeout(() => {
      reject(new Error('Store update timeout'));
    }, timeout);

    const unsubscribe = store.subscribe(() => {
      clearTimeout(timer);
      unsubscribe();
      resolve();
    });
  });
}
