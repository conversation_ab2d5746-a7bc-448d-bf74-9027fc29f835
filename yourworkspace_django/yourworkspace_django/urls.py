"""
URL configuration for yourworkspace_django project.
Migrated from Flask application routes
"""
from django.contrib import admin
from django.urls import path, include
from django.http import JsonResponse

def api_root(request):
    """API根端点"""
    return JsonResponse({
        'message': 'YourWorkspace Django API',
        'version': '1.0',
        'endpoints': {
            'auth': '/api/v1/auth/',
            'profile': '/api/v1/anchor/',
            'todos': '/api/v1/todo/',
            'achievements': '/api/v1/achievements/',
            'plans': '/api/v1/plans/',
            'admin': '/admin/',
        }
    })

urlpatterns = [
    # Django Admin
    path('admin/', admin.site.urls),

    # API Root
    # path('api/', api_root, name='api_root'),
    # path('api/v1/', api_root, name='api_v1_root'),

    # API Endpoints (migrated from Flask blueprints)
    # path('api/v1/auth/', include('authentication.urls')),
    # path('api/v1/anchor/', include('user_profiles.urls')),
    # path('api/v1/todo/', include('todos.urls')),
    # path('api/v1/achievements/', include('achievements.urls')),
    # path('api/v1/plans/', include('plans.urls')),

    path('auth/', include('authentication.urls')),
    path('anchor/', include('user_profiles.urls')),
    path('todo/', include('todos.urls')),
    path('achievements/', include('achievements.urls')),
    path('plans/', include('plans.urls')),
]
