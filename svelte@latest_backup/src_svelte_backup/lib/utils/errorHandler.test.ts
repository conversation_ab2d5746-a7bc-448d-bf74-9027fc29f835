import { describe, it, expect, vi, beforeEach } from 'vitest';
import {
  createApiError,
  handleApiError,
  getDisplayMessage,
  shouldRetry,
  getRetryDelay,
  ErrorType,
  ErrorSeverity,
  type ApiError,
} from './errorHandler';

// Mock Sentry
vi.mock('$lib/sentry', () => ({
  addBreadcrumb: vi.fn(),
}));

describe('errorHandler', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Reset console methods
    vi.spyOn(console, 'error').mockImplementation(() => {});
    vi.spyOn(console, 'warn').mockImplementation(() => {});
    vi.spyOn(console, 'info').mockImplementation(() => {});
    vi.spyOn(console, 'log').mockImplementation(() => {});
  });

  describe('createApiError', () => {
    it('should create error with correct properties for 400 status', () => {
      const error = createApiError('Bad request', 400, undefined, { field: 'invalid' });

      expect(error.type).toBe(ErrorType.CLIENT);
      expect(error.severity).toBe(ErrorSeverity.LOW);
      expect(error.status).toBe(400);
      expect(error.retryable).toBe(false);
      expect(error.userMessage).toBe('The request contains invalid data. Please check your input and try again.');
      expect(error.technicalMessage).toBe('Bad request');
      expect(error.data).toEqual({ field: 'invalid' });
    });

    it('should create error with correct properties for 401 status', () => {
      const error = createApiError('Unauthorized', 401);

      expect(error.type).toBe(ErrorType.AUTHENTICATION);
      expect(error.severity).toBe(ErrorSeverity.HIGH);
      expect(error.status).toBe(401);
      expect(error.retryable).toBe(false);
      expect(error.userMessage).toBe('Your session has expired. Please log in again.');
    });

    it('should create error with correct properties for 500 status', () => {
      const error = createApiError('Internal server error', 500);

      expect(error.type).toBe(ErrorType.SERVER);
      expect(error.severity).toBe(ErrorSeverity.HIGH);
      expect(error.status).toBe(500);
      expect(error.retryable).toBe(true);
      expect(error.userMessage).toBe('A server error occurred. Please try again later.');
    });

    it('should create error with correct properties for network error', () => {
      const error = createApiError('Network error');

      expect(error.type).toBe(ErrorType.NETWORK);
      expect(error.severity).toBe(ErrorSeverity.MEDIUM);
      expect(error.status).toBeUndefined();
      expect(error.retryable).toBe(true);
      expect(error.userMessage).toBe('An unexpected error occurred. Please try again.');
    });

    it('should extract error code from response data', () => {
      const error = createApiError('Error', 400, undefined, { 
        code: 'VALIDATION_ERROR',
        message: 'Field is required' 
      });

      expect(error.code).toBe('VALIDATION_ERROR');
    });

    it('should include context in error', () => {
      const context = { endpoint: '/api/test', method: 'POST' };
      const error = createApiError('Error', 400, undefined, undefined, context);

      expect(error.context).toEqual(context);
    });
  });

  describe('handleApiError', () => {
    it('should handle ApiError instances', () => {
      const originalError = createApiError('Test error', 400);
      const handledError = handleApiError(originalError);

      expect(handledError).toBe(originalError);
      expect(console.info).toHaveBeenCalledWith(
        'API Info:',
        'Test error',
        expect.objectContaining({
          type: ErrorType.CLIENT,
          severity: ErrorSeverity.LOW,
          status: 400,
        })
      );
    });

    it('should convert regular Error to ApiError', () => {
      const originalError = new Error('Regular error') as any;
      originalError.status = 404;
      originalError.data = { message: 'Not found' };

      const handledError = handleApiError(originalError);

      expect(handledError.type).toBe(ErrorType.CLIENT);
      expect(handledError.status).toBe(404);
      expect(handledError.technicalMessage).toBe('Regular error');
    });

    it('should handle non-Error objects', () => {
      const handledError = handleApiError('String error');

      expect(handledError.type).toBe(ErrorType.NETWORK);
      expect(handledError.technicalMessage).toBe('Unknown error occurred');
      expect(handledError.data).toBe('String error');
    });

    it('should include context in handled error', () => {
      const context = { component: 'TestComponent' };
      const handledError = handleApiError(new Error('Test'), context);

      expect(handledError.context).toEqual(context);
    });
  });

  describe('getDisplayMessage', () => {
    it('should return user-friendly message', () => {
      const error = createApiError('Technical error', 400);
      const message = getDisplayMessage(error);

      expect(message).toBe('The request contains invalid data. Please check your input and try again.');
    });
  });

  describe('shouldRetry', () => {
    it('should return true for retryable errors within retry limit', () => {
      const error = createApiError('Server error', 500);
      
      expect(shouldRetry(error, 0, 3)).toBe(true);
      expect(shouldRetry(error, 2, 3)).toBe(true);
    });

    it('should return false for retryable errors exceeding retry limit', () => {
      const error = createApiError('Server error', 500);
      
      expect(shouldRetry(error, 3, 3)).toBe(false);
      expect(shouldRetry(error, 5, 3)).toBe(false);
    });

    it('should return false for non-retryable errors', () => {
      const error = createApiError('Bad request', 400);
      
      expect(shouldRetry(error, 0, 3)).toBe(false);
    });
  });

  describe('getRetryDelay', () => {
    it('should calculate exponential backoff delay', () => {
      expect(getRetryDelay(0, 1000)).toBe(1000);
      expect(getRetryDelay(1, 1000)).toBe(2000);
      expect(getRetryDelay(2, 1000)).toBe(4000);
      expect(getRetryDelay(3, 1000)).toBe(8000);
    });

    it('should cap delay at maximum value', () => {
      expect(getRetryDelay(10, 1000)).toBe(10000); // Max 10 seconds
    });

    it('should use default base delay', () => {
      expect(getRetryDelay(1)).toBe(2000); // 1000 * 2^1
    });
  });

  describe('error logging', () => {
    it('should log critical errors with console.error', () => {
      const error = createApiError('Critical error', 500);
      error.severity = ErrorSeverity.CRITICAL;
      
      handleApiError(error);

      expect(console.error).toHaveBeenCalledWith(
        'API Error:',
        'Critical error',
        expect.objectContaining({
          severity: ErrorSeverity.CRITICAL,
        })
      );
    });

    it('should log medium errors with console.warn', () => {
      const error = createApiError('Warning', 429);
      
      handleApiError(error);

      expect(console.warn).toHaveBeenCalledWith(
        'API Warning:',
        'Warning',
        expect.objectContaining({
          severity: ErrorSeverity.MEDIUM,
        })
      );
    });

    it('should log low errors with console.info', () => {
      const error = createApiError('Info', 400);
      
      handleApiError(error);

      expect(console.info).toHaveBeenCalledWith(
        'API Info:',
        'Info',
        expect.objectContaining({
          severity: ErrorSeverity.LOW,
        })
      );
    });
  });
});
