<script lang="ts">
  import { fly } from 'svelte/transition';
  import { quintOut } from 'svelte/easing';
  import { activeNotifications, errorStore } from '$lib/store/errorStore';
  import { ErrorSeverity } from '$lib/utils/errorHandler';

  // Get severity-based styling
  function getSeverityClasses(severity: ErrorSeverity): string {
    switch (severity) {
      case ErrorSeverity.LOW:
        return 'bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-200';
      case ErrorSeverity.MEDIUM:
        return 'bg-yellow-50 border-yellow-200 text-yellow-800 dark:bg-yellow-900/20 dark:border-yellow-800 dark:text-yellow-200';
      case ErrorSeverity.HIGH:
        return 'bg-red-50 border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-200';
      case ErrorSeverity.CRITICAL:
        return 'bg-red-100 border-red-300 text-red-900 dark:bg-red-900/30 dark:border-red-700 dark:text-red-100';
      default:
        return 'bg-gray-50 border-gray-200 text-gray-800 dark:bg-gray-900/20 dark:border-gray-700 dark:text-gray-200';
    }
  }

  // Get severity icon
  function getSeverityIcon(severity: ErrorSeverity): string {
    switch (severity) {
      case ErrorSeverity.LOW:
        return '💡'; // Info
      case ErrorSeverity.MEDIUM:
        return '⚠️'; // Warning
      case ErrorSeverity.HIGH:
        return '❌'; // Error
      case ErrorSeverity.CRITICAL:
        return '🚨'; // Critical
      default:
        return 'ℹ️'; // Default info
    }
  }

  function handleDismiss(id: string) {
    errorStore.dismissError(id);
  }

  function handleRetry(notification: any) {
    // Dismiss current notification
    errorStore.dismissError(notification.id);
    
    // You could implement retry logic here
    // For now, just dismiss the notification
    console.log('Retry requested for:', notification.error);
  }
</script>

<!-- Error Notifications Container -->
<div class="fixed top-4 right-4 z-50 space-y-2 max-w-sm">
  {#each $activeNotifications as notification (notification.id)}
    <div
      class="border rounded-lg shadow-lg p-4 {getSeverityClasses(notification.error.severity)}"
      transition:fly={{ x: 300, duration: 300, easing: quintOut }}
      role="alert"
      aria-live="polite"
    >
      <!-- Header -->
      <div class="flex items-start justify-between">
        <div class="flex items-center space-x-2">
          <span class="text-lg" role="img" aria-label="Severity indicator">
            {getSeverityIcon(notification.error.severity)}
          </span>
          <h4 class="font-medium text-sm">
            {#if notification.error.severity === ErrorSeverity.CRITICAL}
              Critical Error
            {:else if notification.error.severity === ErrorSeverity.HIGH}
              Error
            {:else if notification.error.severity === ErrorSeverity.MEDIUM}
              Warning
            {:else}
              Information
            {/if}
          </h4>
        </div>
        
        <button
          class="text-current opacity-70 hover:opacity-100 transition-opacity"
          on:click={() => handleDismiss(notification.id)}
          aria-label="Dismiss notification"
        >
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
          </svg>
        </button>
      </div>

      <!-- Message -->
      <div class="mt-2">
        <p class="text-sm">{notification.message}</p>
        
        <!-- Technical details for development -->
        {#if notification.error.status}
          <p class="text-xs opacity-75 mt-1">
            Status: {notification.error.status}
            {#if notification.error.code}
              • Code: {notification.error.code}
            {/if}
          </p>
        {/if}
      </div>

      <!-- Actions -->
      <div class="mt-3 flex space-x-2">
        {#if notification.error.retryable}
          <button
            class="text-xs px-2 py-1 rounded border border-current opacity-75 hover:opacity-100 transition-opacity"
            on:click={() => handleRetry(notification)}
          >
            Retry
          </button>
        {/if}
        
        {#if notification.error.severity === ErrorSeverity.CRITICAL}
          <button
            class="text-xs px-2 py-1 rounded border border-current opacity-75 hover:opacity-100 transition-opacity"
            on:click={() => window.location.reload()}
          >
            Reload Page
          </button>
        {/if}
      </div>

      <!-- Auto-hide progress bar -->
      {#if notification.autoHide && notification.duration > 0}
        <div class="mt-2 w-full bg-current opacity-20 rounded-full h-1">
          <div 
            class="bg-current h-1 rounded-full transition-all duration-100 ease-linear"
            style="width: {100 - ((Date.now() - notification.timestamp.getTime()) / notification.duration) * 100}%"
          ></div>
        </div>
      {/if}
    </div>
  {/each}
</div>

<!-- Global Error Modal for Critical Errors -->
{#if $activeNotifications.some(n => n.error.severity === ErrorSeverity.CRITICAL)}
  <div class="fixed inset-0 bg-black bg-opacity-50 z-40 flex items-center justify-center p-4">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full p-6">
      <div class="flex items-center space-x-3 text-red-600 dark:text-red-400">
        <span class="text-2xl">🚨</span>
        <h2 class="text-lg font-semibold">Critical Error</h2>
      </div>
      
      <div class="mt-4 text-gray-700 dark:text-gray-300">
        <p>A critical error has occurred that may affect the application's functionality.</p>
        
        {#each $activeNotifications.filter(n => n.error.severity === ErrorSeverity.CRITICAL) as notification}
          <div class="mt-3 p-3 bg-red-50 dark:bg-red-900/20 rounded border border-red-200 dark:border-red-800">
            <p class="text-sm text-red-800 dark:text-red-200">{notification.message}</p>
          </div>
        {/each}
      </div>
      
      <div class="mt-6 flex space-x-3">
        <button
          class="flex-1 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
          on:click={() => window.location.reload()}
        >
          Reload Page
        </button>
        <button
          class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          on:click={() => errorStore.clearAll()}
        >
          Dismiss
        </button>
      </div>
    </div>
  </div>
{/if}

<style>
  /* Custom animations for error notifications */
  @keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
  }
  
  .shake {
    animation: shake 0.5s ease-in-out;
  }
</style>
