<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { page } from '$app/stores';
  import { currentUser } from '$lib/store/userStore';
  import { getUserDisplayName, getUserInitials } from '$lib/store/userStore';

  // Props
  let { isOpen = false, onClose = () => {} } = $props<{
    isOpen: boolean;
    onClose: () => void;
  }>();

  // Navigation items
  const navigationItems = [
    {
      id: 'profile',
      label: 'Profile',
      icon: '👤',
      path: '/profile',
      description: 'View and edit your profile',
    },
    {
      id: 'task-arrange',
      label: 'Task Arrange',
      icon: '📋',
      path: '/todo',
      description: 'Manage your tasks',
    },
    {
      id: 'blog',
      label: 'Blog',
      icon: '📝',
      path: '/blog',
      description: 'Read and write blog posts',
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: '⚙️',
      path: '/settings',
      description: 'Configure your preferences',
    },
  ];

  // Handle navigation
  function navigateTo(path: string) {
    onClose();
    goto(path);
  }

  // Handle backdrop click
  function handleBackdropClick(event: MouseEvent) {
    if (event.target === event.currentTarget) {
      onClose();
    }
  }

  // Handle escape key
  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Escape') {
      onClose();
    }
  }

  // Set up event listeners
  onMount(() => {
    return () => {
      document.removeEventListener('keydown', handleKeydown);
      document.body.style.overflow = '';
    };
  });

  // Update event listeners when isOpen changes using $effect
  $effect(() => {
    if (isOpen) {
      document.addEventListener('keydown', handleKeydown);
      document.body.style.overflow = 'hidden';
    } else {
      document.removeEventListener('keydown', handleKeydown);
      document.body.style.overflow = '';
    }
  });
</script>

<!-- Backdrop overlay -->
{#if isOpen}
  <div
    class="fixed inset-0 bg-black bg-opacity-50 z-overlay transition-opacity duration-300"
    style="z-index: 1030;"
    onclick={handleBackdropClick}
    role="button"
    tabindex="-1"
    aria-label="Close navigation"
  ></div>
{/if}

<!-- Side navigation panel -->
<div
  class="fixed top-0 left-0 h-full w-80 bg-white dark:bg-gray-900 shadow-2xl transform transition-transform duration-300 ease-in-out z-drawer {isOpen
    ? 'translate-x-0'
    : '-translate-x-full'}"
  style="z-index: 1035;"
  role="navigation"
  aria-label="Side navigation"
>
  <!-- Header -->
  <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
    <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100">Navigation</h2>
    <button
      onclick={onClose}
      class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
      aria-label="Close navigation"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-6 w-6 text-gray-500 dark:text-gray-400"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M6 18L18 6M6 6l12 12"
        />
      </svg>
    </button>
  </div>

  <!-- User profile section -->
  {#if $currentUser}
    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
      <div class="flex items-center space-x-4">
        <!-- User avatar -->
        <div
          class="w-12 h-12 bg-indigo-500 rounded-full flex items-center justify-center text-white font-semibold text-lg"
        >
          {getUserInitials($currentUser)}
        </div>
        <!-- User info -->
        <div class="flex-1 min-w-0">
          <p class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
            {getUserDisplayName($currentUser)}
          </p>
          <p class="text-sm text-gray-500 dark:text-gray-400 truncate">
            {$currentUser.email}
          </p>
        </div>
      </div>
    </div>
  {/if}

  <!-- Navigation items -->
  <div class="flex-1 overflow-y-auto">
    <nav class="p-4 space-y-2">
      {#each navigationItems as item}
        <button
          onclick={() => navigateTo(item.path)}
          class="w-full flex items-center space-x-4 p-4 rounded-lg text-left hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors group {$page
            .url.pathname === item.path
            ? 'bg-indigo-50 dark:bg-indigo-900/30 border-l-4 border-indigo-500'
            : ''}"
          aria-label="Navigate to {item.label}"
        >
          <!-- Icon -->
          <div class="flex-shrink-0 w-8 h-8 flex items-center justify-center text-xl">
            {item.icon}
          </div>

          <!-- Content -->
          <div class="flex-1 min-w-0">
            <p
              class="text-sm font-medium text-gray-900 dark:text-gray-100 group-hover:text-indigo-600 dark:group-hover:text-indigo-400 {$page
                .url.pathname === item.path
                ? 'text-indigo-600 dark:text-indigo-400'
                : ''}"
            >
              {item.label}
            </p>
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {item.description}
            </p>
          </div>

          <!-- Arrow indicator for current page -->
          {#if $page.url.pathname === item.path}
            <div class="flex-shrink-0">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4 text-indigo-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </div>
          {/if}
        </button>
      {/each}
    </nav>
  </div>

  <!-- Footer -->
  <div class="p-4 border-t border-gray-200 dark:border-gray-700">
    <p class="text-xs text-gray-500 dark:text-gray-400 text-center">Personal Workspace v1.0</p>
  </div>
</div>
