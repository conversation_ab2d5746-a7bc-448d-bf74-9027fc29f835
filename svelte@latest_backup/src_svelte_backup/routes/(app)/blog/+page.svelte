<script lang="ts">
  import { onMount } from 'svelte';
  import {
    pageContainer,
    colorSchemes,
    layouts,
    columnSpans,
    headings,
    scrollArea,
    cardBase,
    combineClasses,
  } from '$lib/styles/pageStyles';

  // Use a consistent color scheme for the blog page
  const pageStyle = colorSchemes.emerald;

  // Sample blog posts data (placeholder)
  let blogPosts = $state([
    {
      id: 1,
      title: 'Welcome to My Blog',
      excerpt:
        "This is the first post on my personal blog. Here I'll share thoughts, experiences, and insights.",
      content:
        'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
      date: '2024-01-15',
      tags: ['welcome', 'introduction'],
      readTime: '3 min read',
    },
    {
      id: 2,
      title: 'Productivity Tips',
      excerpt: 'Some useful tips for staying productive while working from home.',
      content:
        'Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.',
      date: '2024-01-10',
      tags: ['productivity', 'tips'],
      readTime: '5 min read',
    },
  ]);

  let selectedPost = $state(null);
  let isCreating = $state(false);
  let newPost = $state({
    title: '',
    content: '',
    tags: '',
  });

  function selectPost(post) {
    selectedPost = post;
    isCreating = false;
  }

  function startCreating() {
    isCreating = true;
    selectedPost = null;
    newPost = {
      title: '',
      content: '',
      tags: '',
    };
  }

  function cancelCreating() {
    isCreating = false;
    newPost = {
      title: '',
      content: '',
      tags: '',
    };
  }

  function savePost() {
    if (newPost.title.trim() && newPost.content.trim()) {
      const post = {
        id: Date.now(),
        title: newPost.title,
        excerpt: newPost.content.substring(0, 100) + '...',
        content: newPost.content,
        date: new Date().toISOString().split('T')[0],
        tags: newPost.tags
          .split(',')
          .map(tag => tag.trim())
          .filter(tag => tag),
        readTime: Math.ceil(newPost.content.split(' ').length / 200) + ' min read',
      };

      blogPosts = [post, ...blogPosts];
      cancelCreating();
      selectPost(post);
    }
  }

  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  }
</script>

<svelte:head>
  <title>Blog - Personal Workspace</title>
  <meta name="description" content="Personal blog for thoughts and insights" />
</svelte:head>

<div class={combineClasses(pageContainer, 'h-[calc(100vh-180px)] flex flex-col')}>
  <!-- Page header -->
  <div class="mb-6">
    <div class="flex justify-between items-center">
      <div>
        <h1 class={combineClasses(headings.h1, pageStyle.text)}>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class={combineClasses('h-8 w-8 mr-3 inline', pageStyle.icon)}
            viewBox="0 0 24 24"
            fill="currentColor"
          >
            <path
              d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"
            />
          </svg>
          Blog
        </h1>
        <p class="text-gray-600 dark:text-gray-400 mt-2">Share your thoughts and insights</p>
      </div>

      <button
        onclick={startCreating}
        class={combineClasses(
          'inline-flex items-center px-4 py-2 rounded-lg font-medium transition-colors',
          pageStyle.button.primary
        )}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5 mr-2"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 4v16m8-8H4"
          />
        </svg>
        New Post
      </button>
    </div>
  </div>

  <!-- Main content container -->
  <div class="flex-grow overflow-hidden">
    <div class={combineClasses(layouts.twoColumnOneThree, 'h-full')}>
      <!-- Left sidebar - Blog posts list -->
      <div class={combineClasses(columnSpans.oneFourth, 'h-full flex flex-col')}>
        <div class={combineClasses(cardBase, pageStyle.border, 'h-full flex flex-col')}>
          <!-- Header -->
          <div class="p-4 border-b border-gray-200 dark:border-gray-700">
            <h2 class={combineClasses(headings.h3, pageStyle.text)}>Recent Posts</h2>
          </div>

          <!-- Posts list -->
          <div class={combineClasses(scrollArea.container, 'flex-grow relative')}>
            <div class={combineClasses(scrollArea.indicator, 'left-0', pageStyle.scrollbar)}></div>
            <div class="pl-3 absolute inset-0 overflow-y-auto pr-2">
              <div class="space-y-2 p-2">
                {#each blogPosts as post}
                  <button
                    onclick={() => selectPost(post)}
                    class="w-full text-left p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors {selectedPost?.id ===
                    post.id
                      ? 'bg-emerald-50 dark:bg-emerald-900/30 border-l-4 border-emerald-500'
                      : ''}"
                  >
                    <h3
                      class="font-medium text-gray-900 dark:text-gray-100 text-sm mb-1 line-clamp-2"
                    >
                      {post.title}
                    </h3>
                    <p class="text-xs text-gray-500 dark:text-gray-400 mb-2 line-clamp-2">
                      {post.excerpt}
                    </p>
                    <div class="flex justify-between items-center text-xs text-gray-400">
                      <span>{formatDate(post.date)}</span>
                      <span>{post.readTime}</span>
                    </div>
                  </button>
                {/each}

                {#if blogPosts.length === 0}
                  <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-12 w-12 mx-auto mb-4 opacity-50"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"
                      />
                    </svg>
                    <p>No blog posts yet</p>
                    <p class="text-xs mt-1">Create your first post to get started</p>
                  </div>
                {/if}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right content area -->
      <div class={combineClasses(columnSpans.threeFourths, 'h-full flex flex-col')}>
        <div class={combineClasses(cardBase, pageStyle.border, 'h-full flex flex-col')}>
          {#if isCreating}
            <!-- Create new post form -->
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
              <div class="flex justify-between items-center">
                <h2 class={combineClasses(headings.h3, pageStyle.text)}>Create New Post</h2>
                <div class="space-x-2">
                  <button
                    onclick={cancelCreating}
                    class="px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onclick={savePost}
                    class={combineClasses(
                      'px-4 py-2 rounded-lg font-medium transition-colors',
                      pageStyle.button.primary
                    )}
                  >
                    Save Post
                  </button>
                </div>
              </div>
            </div>

            <div class="flex-grow p-6 space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                  >Title</label
                >
                <input
                  type="text"
                  bind:value={newPost.title}
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 dark:bg-gray-800 dark:text-gray-100"
                  placeholder="Enter post title..."
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                  >Tags (comma-separated)</label
                >
                <input
                  type="text"
                  bind:value={newPost.tags}
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 dark:bg-gray-800 dark:text-gray-100"
                  placeholder="tag1, tag2, tag3..."
                />
              </div>

              <div class="flex-grow flex flex-col">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                  >Content</label
                >
                <textarea
                  bind:value={newPost.content}
                  class="flex-grow w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 dark:bg-gray-800 dark:text-gray-100 resize-none"
                  placeholder="Write your post content here..."
                ></textarea>
              </div>
            </div>
          {:else if selectedPost}
            <!-- Display selected post -->
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
              <h2 class={combineClasses(headings.h2, pageStyle.text)}>{selectedPost.title}</h2>
              <div
                class="flex items-center space-x-4 mt-2 text-sm text-gray-500 dark:text-gray-400"
              >
                <span>{formatDate(selectedPost.date)}</span>
                <span>{selectedPost.readTime}</span>
                {#if selectedPost.tags.length > 0}
                  <div class="flex space-x-1">
                    {#each selectedPost.tags as tag}
                      <span
                        class="px-2 py-1 bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300 rounded-full text-xs"
                      >
                        {tag}
                      </span>
                    {/each}
                  </div>
                {/if}
              </div>
            </div>

            <div class="flex-grow p-6 overflow-y-auto">
              <div class="prose dark:prose-invert max-w-none">
                <p class="text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-wrap">
                  {selectedPost.content}
                </p>
              </div>
            </div>
          {:else}
            <!-- Welcome message -->
            <div class="flex-grow flex items-center justify-center">
              <div class="text-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-16 w-16 mx-auto mb-4 text-gray-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"
                  />
                </svg>
                <h3 class="text-xl font-medium text-gray-900 dark:text-gray-100 mb-2">
                  Welcome to Your Blog
                </h3>
                <p class="text-gray-500 dark:text-gray-400 mb-4">
                  Select a post from the sidebar to read, or create a new post to get started.
                </p>
                <button
                  onclick={startCreating}
                  class={combineClasses(
                    'inline-flex items-center px-4 py-2 rounded-lg font-medium transition-colors',
                    pageStyle.button.primary
                  )}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 mr-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 4v16m8-8H4"
                    />
                  </svg>
                  Create Your First Post
                </button>
              </div>
            </div>
          {/if}
        </div>
      </div>
    </div>
  </div>
</div>
