<script lang="ts">
  import { onMount } from 'svelte';
  import {
    pageContainer,
    colorSchemes,
    layouts,
    columnSpans,
    headings,
    scrollArea,
    cardBase,
    combineClasses,
  } from '$lib/styles/pageStyles';
  import { ThemeToggle } from '$lib/components/ui';

  // Use a consistent color scheme for the settings page
  const pageStyle = colorSchemes.slate;

  // Settings categories
  const settingsCategories = [
    {
      id: 'profile',
      name: 'Profile',
      icon: '👤',
      description: 'Manage your personal information',
    },
    {
      id: 'preferences',
      name: 'Preferences',
      icon: '⚙️',
      description: 'Customize your experience',
    },
    {
      id: 'notifications',
      name: 'Notifications',
      icon: '🔔',
      description: 'Control notification settings',
    },
    {
      id: 'privacy',
      name: 'Privacy & Security',
      icon: '🔒',
      description: 'Manage privacy and security settings',
    },
    {
      id: 'data',
      name: 'Data Management',
      icon: '💾',
      description: 'Export, import, and manage your data',
    },
  ];

  let selectedCategory = $state('profile');
  let settings = $state({
    profile: {
      firstName: '',
      lastName: '',
      email: '',
      bio: '',
    },
    preferences: {
      language: 'en',
      timezone: 'UTC',
      dateFormat: 'MM/DD/YYYY',
      startOfWeek: 'monday',
    },
    notifications: {
      emailNotifications: true,
      pushNotifications: false,
      taskReminders: true,
      weeklyDigest: true,
    },
    privacy: {
      profileVisibility: 'private',
      dataSharing: false,
      analyticsOptOut: false,
    },
  });

  function selectCategory(categoryId: string) {
    selectedCategory = categoryId;
  }

  function saveSettings() {
    // Placeholder for saving settings
    console.log('Saving settings:', settings);
    // Here you would typically call an API to save the settings
  }

  function exportData() {
    // Placeholder for data export
    console.log('Exporting data...');
    // Here you would typically trigger a data export
  }

  function importData() {
    // Placeholder for data import
    console.log('Importing data...');
    // Here you would typically open a file picker for data import
  }

  function resetSettings() {
    if (
      confirm(
        'Are you sure you want to reset all settings to default? This action cannot be undone.'
      )
    ) {
      // Reset to default values
      settings = {
        profile: {
          firstName: '',
          lastName: '',
          email: '',
          bio: '',
        },
        preferences: {
          language: 'en',
          timezone: 'UTC',
          dateFormat: 'MM/DD/YYYY',
          startOfWeek: 'monday',
        },
        notifications: {
          emailNotifications: true,
          pushNotifications: false,
          taskReminders: true,
          weeklyDigest: true,
        },
        privacy: {
          profileVisibility: 'private',
          dataSharing: false,
          analyticsOptOut: false,
        },
      };
    }
  }
</script>

<svelte:head>
  <title>Settings - Personal Workspace</title>
  <meta name="description" content="Manage your personal workspace settings and preferences" />
</svelte:head>

<div class={combineClasses(pageContainer, 'h-[calc(100vh-180px)] flex flex-col')}>
  <!-- Page header -->
  <div class="mb-6">
    <div class="flex justify-between items-center">
      <div>
        <h1 class={combineClasses(headings.h1, pageStyle.text)}>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class={combineClasses('h-8 w-8 mr-3 inline', pageStyle.icon)}
            viewBox="0 0 24 24"
            fill="currentColor"
          >
            <path
              d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.82,11.69,4.82,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"
            />
          </svg>
          Settings
        </h1>
        <p class="text-gray-600 dark:text-gray-400 mt-2">
          Manage your preferences and account settings
        </p>
      </div>

      <button
        onclick={saveSettings}
        class={combineClasses(
          'inline-flex items-center px-4 py-2 rounded-lg font-medium transition-colors',
          pageStyle.button.primary
        )}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5 mr-2"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M5 13l4 4L19 7"
          />
        </svg>
        Save Changes
      </button>
    </div>
  </div>

  <!-- Main content container -->
  <div class="flex-grow overflow-hidden">
    <div class={combineClasses(layouts.twoColumnOneThree, 'h-full')}>
      <!-- Left sidebar - Settings categories -->
      <div class={combineClasses(columnSpans.oneFourth, 'h-full flex flex-col')}>
        <div class={combineClasses(cardBase, pageStyle.border, 'h-full flex flex-col')}>
          <!-- Header -->
          <div class="p-4 border-b border-gray-200 dark:border-gray-700">
            <h2 class={combineClasses(headings.h3, pageStyle.text)}>Categories</h2>
          </div>

          <!-- Categories list -->
          <div class="flex-grow p-2">
            <nav class="space-y-1">
              {#each settingsCategories as category}
                <button
                  onclick={() => selectCategory(category.id)}
                  class="w-full text-left p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors {selectedCategory ===
                  category.id
                    ? 'bg-slate-50 dark:bg-slate-900/30 border-l-4 border-slate-500'
                    : ''}"
                >
                  <div class="flex items-center space-x-3">
                    <span class="text-xl">{category.icon}</span>
                    <div class="flex-1 min-w-0">
                      <p class="font-medium text-gray-900 dark:text-gray-100 text-sm">
                        {category.name}
                      </p>
                      <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {category.description}
                      </p>
                    </div>
                  </div>
                </button>
              {/each}
            </nav>
          </div>
        </div>
      </div>

      <!-- Right content area -->
      <div class={combineClasses(columnSpans.threeFourths, 'h-full flex flex-col')}>
        <div class={combineClasses(cardBase, pageStyle.border, 'h-full flex flex-col')}>
          <!-- Content header -->
          <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            {#each settingsCategories as category}
              {#if selectedCategory === category.id}
                <h2 class={combineClasses(headings.h2, pageStyle.text)}>
                  <span class="text-2xl mr-3">{category.icon}</span>
                  {category.name}
                </h2>
                <p class="text-gray-600 dark:text-gray-400 mt-2">{category.description}</p>
              {/if}
            {/each}
          </div>

          <!-- Settings content -->
          <div class="flex-grow p-6 overflow-y-auto">
            {#if selectedCategory === 'profile'}
              <div class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                      >First Name</label
                    >
                    <input
                      type="text"
                      bind:value={settings.profile.firstName}
                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-800 dark:text-gray-100"
                      placeholder="Enter your first name"
                    />
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                      >Last Name</label
                    >
                    <input
                      type="text"
                      bind:value={settings.profile.lastName}
                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-800 dark:text-gray-100"
                      placeholder="Enter your last name"
                    />
                  </div>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                    >Email</label
                  >
                  <input
                    type="email"
                    bind:value={settings.profile.email}
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-800 dark:text-gray-100"
                    placeholder="Enter your email"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                    >Bio</label
                  >
                  <textarea
                    bind:value={settings.profile.bio}
                    rows="4"
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-800 dark:text-gray-100"
                    placeholder="Tell us about yourself..."
                  ></textarea>
                </div>
              </div>
            {:else if selectedCategory === 'preferences'}
              <div class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                      >Language</label
                    >
                    <select
                      bind:value={settings.preferences.language}
                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-800 dark:text-gray-100"
                    >
                      <option value="en">English</option>
                      <option value="es">Español</option>
                      <option value="fr">Français</option>
                      <option value="de">Deutsch</option>
                      <option value="zh">中文</option>
                    </select>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                      >Timezone</label
                    >
                    <select
                      bind:value={settings.preferences.timezone}
                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-800 dark:text-gray-100"
                    >
                      <option value="UTC">UTC</option>
                      <option value="America/New_York">Eastern Time</option>
                      <option value="America/Chicago">Central Time</option>
                      <option value="America/Denver">Mountain Time</option>
                      <option value="America/Los_Angeles">Pacific Time</option>
                    </select>
                  </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                      >Date Format</label
                    >
                    <select
                      bind:value={settings.preferences.dateFormat}
                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-800 dark:text-gray-100"
                    >
                      <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                      <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                      <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                    </select>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                      >Start of Week</label
                    >
                    <select
                      bind:value={settings.preferences.startOfWeek}
                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-800 dark:text-gray-100"
                    >
                      <option value="sunday">Sunday</option>
                      <option value="monday">Monday</option>
                    </select>
                  </div>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-4"
                    >Theme</label
                  >
                  <div class="flex items-center space-x-4">
                    <ThemeToggle size="md" />
                    <span class="text-sm text-gray-600 dark:text-gray-400"
                      >Toggle between light and dark mode</span
                    >
                  </div>
                </div>
              </div>
            {:else if selectedCategory === 'notifications'}
              <div class="space-y-6">
                <div class="space-y-4">
                  <div class="flex items-center justify-between">
                    <div>
                      <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                        Email Notifications
                      </h3>
                      <p class="text-sm text-gray-500 dark:text-gray-400">
                        Receive notifications via email
                      </p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        bind:checked={settings.notifications.emailNotifications}
                        class="sr-only peer"
                      />
                      <div
                        class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-slate-300 dark:peer-focus:ring-slate-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-slate-600"
                      ></div>
                    </label>
                  </div>

                  <div class="flex items-center justify-between">
                    <div>
                      <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                        Push Notifications
                      </h3>
                      <p class="text-sm text-gray-500 dark:text-gray-400">
                        Receive push notifications in your browser
                      </p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        bind:checked={settings.notifications.pushNotifications}
                        class="sr-only peer"
                      />
                      <div
                        class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-slate-300 dark:peer-focus:ring-slate-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-slate-600"
                      ></div>
                    </label>
                  </div>

                  <div class="flex items-center justify-between">
                    <div>
                      <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                        Task Reminders
                      </h3>
                      <p class="text-sm text-gray-500 dark:text-gray-400">
                        Get reminded about upcoming tasks
                      </p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        bind:checked={settings.notifications.taskReminders}
                        class="sr-only peer"
                      />
                      <div
                        class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-slate-300 dark:peer-focus:ring-slate-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-slate-600"
                      ></div>
                    </label>
                  </div>

                  <div class="flex items-center justify-between">
                    <div>
                      <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                        Weekly Digest
                      </h3>
                      <p class="text-sm text-gray-500 dark:text-gray-400">
                        Receive a weekly summary of your activities
                      </p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        bind:checked={settings.notifications.weeklyDigest}
                        class="sr-only peer"
                      />
                      <div
                        class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-slate-300 dark:peer-focus:ring-slate-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-slate-600"
                      ></div>
                    </label>
                  </div>
                </div>
              </div>
            {:else if selectedCategory === 'privacy'}
              <div class="space-y-6">
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                    >Profile Visibility</label
                  >
                  <select
                    bind:value={settings.privacy.profileVisibility}
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-800 dark:text-gray-100"
                  >
                    <option value="public">Public</option>
                    <option value="private">Private</option>
                    <option value="friends">Friends Only</option>
                  </select>
                </div>

                <div class="space-y-4">
                  <div class="flex items-center justify-between">
                    <div>
                      <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                        Data Sharing
                      </h3>
                      <p class="text-sm text-gray-500 dark:text-gray-400">
                        Allow sharing of anonymized usage data
                      </p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        bind:checked={settings.privacy.dataSharing}
                        class="sr-only peer"
                      />
                      <div
                        class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-slate-300 dark:peer-focus:ring-slate-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-slate-600"
                      ></div>
                    </label>
                  </div>

                  <div class="flex items-center justify-between">
                    <div>
                      <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                        Analytics Opt-out
                      </h3>
                      <p class="text-sm text-gray-500 dark:text-gray-400">
                        Opt out of analytics tracking
                      </p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        bind:checked={settings.privacy.analyticsOptOut}
                        class="sr-only peer"
                      />
                      <div
                        class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-slate-300 dark:peer-focus:ring-slate-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-slate-600"
                      ></div>
                    </label>
                  </div>
                </div>
              </div>
            {:else if selectedCategory === 'data'}
              <div class="space-y-6">
                <div
                  class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4"
                >
                  <div class="flex">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-5 w-5 text-yellow-400 mr-3 mt-0.5"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                        clip-rule="evenodd"
                      />
                    </svg>
                    <div>
                      <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                        Data Management
                      </h3>
                      <p class="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                        These actions will affect all your data. Please proceed with caution.
                      </p>
                    </div>
                  </div>
                </div>

                <div class="space-y-4">
                  <div
                    class="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
                  >
                    <div>
                      <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                        Export Data
                      </h3>
                      <p class="text-sm text-gray-500 dark:text-gray-400">
                        Download all your data in JSON format
                      </p>
                    </div>
                    <button
                      onclick={exportData}
                      class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                    >
                      Export
                    </button>
                  </div>

                  <div
                    class="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
                  >
                    <div>
                      <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                        Import Data
                      </h3>
                      <p class="text-sm text-gray-500 dark:text-gray-400">
                        Import data from a previously exported file
                      </p>
                    </div>
                    <button
                      onclick={importData}
                      class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
                    >
                      Import
                    </button>
                  </div>

                  <div
                    class="flex items-center justify-between p-4 border border-red-200 dark:border-red-800 rounded-lg bg-red-50 dark:bg-red-900/20"
                  >
                    <div>
                      <h3 class="text-sm font-medium text-red-900 dark:text-red-100">
                        Reset All Settings
                      </h3>
                      <p class="text-sm text-red-700 dark:text-red-300">
                        Reset all settings to their default values
                      </p>
                    </div>
                    <button
                      onclick={resetSettings}
                      class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
                    >
                      Reset
                    </button>
                  </div>
                </div>
              </div>
            {/if}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
