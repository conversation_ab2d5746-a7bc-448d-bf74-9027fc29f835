#!/usr/bin/env python3
"""
Quick test script to verify the todo focus toggle fix
"""

import requests
import json

BASE_URL = "http://127.0.0.1:8000"

def test_focus_toggle():
    """Test the focus toggle functionality"""
    
    # Step 1: Register/Login
    email = "<EMAIL>"
    password = "testpass123"
    
    # Register
    register_data = {
        "username": "testuser",
        "password": password,
        "password_confirm": password,
        "email": email
    }
    
    register_response = requests.post(f"{BASE_URL}/api/v1/auth/register/", json=register_data)
    print(f"Register: {register_response.status_code}")
    
    # Login
    login_data = {
        "email": email,
        "password": password
    }
    
    login_response = requests.post(f"{BASE_URL}/api/v1/auth/login/", json=login_data)
    print(f"Login: {login_response.status_code}")
    
    if login_response.status_code != 200:
        print("Login failed!")
        return False
    
    token = login_response.json().get("access_token")
    headers = {
        'Authorization': f'Token {token}',
        'Content-Type': 'application/json'
    }
    
    # Step 2: Create a todo
    todo_data = {
        "title": "Test Focus Toggle",
        "description": "Testing focus toggle functionality",
        "priority": "high"
    }
    
    create_response = requests.post(f"{BASE_URL}/api/v1/todo/todos/", json=todo_data, headers=headers)
    print(f"Create todo: {create_response.status_code}")
    
    if create_response.status_code != 201:
        print("Todo creation failed!")
        return False
    
    todo = create_response.json()
    todo_id = todo["id"]
    print(f"Created todo with ID: {todo_id}")
    
    # Step 3: Toggle focus to True
    focus_data = {"is_current_focus": True}
    
    focus_response = requests.put(f"{BASE_URL}/api/v1/todo/todos/{todo_id}/", json=focus_data, headers=headers)
    print(f"Set focus: {focus_response.status_code}")
    
    if focus_response.status_code != 200:
        print(f"Focus toggle failed: {focus_response.text}")
        return False
    
    updated_todo = focus_response.json()
    if updated_todo["is_current_focus"] != True:
        print("Focus was not set correctly!")
        return False
    
    print("✅ Focus toggle test PASSED!")
    
    # Step 4: Toggle focus to False
    unfocus_data = {"is_current_focus": False}
    
    unfocus_response = requests.put(f"{BASE_URL}/api/v1/todo/todos/{todo_id}/", json=unfocus_data, headers=headers)
    print(f"Remove focus: {unfocus_response.status_code}")
    
    if unfocus_response.status_code != 200:
        print(f"Focus removal failed: {unfocus_response.text}")
        return False
    
    updated_todo = unfocus_response.json()
    if updated_todo["is_current_focus"] != False:
        print("Focus was not removed correctly!")
        return False
    
    print("✅ Focus removal test PASSED!")
    return True

if __name__ == "__main__":
    print("Testing todo focus toggle fix...")
    success = test_focus_toggle()
    if success:
        print("\n🎉 All tests passed! The focus toggle fix is working.")
    else:
        print("\n❌ Tests failed. There are still issues.")
