import React, { useEffect, useState } from 'react';
import { TaskLayout } from '../components/layout/TaskLayout';
import { usePlanStore } from '../store/planStore';
import { Button } from '../components/ui/Button';
import { PlanModal } from '../components/plan/PlanModal';
import { formatDate } from '../lib/utils';
import type { FuturePlan, GoalType, PlanStatus } from '../services/planService';

// Goal type colors
const getGoalTypeColor = (goalType: GoalType | null | undefined) => {
  switch (goalType) {
    case 'career':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
    case 'skill_development':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
    case 'personal':
      return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
    case 'project':
      return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
    case 'long_term':
      return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200';
    case 'short_term':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
  }
};

// Status colors
const getStatusColor = (status: PlanStatus) => {
  switch (status) {
    case 'achieved':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
    case 'active':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
    case 'deferred':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
    case 'abandoned':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
  }
};

// Plan List Item Component
interface PlanListItemProps {
  plan: FuturePlan;
  isSelected: boolean;
  onClick: () => void;
}

const PlanListItem: React.FC<PlanListItemProps> = ({
  plan,
  isSelected,
  onClick,
}) => {
  const isOverdue = plan.target_date && new Date(plan.target_date) < new Date() && plan.status === 'active';

  return (
    <div
      onClick={onClick}
      className={`p-4 rounded-lg border cursor-pointer transition-all ${
        isSelected
          ? 'border-purple-500 bg-purple-50 dark:bg-purple-900/20 dark:border-purple-400'
          : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 bg-white dark:bg-gray-800'
      }`}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          <h3 className="font-medium text-gray-900 dark:text-white truncate">
            {plan.title}
          </h3>

          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">
            {plan.description}
          </p>

          <div className="flex flex-wrap gap-1 mt-2">
            {plan.goal_type && (
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getGoalTypeColor(plan.goal_type)}`}>
                {plan.goal_type.replace('_', ' ')}
              </span>
            )}

            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(plan.status)}`}>
              {plan.status}
            </span>

            {plan.target_date && (
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                isOverdue
                  ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                  : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
              }`}>
                {isOverdue ? 'Overdue' : `Target: ${formatDate(plan.target_date)}`}
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

// Plan Details Component
interface PlanDetailsProps {
  plan: FuturePlan;
  onEdit: () => void;
  onDelete: () => void;
}

const PlanDetails: React.FC<PlanDetailsProps> = ({
  plan,
  onEdit,
  onDelete,
}) => {
  const isOverdue = plan.target_date && new Date(plan.target_date) < new Date() && plan.status === 'active';

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {plan.title}
            </h1>

            <div className="flex flex-wrap gap-2 mt-3">
              {plan.goal_type && (
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getGoalTypeColor(plan.goal_type)}`}>
                  {plan.goal_type.replace('_', ' ')}
                </span>
              )}

              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(plan.status)}`}>
                {plan.status}
              </span>

              {plan.target_date && (
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                  isOverdue
                    ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                    : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                }`}>
                  {isOverdue ? `Overdue since ${formatDate(plan.target_date)}` : `Target: ${formatDate(plan.target_date)}`}
                </span>
              )}
            </div>
          </div>

          <div className="flex space-x-2 ml-4">
            <Button onClick={onEdit} variant="secondary" size="sm">
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
              Edit
            </Button>
            <Button onClick={onDelete} variant="danger" size="sm">
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
              Delete
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-6 space-y-6">
        {/* Description */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
            Description
          </h3>
          <div className="prose dark:prose-invert max-w-none">
            <p className="text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
              {plan.description}
            </p>
          </div>
        </div>

        {/* Progress Indicator */}
        {plan.status === 'active' && (
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
              📈 Active Plan
            </h4>
            <p className="text-blue-800 dark:text-blue-200 text-sm">
              This plan is currently active. Keep working towards your goal!
              {plan.target_date && (
                <span className="block mt-1">
                  {isOverdue
                    ? `⚠️ This plan was due on ${formatDate(plan.target_date)}`
                    : `🎯 Target completion: ${formatDate(plan.target_date)}`
                  }
                </span>
              )}
            </p>
          </div>
        )}

        {plan.status === 'achieved' && (
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
            <h4 className="font-medium text-green-900 dark:text-green-100 mb-2">
              🎉 Achieved!
            </h4>
            <p className="text-green-800 dark:text-green-200 text-sm">
              Congratulations! You've successfully completed this plan.
            </p>
          </div>
        )}

        {/* Metadata */}
        <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
            Details
          </h3>
          <dl className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div>
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Created</dt>
              <dd className="text-sm text-gray-900 dark:text-white">
                {formatDate(plan.created_at)}
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Last Updated</dt>
              <dd className="text-sm text-gray-900 dark:text-white">
                {formatDate(plan.updated_at)}
              </dd>
            </div>
            {plan.target_date && (
              <div>
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Target Date</dt>
                <dd className="text-sm text-gray-900 dark:text-white">
                  {formatDate(plan.target_date)}
                </dd>
              </div>
            )}
            <div>
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Status</dt>
              <dd className="text-sm text-gray-900 dark:text-white capitalize">
                {plan.status}
              </dd>
            </div>
          </dl>
        </div>
      </div>
    </div>
  );
};

export const PlanPage: React.FC = () => {
  const {
    plans,
    selectedPlan,
    isLoading,
    error,
    fetchPlans,
    selectPlan,
    addPlan,
    updatePlan,
    deletePlan,
  } = usePlanStore();

  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  useEffect(() => {
    fetchPlans();
  }, [fetchPlans]);

  const handleAddPlan = () => {
    setIsAddModalOpen(true);
  };

  const handleEditPlan = () => {
    if (selectedPlan) {
      setIsEditModalOpen(true);
    }
  };

  const handleDeletePlan = async () => {
    if (selectedPlan && window.confirm('Are you sure you want to delete this plan?')) {
      await deletePlan(selectedPlan.id);
      selectPlan(null);
    }
  };

  // Left Panel - Plan List
  const leftPanel = (
    <div className="space-y-2 p-4">
      {plans.length === 0 ? (
        <div className="text-center py-8 text-gray-500 dark:text-gray-400">
          <p>No plans yet.</p>
          <p className="text-sm">Add your first plan to get started!</p>
        </div>
      ) : (
        plans.map((plan) => (
          <PlanListItem
            key={plan.id}
            plan={plan}
            isSelected={selectedPlan?.id === plan.id}
            onClick={() => selectPlan(plan)}
          />
        ))
      )}
    </div>
  );

  // Right Panel - Plan Details
  const rightPanel = selectedPlan ? (
    <PlanDetails
      plan={selectedPlan}
      onEdit={handleEditPlan}
      onDelete={handleDeletePlan}
    />
  ) : (
    <div className="flex items-center justify-center h-full text-gray-500 dark:text-gray-400">
      <div className="text-center">
        <svg className="w-16 h-16 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 5H7a2 2 0 00-2 2v6a2 2 0 002 2h6a2 2 0 002-2V7a2 2 0 00-2-2H9z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 9h6m-6 4h6m2 5l-8-8 8-8" />
        </svg>
        <p className="text-lg font-medium">Select a plan</p>
        <p className="text-sm">Choose a plan from the list to view details</p>
      </div>
    </div>
  );

  return (
    <>
      <TaskLayout
        title="Plans"
        icon="📋"
        leftPanel={leftPanel}
        rightPanel={rightPanel}
        isLoading={isLoading}
        error={error}
        onAddNew={handleAddPlan}
        addButtonText="Add Plan"
      />

      {/* Add Plan Modal */}
      <PlanModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onSubmit={async (data) => {
          await addPlan(data);
          setIsAddModalOpen(false);
        }}
        title="Add New Plan"
      />

      {/* Edit Plan Modal */}
      {selectedPlan && (
        <PlanModal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          onSubmit={async (data) => {
            // Convert CreatePlanData to UpdatePlanData for editing
            await updatePlan(selectedPlan.id, {
              title: data.title,
              description: data.description,
              goal_type: data.goal_type,
              target_date: data.target_date,
            });
            setIsEditModalOpen(false);
          }}
          title="Edit Plan"
          initialData={selectedPlan}
        />
      )}
    </>
  );
};
