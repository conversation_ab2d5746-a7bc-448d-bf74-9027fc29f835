var ap=Object.defineProperty;var op=(e,t,r)=>t in e?ap(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;var hu=(e,t,r)=>op(e,typeof t!="symbol"?t+"":t,r);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const l of document.querySelectorAll('link[rel="modulepreload"]'))n(l);new MutationObserver(l=>{for(const a of l)if(a.type==="childList")for(const o of a.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&n(o)}).observe(document,{childList:!0,subtree:!0});function r(l){const a={};return l.integrity&&(a.integrity=l.integrity),l.referrerPolicy&&(a.referrerPolicy=l.referrerPolicy),l.crossOrigin==="use-credentials"?a.credentials="include":l.crossOrigin==="anonymous"?a.credentials="omit":a.credentials="same-origin",a}function n(l){if(l.ep)return;l.ep=!0;const a=r(l);fetch(l.href,a)}})();function ip(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var td={exports:{}},Va={},rd={exports:{}},Y={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var kl=Symbol.for("react.element"),sp=Symbol.for("react.portal"),up=Symbol.for("react.fragment"),cp=Symbol.for("react.strict_mode"),dp=Symbol.for("react.profiler"),fp=Symbol.for("react.provider"),hp=Symbol.for("react.context"),pp=Symbol.for("react.forward_ref"),mp=Symbol.for("react.suspense"),gp=Symbol.for("react.memo"),yp=Symbol.for("react.lazy"),pu=Symbol.iterator;function vp(e){return e===null||typeof e!="object"?null:(e=pu&&e[pu]||e["@@iterator"],typeof e=="function"?e:null)}var nd={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},ld=Object.assign,ad={};function vn(e,t,r){this.props=e,this.context=t,this.refs=ad,this.updater=r||nd}vn.prototype.isReactComponent={};vn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};vn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function od(){}od.prototype=vn.prototype;function Xi(e,t,r){this.props=e,this.context=t,this.refs=ad,this.updater=r||nd}var Ji=Xi.prototype=new od;Ji.constructor=Xi;ld(Ji,vn.prototype);Ji.isPureReactComponent=!0;var mu=Array.isArray,id=Object.prototype.hasOwnProperty,Zi={current:null},sd={key:!0,ref:!0,__self:!0,__source:!0};function ud(e,t,r){var n,l={},a=null,o=null;if(t!=null)for(n in t.ref!==void 0&&(o=t.ref),t.key!==void 0&&(a=""+t.key),t)id.call(t,n)&&!sd.hasOwnProperty(n)&&(l[n]=t[n]);var i=arguments.length-2;if(i===1)l.children=r;else if(1<i){for(var s=Array(i),c=0;c<i;c++)s[c]=arguments[c+2];l.children=s}if(e&&e.defaultProps)for(n in i=e.defaultProps,i)l[n]===void 0&&(l[n]=i[n]);return{$$typeof:kl,type:e,key:a,ref:o,props:l,_owner:Zi.current}}function xp(e,t){return{$$typeof:kl,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function qi(e){return typeof e=="object"&&e!==null&&e.$$typeof===kl}function wp(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(r){return t[r]})}var gu=/\/+/g;function wo(e,t){return typeof e=="object"&&e!==null&&e.key!=null?wp(""+e.key):t.toString(36)}function ra(e,t,r,n,l){var a=typeof e;(a==="undefined"||a==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(a){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case kl:case sp:o=!0}}if(o)return o=e,l=l(o),e=n===""?"."+wo(o,0):n,mu(l)?(r="",e!=null&&(r=e.replace(gu,"$&/")+"/"),ra(l,t,r,"",function(c){return c})):l!=null&&(qi(l)&&(l=xp(l,r+(!l.key||o&&o.key===l.key?"":(""+l.key).replace(gu,"$&/")+"/")+e)),t.push(l)),1;if(o=0,n=n===""?".":n+":",mu(e))for(var i=0;i<e.length;i++){a=e[i];var s=n+wo(a,i);o+=ra(a,t,r,s,l)}else if(s=vp(e),typeof s=="function")for(e=s.call(e),i=0;!(a=e.next()).done;)a=a.value,s=n+wo(a,i++),o+=ra(a,t,r,s,l);else if(a==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function Fl(e,t,r){if(e==null)return e;var n=[],l=0;return ra(e,n,"","",function(a){return t.call(r,a,l++)}),n}function kp(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(r){(e._status===0||e._status===-1)&&(e._status=1,e._result=r)},function(r){(e._status===0||e._status===-1)&&(e._status=2,e._result=r)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Ue={current:null},na={transition:null},Sp={ReactCurrentDispatcher:Ue,ReactCurrentBatchConfig:na,ReactCurrentOwner:Zi};function cd(){throw Error("act(...) is not supported in production builds of React.")}Y.Children={map:Fl,forEach:function(e,t,r){Fl(e,function(){t.apply(this,arguments)},r)},count:function(e){var t=0;return Fl(e,function(){t++}),t},toArray:function(e){return Fl(e,function(t){return t})||[]},only:function(e){if(!qi(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};Y.Component=vn;Y.Fragment=up;Y.Profiler=dp;Y.PureComponent=Xi;Y.StrictMode=cp;Y.Suspense=mp;Y.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Sp;Y.act=cd;Y.cloneElement=function(e,t,r){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var n=ld({},e.props),l=e.key,a=e.ref,o=e._owner;if(t!=null){if(t.ref!==void 0&&(a=t.ref,o=Zi.current),t.key!==void 0&&(l=""+t.key),e.type&&e.type.defaultProps)var i=e.type.defaultProps;for(s in t)id.call(t,s)&&!sd.hasOwnProperty(s)&&(n[s]=t[s]===void 0&&i!==void 0?i[s]:t[s])}var s=arguments.length-2;if(s===1)n.children=r;else if(1<s){i=Array(s);for(var c=0;c<s;c++)i[c]=arguments[c+2];n.children=i}return{$$typeof:kl,type:e.type,key:l,ref:a,props:n,_owner:o}};Y.createContext=function(e){return e={$$typeof:hp,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:fp,_context:e},e.Consumer=e};Y.createElement=ud;Y.createFactory=function(e){var t=ud.bind(null,e);return t.type=e,t};Y.createRef=function(){return{current:null}};Y.forwardRef=function(e){return{$$typeof:pp,render:e}};Y.isValidElement=qi;Y.lazy=function(e){return{$$typeof:yp,_payload:{_status:-1,_result:e},_init:kp}};Y.memo=function(e,t){return{$$typeof:gp,type:e,compare:t===void 0?null:t}};Y.startTransition=function(e){var t=na.transition;na.transition={};try{e()}finally{na.transition=t}};Y.unstable_act=cd;Y.useCallback=function(e,t){return Ue.current.useCallback(e,t)};Y.useContext=function(e){return Ue.current.useContext(e)};Y.useDebugValue=function(){};Y.useDeferredValue=function(e){return Ue.current.useDeferredValue(e)};Y.useEffect=function(e,t){return Ue.current.useEffect(e,t)};Y.useId=function(){return Ue.current.useId()};Y.useImperativeHandle=function(e,t,r){return Ue.current.useImperativeHandle(e,t,r)};Y.useInsertionEffect=function(e,t){return Ue.current.useInsertionEffect(e,t)};Y.useLayoutEffect=function(e,t){return Ue.current.useLayoutEffect(e,t)};Y.useMemo=function(e,t){return Ue.current.useMemo(e,t)};Y.useReducer=function(e,t,r){return Ue.current.useReducer(e,t,r)};Y.useRef=function(e){return Ue.current.useRef(e)};Y.useState=function(e){return Ue.current.useState(e)};Y.useSyncExternalStore=function(e,t,r){return Ue.current.useSyncExternalStore(e,t,r)};Y.useTransition=function(){return Ue.current.useTransition()};Y.version="18.3.1";rd.exports=Y;var j=rd.exports;const Jo=ip(j);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var jp=j,Ep=Symbol.for("react.element"),Np=Symbol.for("react.fragment"),Cp=Object.prototype.hasOwnProperty,_p=jp.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Lp={key:!0,ref:!0,__self:!0,__source:!0};function dd(e,t,r){var n,l={},a=null,o=null;r!==void 0&&(a=""+r),t.key!==void 0&&(a=""+t.key),t.ref!==void 0&&(o=t.ref);for(n in t)Cp.call(t,n)&&!Lp.hasOwnProperty(n)&&(l[n]=t[n]);if(e&&e.defaultProps)for(n in t=e.defaultProps,t)l[n]===void 0&&(l[n]=t[n]);return{$$typeof:Ep,type:e,key:a,ref:o,props:l,_owner:_p.current}}Va.Fragment=Np;Va.jsx=dd;Va.jsxs=dd;td.exports=Va;var u=td.exports,Zo={},fd={exports:{}},nt={},hd={exports:{}},pd={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(T,D){var I=T.length;T.push(D);e:for(;0<I;){var G=I-1>>>1,ee=T[G];if(0<l(ee,D))T[G]=D,T[I]=ee,I=G;else break e}}function r(T){return T.length===0?null:T[0]}function n(T){if(T.length===0)return null;var D=T[0],I=T.pop();if(I!==D){T[0]=I;e:for(var G=0,ee=T.length,ge=ee>>>1;G<ge;){var De=2*(G+1)-1,Bt=T[De],Oe=De+1,Ar=T[Oe];if(0>l(Bt,I))Oe<ee&&0>l(Ar,Bt)?(T[G]=Ar,T[Oe]=I,G=Oe):(T[G]=Bt,T[De]=I,G=De);else if(Oe<ee&&0>l(Ar,I))T[G]=Ar,T[Oe]=I,G=Oe;else break e}}return D}function l(T,D){var I=T.sortIndex-D.sortIndex;return I!==0?I:T.id-D.id}if(typeof performance=="object"&&typeof performance.now=="function"){var a=performance;e.unstable_now=function(){return a.now()}}else{var o=Date,i=o.now();e.unstable_now=function(){return o.now()-i}}var s=[],c=[],d=1,f=null,p=3,x=!1,S=!1,v=!1,_=typeof setTimeout=="function"?setTimeout:null,h=typeof clearTimeout=="function"?clearTimeout:null,m=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function g(T){for(var D=r(c);D!==null;){if(D.callback===null)n(c);else if(D.startTime<=T)n(c),D.sortIndex=D.expirationTime,t(s,D);else break;D=r(c)}}function E(T){if(v=!1,g(T),!S)if(r(s)!==null)S=!0,q(L);else{var D=r(c);D!==null&&we(E,D.startTime-T)}}function L(T,D){S=!1,v&&(v=!1,h(R),R=-1),x=!0;var I=p;try{for(g(D),f=r(s);f!==null&&(!(f.expirationTime>D)||T&&!se());){var G=f.callback;if(typeof G=="function"){f.callback=null,p=f.priorityLevel;var ee=G(f.expirationTime<=D);D=e.unstable_now(),typeof ee=="function"?f.callback=ee:f===r(s)&&n(s),g(D)}else n(s);f=r(s)}if(f!==null)var ge=!0;else{var De=r(c);De!==null&&we(E,De.startTime-D),ge=!1}return ge}finally{f=null,p=I,x=!1}}var w=!1,C=null,R=-1,O=5,B=-1;function se(){return!(e.unstable_now()-B<O)}function Ye(){if(C!==null){var T=e.unstable_now();B=T;var D=!0;try{D=C(!0,T)}finally{D?Ge():(w=!1,C=null)}}else w=!1}var Ge;if(typeof m=="function")Ge=function(){m(Ye)};else if(typeof MessageChannel<"u"){var Z=new MessageChannel,fe=Z.port2;Z.port1.onmessage=Ye,Ge=function(){fe.postMessage(null)}}else Ge=function(){_(Ye,0)};function q(T){C=T,w||(w=!0,Ge())}function we(T,D){R=_(function(){T(e.unstable_now())},D)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(T){T.callback=null},e.unstable_continueExecution=function(){S||x||(S=!0,q(L))},e.unstable_forceFrameRate=function(T){0>T||125<T?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):O=0<T?Math.floor(1e3/T):5},e.unstable_getCurrentPriorityLevel=function(){return p},e.unstable_getFirstCallbackNode=function(){return r(s)},e.unstable_next=function(T){switch(p){case 1:case 2:case 3:var D=3;break;default:D=p}var I=p;p=D;try{return T()}finally{p=I}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(T,D){switch(T){case 1:case 2:case 3:case 4:case 5:break;default:T=3}var I=p;p=T;try{return D()}finally{p=I}},e.unstable_scheduleCallback=function(T,D,I){var G=e.unstable_now();switch(typeof I=="object"&&I!==null?(I=I.delay,I=typeof I=="number"&&0<I?G+I:G):I=G,T){case 1:var ee=-1;break;case 2:ee=250;break;case 5:ee=**********;break;case 4:ee=1e4;break;default:ee=5e3}return ee=I+ee,T={id:d++,callback:D,priorityLevel:T,startTime:I,expirationTime:ee,sortIndex:-1},I>G?(T.sortIndex=I,t(c,T),r(s)===null&&T===r(c)&&(v?(h(R),R=-1):v=!0,we(E,I-G))):(T.sortIndex=ee,t(s,T),S||x||(S=!0,q(L))),T},e.unstable_shouldYield=se,e.unstable_wrapCallback=function(T){var D=p;return function(){var I=p;p=D;try{return T.apply(this,arguments)}finally{p=I}}}})(pd);hd.exports=pd;var Pp=hd.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var bp=j,rt=Pp;function b(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,r=1;r<arguments.length;r++)t+="&args[]="+encodeURIComponent(arguments[r]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var md=new Set,rl={};function Fr(e,t){cn(e,t),cn(e+"Capture",t)}function cn(e,t){for(rl[e]=t,e=0;e<t.length;e++)md.add(t[e])}var Dt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),qo=Object.prototype.hasOwnProperty,Rp=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,yu={},vu={};function Tp(e){return qo.call(vu,e)?!0:qo.call(yu,e)?!1:Rp.test(e)?vu[e]=!0:(yu[e]=!0,!1)}function Mp(e,t,r,n){if(r!==null&&r.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return n?!1:r!==null?!r.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function zp(e,t,r,n){if(t===null||typeof t>"u"||Mp(e,t,r,n))return!0;if(n)return!1;if(r!==null)switch(r.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Be(e,t,r,n,l,a,o){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=n,this.attributeNamespace=l,this.mustUseProperty=r,this.propertyName=e,this.type=t,this.sanitizeURL=a,this.removeEmptyString=o}var be={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){be[e]=new Be(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];be[t]=new Be(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){be[e]=new Be(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){be[e]=new Be(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){be[e]=new Be(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){be[e]=new Be(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){be[e]=new Be(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){be[e]=new Be(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){be[e]=new Be(e,5,!1,e.toLowerCase(),null,!1,!1)});var es=/[\-:]([a-z])/g;function ts(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(es,ts);be[t]=new Be(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(es,ts);be[t]=new Be(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(es,ts);be[t]=new Be(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){be[e]=new Be(e,1,!1,e.toLowerCase(),null,!1,!1)});be.xlinkHref=new Be("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){be[e]=new Be(e,1,!1,e.toLowerCase(),null,!0,!0)});function rs(e,t,r,n){var l=be.hasOwnProperty(t)?be[t]:null;(l!==null?l.type!==0:n||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(zp(t,r,l,n)&&(r=null),n||l===null?Tp(t)&&(r===null?e.removeAttribute(t):e.setAttribute(t,""+r)):l.mustUseProperty?e[l.propertyName]=r===null?l.type===3?!1:"":r:(t=l.attributeName,n=l.attributeNamespace,r===null?e.removeAttribute(t):(l=l.type,r=l===3||l===4&&r===!0?"":""+r,n?e.setAttributeNS(n,t,r):e.setAttribute(t,r))))}var $t=bp.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Dl=Symbol.for("react.element"),Wr=Symbol.for("react.portal"),Vr=Symbol.for("react.fragment"),ns=Symbol.for("react.strict_mode"),ei=Symbol.for("react.profiler"),gd=Symbol.for("react.provider"),yd=Symbol.for("react.context"),ls=Symbol.for("react.forward_ref"),ti=Symbol.for("react.suspense"),ri=Symbol.for("react.suspense_list"),as=Symbol.for("react.memo"),Yt=Symbol.for("react.lazy"),vd=Symbol.for("react.offscreen"),xu=Symbol.iterator;function _n(e){return e===null||typeof e!="object"?null:(e=xu&&e[xu]||e["@@iterator"],typeof e=="function"?e:null)}var de=Object.assign,ko;function $n(e){if(ko===void 0)try{throw Error()}catch(r){var t=r.stack.trim().match(/\n( *(at )?)/);ko=t&&t[1]||""}return`
`+ko+e}var So=!1;function jo(e,t){if(!e||So)return"";So=!0;var r=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var n=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){n=c}e.call(t.prototype)}else{try{throw Error()}catch(c){n=c}e()}}catch(c){if(c&&n&&typeof c.stack=="string"){for(var l=c.stack.split(`
`),a=n.stack.split(`
`),o=l.length-1,i=a.length-1;1<=o&&0<=i&&l[o]!==a[i];)i--;for(;1<=o&&0<=i;o--,i--)if(l[o]!==a[i]){if(o!==1||i!==1)do if(o--,i--,0>i||l[o]!==a[i]){var s=`
`+l[o].replace(" at new "," at ");return e.displayName&&s.includes("<anonymous>")&&(s=s.replace("<anonymous>",e.displayName)),s}while(1<=o&&0<=i);break}}}finally{So=!1,Error.prepareStackTrace=r}return(e=e?e.displayName||e.name:"")?$n(e):""}function Fp(e){switch(e.tag){case 5:return $n(e.type);case 16:return $n("Lazy");case 13:return $n("Suspense");case 19:return $n("SuspenseList");case 0:case 2:case 15:return e=jo(e.type,!1),e;case 11:return e=jo(e.type.render,!1),e;case 1:return e=jo(e.type,!0),e;default:return""}}function ni(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Vr:return"Fragment";case Wr:return"Portal";case ei:return"Profiler";case ns:return"StrictMode";case ti:return"Suspense";case ri:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case yd:return(e.displayName||"Context")+".Consumer";case gd:return(e._context.displayName||"Context")+".Provider";case ls:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case as:return t=e.displayName||null,t!==null?t:ni(e.type)||"Memo";case Yt:t=e._payload,e=e._init;try{return ni(e(t))}catch{}}return null}function Dp(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return ni(t);case 8:return t===ns?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function ur(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function xd(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Op(e){var t=xd(e)?"checked":"value",r=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),n=""+e[t];if(!e.hasOwnProperty(t)&&typeof r<"u"&&typeof r.get=="function"&&typeof r.set=="function"){var l=r.get,a=r.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return l.call(this)},set:function(o){n=""+o,a.call(this,o)}}),Object.defineProperty(e,t,{enumerable:r.enumerable}),{getValue:function(){return n},setValue:function(o){n=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Ol(e){e._valueTracker||(e._valueTracker=Op(e))}function wd(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var r=t.getValue(),n="";return e&&(n=xd(e)?e.checked?"true":"false":e.value),e=n,e!==r?(t.setValue(e),!0):!1}function ya(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function li(e,t){var r=t.checked;return de({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:r??e._wrapperState.initialChecked})}function wu(e,t){var r=t.defaultValue==null?"":t.defaultValue,n=t.checked!=null?t.checked:t.defaultChecked;r=ur(t.value!=null?t.value:r),e._wrapperState={initialChecked:n,initialValue:r,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function kd(e,t){t=t.checked,t!=null&&rs(e,"checked",t,!1)}function ai(e,t){kd(e,t);var r=ur(t.value),n=t.type;if(r!=null)n==="number"?(r===0&&e.value===""||e.value!=r)&&(e.value=""+r):e.value!==""+r&&(e.value=""+r);else if(n==="submit"||n==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?oi(e,t.type,r):t.hasOwnProperty("defaultValue")&&oi(e,t.type,ur(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function ku(e,t,r){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var n=t.type;if(!(n!=="submit"&&n!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,r||t===e.value||(e.value=t),e.defaultValue=t}r=e.name,r!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,r!==""&&(e.name=r)}function oi(e,t,r){(t!=="number"||ya(e.ownerDocument)!==e)&&(r==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+r&&(e.defaultValue=""+r))}var Un=Array.isArray;function rn(e,t,r,n){if(e=e.options,t){t={};for(var l=0;l<r.length;l++)t["$"+r[l]]=!0;for(r=0;r<e.length;r++)l=t.hasOwnProperty("$"+e[r].value),e[r].selected!==l&&(e[r].selected=l),l&&n&&(e[r].defaultSelected=!0)}else{for(r=""+ur(r),t=null,l=0;l<e.length;l++){if(e[l].value===r){e[l].selected=!0,n&&(e[l].defaultSelected=!0);return}t!==null||e[l].disabled||(t=e[l])}t!==null&&(t.selected=!0)}}function ii(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(b(91));return de({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Su(e,t){var r=t.value;if(r==null){if(r=t.children,t=t.defaultValue,r!=null){if(t!=null)throw Error(b(92));if(Un(r)){if(1<r.length)throw Error(b(93));r=r[0]}t=r}t==null&&(t=""),r=t}e._wrapperState={initialValue:ur(r)}}function Sd(e,t){var r=ur(t.value),n=ur(t.defaultValue);r!=null&&(r=""+r,r!==e.value&&(e.value=r),t.defaultValue==null&&e.defaultValue!==r&&(e.defaultValue=r)),n!=null&&(e.defaultValue=""+n)}function ju(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function jd(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function si(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?jd(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Al,Ed=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,r,n,l){MSApp.execUnsafeLocalFunction(function(){return e(t,r,n,l)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Al=Al||document.createElement("div"),Al.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Al.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function nl(e,t){if(t){var r=e.firstChild;if(r&&r===e.lastChild&&r.nodeType===3){r.nodeValue=t;return}}e.textContent=t}var Kn={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Ap=["Webkit","ms","Moz","O"];Object.keys(Kn).forEach(function(e){Ap.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Kn[t]=Kn[e]})});function Nd(e,t,r){return t==null||typeof t=="boolean"||t===""?"":r||typeof t!="number"||t===0||Kn.hasOwnProperty(e)&&Kn[e]?(""+t).trim():t+"px"}function Cd(e,t){e=e.style;for(var r in t)if(t.hasOwnProperty(r)){var n=r.indexOf("--")===0,l=Nd(r,t[r],n);r==="float"&&(r="cssFloat"),n?e.setProperty(r,l):e[r]=l}}var Ip=de({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ui(e,t){if(t){if(Ip[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(b(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(b(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(b(61))}if(t.style!=null&&typeof t.style!="object")throw Error(b(62))}}function ci(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var di=null;function os(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var fi=null,nn=null,ln=null;function Eu(e){if(e=El(e)){if(typeof fi!="function")throw Error(b(280));var t=e.stateNode;t&&(t=Xa(t),fi(e.stateNode,e.type,t))}}function _d(e){nn?ln?ln.push(e):ln=[e]:nn=e}function Ld(){if(nn){var e=nn,t=ln;if(ln=nn=null,Eu(e),t)for(e=0;e<t.length;e++)Eu(t[e])}}function Pd(e,t){return e(t)}function bd(){}var Eo=!1;function Rd(e,t,r){if(Eo)return e(t,r);Eo=!0;try{return Pd(e,t,r)}finally{Eo=!1,(nn!==null||ln!==null)&&(bd(),Ld())}}function ll(e,t){var r=e.stateNode;if(r===null)return null;var n=Xa(r);if(n===null)return null;r=n[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(n=!n.disabled)||(e=e.type,n=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!n;break e;default:e=!1}if(e)return null;if(r&&typeof r!="function")throw Error(b(231,t,typeof r));return r}var hi=!1;if(Dt)try{var Ln={};Object.defineProperty(Ln,"passive",{get:function(){hi=!0}}),window.addEventListener("test",Ln,Ln),window.removeEventListener("test",Ln,Ln)}catch{hi=!1}function $p(e,t,r,n,l,a,o,i,s){var c=Array.prototype.slice.call(arguments,3);try{t.apply(r,c)}catch(d){this.onError(d)}}var Yn=!1,va=null,xa=!1,pi=null,Up={onError:function(e){Yn=!0,va=e}};function Bp(e,t,r,n,l,a,o,i,s){Yn=!1,va=null,$p.apply(Up,arguments)}function Hp(e,t,r,n,l,a,o,i,s){if(Bp.apply(this,arguments),Yn){if(Yn){var c=va;Yn=!1,va=null}else throw Error(b(198));xa||(xa=!0,pi=c)}}function Dr(e){var t=e,r=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(r=t.return),e=t.return;while(e)}return t.tag===3?r:null}function Td(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Nu(e){if(Dr(e)!==e)throw Error(b(188))}function Wp(e){var t=e.alternate;if(!t){if(t=Dr(e),t===null)throw Error(b(188));return t!==e?null:e}for(var r=e,n=t;;){var l=r.return;if(l===null)break;var a=l.alternate;if(a===null){if(n=l.return,n!==null){r=n;continue}break}if(l.child===a.child){for(a=l.child;a;){if(a===r)return Nu(l),e;if(a===n)return Nu(l),t;a=a.sibling}throw Error(b(188))}if(r.return!==n.return)r=l,n=a;else{for(var o=!1,i=l.child;i;){if(i===r){o=!0,r=l,n=a;break}if(i===n){o=!0,n=l,r=a;break}i=i.sibling}if(!o){for(i=a.child;i;){if(i===r){o=!0,r=a,n=l;break}if(i===n){o=!0,n=a,r=l;break}i=i.sibling}if(!o)throw Error(b(189))}}if(r.alternate!==n)throw Error(b(190))}if(r.tag!==3)throw Error(b(188));return r.stateNode.current===r?e:t}function Md(e){return e=Wp(e),e!==null?zd(e):null}function zd(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=zd(e);if(t!==null)return t;e=e.sibling}return null}var Fd=rt.unstable_scheduleCallback,Cu=rt.unstable_cancelCallback,Vp=rt.unstable_shouldYield,Qp=rt.unstable_requestPaint,pe=rt.unstable_now,Kp=rt.unstable_getCurrentPriorityLevel,is=rt.unstable_ImmediatePriority,Dd=rt.unstable_UserBlockingPriority,wa=rt.unstable_NormalPriority,Yp=rt.unstable_LowPriority,Od=rt.unstable_IdlePriority,Qa=null,Ct=null;function Gp(e){if(Ct&&typeof Ct.onCommitFiberRoot=="function")try{Ct.onCommitFiberRoot(Qa,e,void 0,(e.current.flags&128)===128)}catch{}}var xt=Math.clz32?Math.clz32:Zp,Xp=Math.log,Jp=Math.LN2;function Zp(e){return e>>>=0,e===0?32:31-(Xp(e)/Jp|0)|0}var Il=64,$l=4194304;function Bn(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ka(e,t){var r=e.pendingLanes;if(r===0)return 0;var n=0,l=e.suspendedLanes,a=e.pingedLanes,o=r&268435455;if(o!==0){var i=o&~l;i!==0?n=Bn(i):(a&=o,a!==0&&(n=Bn(a)))}else o=r&~l,o!==0?n=Bn(o):a!==0&&(n=Bn(a));if(n===0)return 0;if(t!==0&&t!==n&&!(t&l)&&(l=n&-n,a=t&-t,l>=a||l===16&&(a&4194240)!==0))return t;if(n&4&&(n|=r&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=n;0<t;)r=31-xt(t),l=1<<r,n|=e[r],t&=~l;return n}function qp(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function em(e,t){for(var r=e.suspendedLanes,n=e.pingedLanes,l=e.expirationTimes,a=e.pendingLanes;0<a;){var o=31-xt(a),i=1<<o,s=l[o];s===-1?(!(i&r)||i&n)&&(l[o]=qp(i,t)):s<=t&&(e.expiredLanes|=i),a&=~i}}function mi(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Ad(){var e=Il;return Il<<=1,!(Il&4194240)&&(Il=64),e}function No(e){for(var t=[],r=0;31>r;r++)t.push(e);return t}function Sl(e,t,r){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-xt(t),e[t]=r}function tm(e,t){var r=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var n=e.eventTimes;for(e=e.expirationTimes;0<r;){var l=31-xt(r),a=1<<l;t[l]=0,n[l]=-1,e[l]=-1,r&=~a}}function ss(e,t){var r=e.entangledLanes|=t;for(e=e.entanglements;r;){var n=31-xt(r),l=1<<n;l&t|e[n]&t&&(e[n]|=t),r&=~l}}var te=0;function Id(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var $d,us,Ud,Bd,Hd,gi=!1,Ul=[],tr=null,rr=null,nr=null,al=new Map,ol=new Map,Xt=[],rm="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function _u(e,t){switch(e){case"focusin":case"focusout":tr=null;break;case"dragenter":case"dragleave":rr=null;break;case"mouseover":case"mouseout":nr=null;break;case"pointerover":case"pointerout":al.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":ol.delete(t.pointerId)}}function Pn(e,t,r,n,l,a){return e===null||e.nativeEvent!==a?(e={blockedOn:t,domEventName:r,eventSystemFlags:n,nativeEvent:a,targetContainers:[l]},t!==null&&(t=El(t),t!==null&&us(t)),e):(e.eventSystemFlags|=n,t=e.targetContainers,l!==null&&t.indexOf(l)===-1&&t.push(l),e)}function nm(e,t,r,n,l){switch(t){case"focusin":return tr=Pn(tr,e,t,r,n,l),!0;case"dragenter":return rr=Pn(rr,e,t,r,n,l),!0;case"mouseover":return nr=Pn(nr,e,t,r,n,l),!0;case"pointerover":var a=l.pointerId;return al.set(a,Pn(al.get(a)||null,e,t,r,n,l)),!0;case"gotpointercapture":return a=l.pointerId,ol.set(a,Pn(ol.get(a)||null,e,t,r,n,l)),!0}return!1}function Wd(e){var t=Er(e.target);if(t!==null){var r=Dr(t);if(r!==null){if(t=r.tag,t===13){if(t=Td(r),t!==null){e.blockedOn=t,Hd(e.priority,function(){Ud(r)});return}}else if(t===3&&r.stateNode.current.memoizedState.isDehydrated){e.blockedOn=r.tag===3?r.stateNode.containerInfo:null;return}}}e.blockedOn=null}function la(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var r=yi(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(r===null){r=e.nativeEvent;var n=new r.constructor(r.type,r);di=n,r.target.dispatchEvent(n),di=null}else return t=El(r),t!==null&&us(t),e.blockedOn=r,!1;t.shift()}return!0}function Lu(e,t,r){la(e)&&r.delete(t)}function lm(){gi=!1,tr!==null&&la(tr)&&(tr=null),rr!==null&&la(rr)&&(rr=null),nr!==null&&la(nr)&&(nr=null),al.forEach(Lu),ol.forEach(Lu)}function bn(e,t){e.blockedOn===t&&(e.blockedOn=null,gi||(gi=!0,rt.unstable_scheduleCallback(rt.unstable_NormalPriority,lm)))}function il(e){function t(l){return bn(l,e)}if(0<Ul.length){bn(Ul[0],e);for(var r=1;r<Ul.length;r++){var n=Ul[r];n.blockedOn===e&&(n.blockedOn=null)}}for(tr!==null&&bn(tr,e),rr!==null&&bn(rr,e),nr!==null&&bn(nr,e),al.forEach(t),ol.forEach(t),r=0;r<Xt.length;r++)n=Xt[r],n.blockedOn===e&&(n.blockedOn=null);for(;0<Xt.length&&(r=Xt[0],r.blockedOn===null);)Wd(r),r.blockedOn===null&&Xt.shift()}var an=$t.ReactCurrentBatchConfig,Sa=!0;function am(e,t,r,n){var l=te,a=an.transition;an.transition=null;try{te=1,cs(e,t,r,n)}finally{te=l,an.transition=a}}function om(e,t,r,n){var l=te,a=an.transition;an.transition=null;try{te=4,cs(e,t,r,n)}finally{te=l,an.transition=a}}function cs(e,t,r,n){if(Sa){var l=yi(e,t,r,n);if(l===null)Fo(e,t,n,ja,r),_u(e,n);else if(nm(l,e,t,r,n))n.stopPropagation();else if(_u(e,n),t&4&&-1<rm.indexOf(e)){for(;l!==null;){var a=El(l);if(a!==null&&$d(a),a=yi(e,t,r,n),a===null&&Fo(e,t,n,ja,r),a===l)break;l=a}l!==null&&n.stopPropagation()}else Fo(e,t,n,null,r)}}var ja=null;function yi(e,t,r,n){if(ja=null,e=os(n),e=Er(e),e!==null)if(t=Dr(e),t===null)e=null;else if(r=t.tag,r===13){if(e=Td(t),e!==null)return e;e=null}else if(r===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return ja=e,null}function Vd(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Kp()){case is:return 1;case Dd:return 4;case wa:case Yp:return 16;case Od:return 536870912;default:return 16}default:return 16}}var qt=null,ds=null,aa=null;function Qd(){if(aa)return aa;var e,t=ds,r=t.length,n,l="value"in qt?qt.value:qt.textContent,a=l.length;for(e=0;e<r&&t[e]===l[e];e++);var o=r-e;for(n=1;n<=o&&t[r-n]===l[a-n];n++);return aa=l.slice(e,1<n?1-n:void 0)}function oa(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Bl(){return!0}function Pu(){return!1}function lt(e){function t(r,n,l,a,o){this._reactName=r,this._targetInst=l,this.type=n,this.nativeEvent=a,this.target=o,this.currentTarget=null;for(var i in e)e.hasOwnProperty(i)&&(r=e[i],this[i]=r?r(a):a[i]);return this.isDefaultPrevented=(a.defaultPrevented!=null?a.defaultPrevented:a.returnValue===!1)?Bl:Pu,this.isPropagationStopped=Pu,this}return de(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var r=this.nativeEvent;r&&(r.preventDefault?r.preventDefault():typeof r.returnValue!="unknown"&&(r.returnValue=!1),this.isDefaultPrevented=Bl)},stopPropagation:function(){var r=this.nativeEvent;r&&(r.stopPropagation?r.stopPropagation():typeof r.cancelBubble!="unknown"&&(r.cancelBubble=!0),this.isPropagationStopped=Bl)},persist:function(){},isPersistent:Bl}),t}var xn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},fs=lt(xn),jl=de({},xn,{view:0,detail:0}),im=lt(jl),Co,_o,Rn,Ka=de({},jl,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:hs,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Rn&&(Rn&&e.type==="mousemove"?(Co=e.screenX-Rn.screenX,_o=e.screenY-Rn.screenY):_o=Co=0,Rn=e),Co)},movementY:function(e){return"movementY"in e?e.movementY:_o}}),bu=lt(Ka),sm=de({},Ka,{dataTransfer:0}),um=lt(sm),cm=de({},jl,{relatedTarget:0}),Lo=lt(cm),dm=de({},xn,{animationName:0,elapsedTime:0,pseudoElement:0}),fm=lt(dm),hm=de({},xn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),pm=lt(hm),mm=de({},xn,{data:0}),Ru=lt(mm),gm={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},ym={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},vm={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function xm(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=vm[e])?!!t[e]:!1}function hs(){return xm}var wm=de({},jl,{key:function(e){if(e.key){var t=gm[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=oa(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?ym[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:hs,charCode:function(e){return e.type==="keypress"?oa(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?oa(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),km=lt(wm),Sm=de({},Ka,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Tu=lt(Sm),jm=de({},jl,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:hs}),Em=lt(jm),Nm=de({},xn,{propertyName:0,elapsedTime:0,pseudoElement:0}),Cm=lt(Nm),_m=de({},Ka,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Lm=lt(_m),Pm=[9,13,27,32],ps=Dt&&"CompositionEvent"in window,Gn=null;Dt&&"documentMode"in document&&(Gn=document.documentMode);var bm=Dt&&"TextEvent"in window&&!Gn,Kd=Dt&&(!ps||Gn&&8<Gn&&11>=Gn),Mu=" ",zu=!1;function Yd(e,t){switch(e){case"keyup":return Pm.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Gd(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Qr=!1;function Rm(e,t){switch(e){case"compositionend":return Gd(t);case"keypress":return t.which!==32?null:(zu=!0,Mu);case"textInput":return e=t.data,e===Mu&&zu?null:e;default:return null}}function Tm(e,t){if(Qr)return e==="compositionend"||!ps&&Yd(e,t)?(e=Qd(),aa=ds=qt=null,Qr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Kd&&t.locale!=="ko"?null:t.data;default:return null}}var Mm={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Fu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Mm[e.type]:t==="textarea"}function Xd(e,t,r,n){_d(n),t=Ea(t,"onChange"),0<t.length&&(r=new fs("onChange","change",null,r,n),e.push({event:r,listeners:t}))}var Xn=null,sl=null;function zm(e){sf(e,0)}function Ya(e){var t=Gr(e);if(wd(t))return e}function Fm(e,t){if(e==="change")return t}var Jd=!1;if(Dt){var Po;if(Dt){var bo="oninput"in document;if(!bo){var Du=document.createElement("div");Du.setAttribute("oninput","return;"),bo=typeof Du.oninput=="function"}Po=bo}else Po=!1;Jd=Po&&(!document.documentMode||9<document.documentMode)}function Ou(){Xn&&(Xn.detachEvent("onpropertychange",Zd),sl=Xn=null)}function Zd(e){if(e.propertyName==="value"&&Ya(sl)){var t=[];Xd(t,sl,e,os(e)),Rd(zm,t)}}function Dm(e,t,r){e==="focusin"?(Ou(),Xn=t,sl=r,Xn.attachEvent("onpropertychange",Zd)):e==="focusout"&&Ou()}function Om(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ya(sl)}function Am(e,t){if(e==="click")return Ya(t)}function Im(e,t){if(e==="input"||e==="change")return Ya(t)}function $m(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var kt=typeof Object.is=="function"?Object.is:$m;function ul(e,t){if(kt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var r=Object.keys(e),n=Object.keys(t);if(r.length!==n.length)return!1;for(n=0;n<r.length;n++){var l=r[n];if(!qo.call(t,l)||!kt(e[l],t[l]))return!1}return!0}function Au(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Iu(e,t){var r=Au(e);e=0;for(var n;r;){if(r.nodeType===3){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=Au(r)}}function qd(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?qd(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function ef(){for(var e=window,t=ya();t instanceof e.HTMLIFrameElement;){try{var r=typeof t.contentWindow.location.href=="string"}catch{r=!1}if(r)e=t.contentWindow;else break;t=ya(e.document)}return t}function ms(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Um(e){var t=ef(),r=e.focusedElem,n=e.selectionRange;if(t!==r&&r&&r.ownerDocument&&qd(r.ownerDocument.documentElement,r)){if(n!==null&&ms(r)){if(t=n.start,e=n.end,e===void 0&&(e=t),"selectionStart"in r)r.selectionStart=t,r.selectionEnd=Math.min(e,r.value.length);else if(e=(t=r.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var l=r.textContent.length,a=Math.min(n.start,l);n=n.end===void 0?a:Math.min(n.end,l),!e.extend&&a>n&&(l=n,n=a,a=l),l=Iu(r,a);var o=Iu(r,n);l&&o&&(e.rangeCount!==1||e.anchorNode!==l.node||e.anchorOffset!==l.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&(t=t.createRange(),t.setStart(l.node,l.offset),e.removeAllRanges(),a>n?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}}for(t=[],e=r;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof r.focus=="function"&&r.focus(),r=0;r<t.length;r++)e=t[r],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Bm=Dt&&"documentMode"in document&&11>=document.documentMode,Kr=null,vi=null,Jn=null,xi=!1;function $u(e,t,r){var n=r.window===r?r.document:r.nodeType===9?r:r.ownerDocument;xi||Kr==null||Kr!==ya(n)||(n=Kr,"selectionStart"in n&&ms(n)?n={start:n.selectionStart,end:n.selectionEnd}:(n=(n.ownerDocument&&n.ownerDocument.defaultView||window).getSelection(),n={anchorNode:n.anchorNode,anchorOffset:n.anchorOffset,focusNode:n.focusNode,focusOffset:n.focusOffset}),Jn&&ul(Jn,n)||(Jn=n,n=Ea(vi,"onSelect"),0<n.length&&(t=new fs("onSelect","select",null,t,r),e.push({event:t,listeners:n}),t.target=Kr)))}function Hl(e,t){var r={};return r[e.toLowerCase()]=t.toLowerCase(),r["Webkit"+e]="webkit"+t,r["Moz"+e]="moz"+t,r}var Yr={animationend:Hl("Animation","AnimationEnd"),animationiteration:Hl("Animation","AnimationIteration"),animationstart:Hl("Animation","AnimationStart"),transitionend:Hl("Transition","TransitionEnd")},Ro={},tf={};Dt&&(tf=document.createElement("div").style,"AnimationEvent"in window||(delete Yr.animationend.animation,delete Yr.animationiteration.animation,delete Yr.animationstart.animation),"TransitionEvent"in window||delete Yr.transitionend.transition);function Ga(e){if(Ro[e])return Ro[e];if(!Yr[e])return e;var t=Yr[e],r;for(r in t)if(t.hasOwnProperty(r)&&r in tf)return Ro[e]=t[r];return e}var rf=Ga("animationend"),nf=Ga("animationiteration"),lf=Ga("animationstart"),af=Ga("transitionend"),of=new Map,Uu="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function fr(e,t){of.set(e,t),Fr(t,[e])}for(var To=0;To<Uu.length;To++){var Mo=Uu[To],Hm=Mo.toLowerCase(),Wm=Mo[0].toUpperCase()+Mo.slice(1);fr(Hm,"on"+Wm)}fr(rf,"onAnimationEnd");fr(nf,"onAnimationIteration");fr(lf,"onAnimationStart");fr("dblclick","onDoubleClick");fr("focusin","onFocus");fr("focusout","onBlur");fr(af,"onTransitionEnd");cn("onMouseEnter",["mouseout","mouseover"]);cn("onMouseLeave",["mouseout","mouseover"]);cn("onPointerEnter",["pointerout","pointerover"]);cn("onPointerLeave",["pointerout","pointerover"]);Fr("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Fr("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Fr("onBeforeInput",["compositionend","keypress","textInput","paste"]);Fr("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Fr("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Fr("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Hn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Vm=new Set("cancel close invalid load scroll toggle".split(" ").concat(Hn));function Bu(e,t,r){var n=e.type||"unknown-event";e.currentTarget=r,Hp(n,t,void 0,e),e.currentTarget=null}function sf(e,t){t=(t&4)!==0;for(var r=0;r<e.length;r++){var n=e[r],l=n.event;n=n.listeners;e:{var a=void 0;if(t)for(var o=n.length-1;0<=o;o--){var i=n[o],s=i.instance,c=i.currentTarget;if(i=i.listener,s!==a&&l.isPropagationStopped())break e;Bu(l,i,c),a=s}else for(o=0;o<n.length;o++){if(i=n[o],s=i.instance,c=i.currentTarget,i=i.listener,s!==a&&l.isPropagationStopped())break e;Bu(l,i,c),a=s}}}if(xa)throw e=pi,xa=!1,pi=null,e}function ae(e,t){var r=t[Ei];r===void 0&&(r=t[Ei]=new Set);var n=e+"__bubble";r.has(n)||(uf(t,e,2,!1),r.add(n))}function zo(e,t,r){var n=0;t&&(n|=4),uf(r,e,n,t)}var Wl="_reactListening"+Math.random().toString(36).slice(2);function cl(e){if(!e[Wl]){e[Wl]=!0,md.forEach(function(r){r!=="selectionchange"&&(Vm.has(r)||zo(r,!1,e),zo(r,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Wl]||(t[Wl]=!0,zo("selectionchange",!1,t))}}function uf(e,t,r,n){switch(Vd(t)){case 1:var l=am;break;case 4:l=om;break;default:l=cs}r=l.bind(null,t,r,e),l=void 0,!hi||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(l=!0),n?l!==void 0?e.addEventListener(t,r,{capture:!0,passive:l}):e.addEventListener(t,r,!0):l!==void 0?e.addEventListener(t,r,{passive:l}):e.addEventListener(t,r,!1)}function Fo(e,t,r,n,l){var a=n;if(!(t&1)&&!(t&2)&&n!==null)e:for(;;){if(n===null)return;var o=n.tag;if(o===3||o===4){var i=n.stateNode.containerInfo;if(i===l||i.nodeType===8&&i.parentNode===l)break;if(o===4)for(o=n.return;o!==null;){var s=o.tag;if((s===3||s===4)&&(s=o.stateNode.containerInfo,s===l||s.nodeType===8&&s.parentNode===l))return;o=o.return}for(;i!==null;){if(o=Er(i),o===null)return;if(s=o.tag,s===5||s===6){n=a=o;continue e}i=i.parentNode}}n=n.return}Rd(function(){var c=a,d=os(r),f=[];e:{var p=of.get(e);if(p!==void 0){var x=fs,S=e;switch(e){case"keypress":if(oa(r)===0)break e;case"keydown":case"keyup":x=km;break;case"focusin":S="focus",x=Lo;break;case"focusout":S="blur",x=Lo;break;case"beforeblur":case"afterblur":x=Lo;break;case"click":if(r.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":x=bu;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":x=um;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":x=Em;break;case rf:case nf:case lf:x=fm;break;case af:x=Cm;break;case"scroll":x=im;break;case"wheel":x=Lm;break;case"copy":case"cut":case"paste":x=pm;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":x=Tu}var v=(t&4)!==0,_=!v&&e==="scroll",h=v?p!==null?p+"Capture":null:p;v=[];for(var m=c,g;m!==null;){g=m;var E=g.stateNode;if(g.tag===5&&E!==null&&(g=E,h!==null&&(E=ll(m,h),E!=null&&v.push(dl(m,E,g)))),_)break;m=m.return}0<v.length&&(p=new x(p,S,null,r,d),f.push({event:p,listeners:v}))}}if(!(t&7)){e:{if(p=e==="mouseover"||e==="pointerover",x=e==="mouseout"||e==="pointerout",p&&r!==di&&(S=r.relatedTarget||r.fromElement)&&(Er(S)||S[Ot]))break e;if((x||p)&&(p=d.window===d?d:(p=d.ownerDocument)?p.defaultView||p.parentWindow:window,x?(S=r.relatedTarget||r.toElement,x=c,S=S?Er(S):null,S!==null&&(_=Dr(S),S!==_||S.tag!==5&&S.tag!==6)&&(S=null)):(x=null,S=c),x!==S)){if(v=bu,E="onMouseLeave",h="onMouseEnter",m="mouse",(e==="pointerout"||e==="pointerover")&&(v=Tu,E="onPointerLeave",h="onPointerEnter",m="pointer"),_=x==null?p:Gr(x),g=S==null?p:Gr(S),p=new v(E,m+"leave",x,r,d),p.target=_,p.relatedTarget=g,E=null,Er(d)===c&&(v=new v(h,m+"enter",S,r,d),v.target=g,v.relatedTarget=_,E=v),_=E,x&&S)t:{for(v=x,h=S,m=0,g=v;g;g=Ur(g))m++;for(g=0,E=h;E;E=Ur(E))g++;for(;0<m-g;)v=Ur(v),m--;for(;0<g-m;)h=Ur(h),g--;for(;m--;){if(v===h||h!==null&&v===h.alternate)break t;v=Ur(v),h=Ur(h)}v=null}else v=null;x!==null&&Hu(f,p,x,v,!1),S!==null&&_!==null&&Hu(f,_,S,v,!0)}}e:{if(p=c?Gr(c):window,x=p.nodeName&&p.nodeName.toLowerCase(),x==="select"||x==="input"&&p.type==="file")var L=Fm;else if(Fu(p))if(Jd)L=Im;else{L=Om;var w=Dm}else(x=p.nodeName)&&x.toLowerCase()==="input"&&(p.type==="checkbox"||p.type==="radio")&&(L=Am);if(L&&(L=L(e,c))){Xd(f,L,r,d);break e}w&&w(e,p,c),e==="focusout"&&(w=p._wrapperState)&&w.controlled&&p.type==="number"&&oi(p,"number",p.value)}switch(w=c?Gr(c):window,e){case"focusin":(Fu(w)||w.contentEditable==="true")&&(Kr=w,vi=c,Jn=null);break;case"focusout":Jn=vi=Kr=null;break;case"mousedown":xi=!0;break;case"contextmenu":case"mouseup":case"dragend":xi=!1,$u(f,r,d);break;case"selectionchange":if(Bm)break;case"keydown":case"keyup":$u(f,r,d)}var C;if(ps)e:{switch(e){case"compositionstart":var R="onCompositionStart";break e;case"compositionend":R="onCompositionEnd";break e;case"compositionupdate":R="onCompositionUpdate";break e}R=void 0}else Qr?Yd(e,r)&&(R="onCompositionEnd"):e==="keydown"&&r.keyCode===229&&(R="onCompositionStart");R&&(Kd&&r.locale!=="ko"&&(Qr||R!=="onCompositionStart"?R==="onCompositionEnd"&&Qr&&(C=Qd()):(qt=d,ds="value"in qt?qt.value:qt.textContent,Qr=!0)),w=Ea(c,R),0<w.length&&(R=new Ru(R,e,null,r,d),f.push({event:R,listeners:w}),C?R.data=C:(C=Gd(r),C!==null&&(R.data=C)))),(C=bm?Rm(e,r):Tm(e,r))&&(c=Ea(c,"onBeforeInput"),0<c.length&&(d=new Ru("onBeforeInput","beforeinput",null,r,d),f.push({event:d,listeners:c}),d.data=C))}sf(f,t)})}function dl(e,t,r){return{instance:e,listener:t,currentTarget:r}}function Ea(e,t){for(var r=t+"Capture",n=[];e!==null;){var l=e,a=l.stateNode;l.tag===5&&a!==null&&(l=a,a=ll(e,r),a!=null&&n.unshift(dl(e,a,l)),a=ll(e,t),a!=null&&n.push(dl(e,a,l))),e=e.return}return n}function Ur(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Hu(e,t,r,n,l){for(var a=t._reactName,o=[];r!==null&&r!==n;){var i=r,s=i.alternate,c=i.stateNode;if(s!==null&&s===n)break;i.tag===5&&c!==null&&(i=c,l?(s=ll(r,a),s!=null&&o.unshift(dl(r,s,i))):l||(s=ll(r,a),s!=null&&o.push(dl(r,s,i)))),r=r.return}o.length!==0&&e.push({event:t,listeners:o})}var Qm=/\r\n?/g,Km=/\u0000|\uFFFD/g;function Wu(e){return(typeof e=="string"?e:""+e).replace(Qm,`
`).replace(Km,"")}function Vl(e,t,r){if(t=Wu(t),Wu(e)!==t&&r)throw Error(b(425))}function Na(){}var wi=null,ki=null;function Si(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var ji=typeof setTimeout=="function"?setTimeout:void 0,Ym=typeof clearTimeout=="function"?clearTimeout:void 0,Vu=typeof Promise=="function"?Promise:void 0,Gm=typeof queueMicrotask=="function"?queueMicrotask:typeof Vu<"u"?function(e){return Vu.resolve(null).then(e).catch(Xm)}:ji;function Xm(e){setTimeout(function(){throw e})}function Do(e,t){var r=t,n=0;do{var l=r.nextSibling;if(e.removeChild(r),l&&l.nodeType===8)if(r=l.data,r==="/$"){if(n===0){e.removeChild(l),il(t);return}n--}else r!=="$"&&r!=="$?"&&r!=="$!"||n++;r=l}while(r);il(t)}function lr(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Qu(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var r=e.data;if(r==="$"||r==="$!"||r==="$?"){if(t===0)return e;t--}else r==="/$"&&t++}e=e.previousSibling}return null}var wn=Math.random().toString(36).slice(2),Nt="__reactFiber$"+wn,fl="__reactProps$"+wn,Ot="__reactContainer$"+wn,Ei="__reactEvents$"+wn,Jm="__reactListeners$"+wn,Zm="__reactHandles$"+wn;function Er(e){var t=e[Nt];if(t)return t;for(var r=e.parentNode;r;){if(t=r[Ot]||r[Nt]){if(r=t.alternate,t.child!==null||r!==null&&r.child!==null)for(e=Qu(e);e!==null;){if(r=e[Nt])return r;e=Qu(e)}return t}e=r,r=e.parentNode}return null}function El(e){return e=e[Nt]||e[Ot],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Gr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(b(33))}function Xa(e){return e[fl]||null}var Ni=[],Xr=-1;function hr(e){return{current:e}}function oe(e){0>Xr||(e.current=Ni[Xr],Ni[Xr]=null,Xr--)}function le(e,t){Xr++,Ni[Xr]=e.current,e.current=t}var cr={},Fe=hr(cr),Ve=hr(!1),br=cr;function dn(e,t){var r=e.type.contextTypes;if(!r)return cr;var n=e.stateNode;if(n&&n.__reactInternalMemoizedUnmaskedChildContext===t)return n.__reactInternalMemoizedMaskedChildContext;var l={},a;for(a in r)l[a]=t[a];return n&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function Qe(e){return e=e.childContextTypes,e!=null}function Ca(){oe(Ve),oe(Fe)}function Ku(e,t,r){if(Fe.current!==cr)throw Error(b(168));le(Fe,t),le(Ve,r)}function cf(e,t,r){var n=e.stateNode;if(t=t.childContextTypes,typeof n.getChildContext!="function")return r;n=n.getChildContext();for(var l in n)if(!(l in t))throw Error(b(108,Dp(e)||"Unknown",l));return de({},r,n)}function _a(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||cr,br=Fe.current,le(Fe,e),le(Ve,Ve.current),!0}function Yu(e,t,r){var n=e.stateNode;if(!n)throw Error(b(169));r?(e=cf(e,t,br),n.__reactInternalMemoizedMergedChildContext=e,oe(Ve),oe(Fe),le(Fe,e)):oe(Ve),le(Ve,r)}var Tt=null,Ja=!1,Oo=!1;function df(e){Tt===null?Tt=[e]:Tt.push(e)}function qm(e){Ja=!0,df(e)}function pr(){if(!Oo&&Tt!==null){Oo=!0;var e=0,t=te;try{var r=Tt;for(te=1;e<r.length;e++){var n=r[e];do n=n(!0);while(n!==null)}Tt=null,Ja=!1}catch(l){throw Tt!==null&&(Tt=Tt.slice(e+1)),Fd(is,pr),l}finally{te=t,Oo=!1}}return null}var Jr=[],Zr=0,La=null,Pa=0,st=[],ut=0,Rr=null,Mt=1,zt="";function kr(e,t){Jr[Zr++]=Pa,Jr[Zr++]=La,La=e,Pa=t}function ff(e,t,r){st[ut++]=Mt,st[ut++]=zt,st[ut++]=Rr,Rr=e;var n=Mt;e=zt;var l=32-xt(n)-1;n&=~(1<<l),r+=1;var a=32-xt(t)+l;if(30<a){var o=l-l%5;a=(n&(1<<o)-1).toString(32),n>>=o,l-=o,Mt=1<<32-xt(t)+l|r<<l|n,zt=a+e}else Mt=1<<a|r<<l|n,zt=e}function gs(e){e.return!==null&&(kr(e,1),ff(e,1,0))}function ys(e){for(;e===La;)La=Jr[--Zr],Jr[Zr]=null,Pa=Jr[--Zr],Jr[Zr]=null;for(;e===Rr;)Rr=st[--ut],st[ut]=null,zt=st[--ut],st[ut]=null,Mt=st[--ut],st[ut]=null}var tt=null,et=null,ie=!1,vt=null;function hf(e,t){var r=ct(5,null,null,0);r.elementType="DELETED",r.stateNode=t,r.return=e,t=e.deletions,t===null?(e.deletions=[r],e.flags|=16):t.push(r)}function Gu(e,t){switch(e.tag){case 5:var r=e.type;return t=t.nodeType!==1||r.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,tt=e,et=lr(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,tt=e,et=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(r=Rr!==null?{id:Mt,overflow:zt}:null,e.memoizedState={dehydrated:t,treeContext:r,retryLane:1073741824},r=ct(18,null,null,0),r.stateNode=t,r.return=e,e.child=r,tt=e,et=null,!0):!1;default:return!1}}function Ci(e){return(e.mode&1)!==0&&(e.flags&128)===0}function _i(e){if(ie){var t=et;if(t){var r=t;if(!Gu(e,t)){if(Ci(e))throw Error(b(418));t=lr(r.nextSibling);var n=tt;t&&Gu(e,t)?hf(n,r):(e.flags=e.flags&-4097|2,ie=!1,tt=e)}}else{if(Ci(e))throw Error(b(418));e.flags=e.flags&-4097|2,ie=!1,tt=e}}}function Xu(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;tt=e}function Ql(e){if(e!==tt)return!1;if(!ie)return Xu(e),ie=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Si(e.type,e.memoizedProps)),t&&(t=et)){if(Ci(e))throw pf(),Error(b(418));for(;t;)hf(e,t),t=lr(t.nextSibling)}if(Xu(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(b(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var r=e.data;if(r==="/$"){if(t===0){et=lr(e.nextSibling);break e}t--}else r!=="$"&&r!=="$!"&&r!=="$?"||t++}e=e.nextSibling}et=null}}else et=tt?lr(e.stateNode.nextSibling):null;return!0}function pf(){for(var e=et;e;)e=lr(e.nextSibling)}function fn(){et=tt=null,ie=!1}function vs(e){vt===null?vt=[e]:vt.push(e)}var e0=$t.ReactCurrentBatchConfig;function Tn(e,t,r){if(e=r.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(r._owner){if(r=r._owner,r){if(r.tag!==1)throw Error(b(309));var n=r.stateNode}if(!n)throw Error(b(147,e));var l=n,a=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===a?t.ref:(t=function(o){var i=l.refs;o===null?delete i[a]:i[a]=o},t._stringRef=a,t)}if(typeof e!="string")throw Error(b(284));if(!r._owner)throw Error(b(290,e))}return e}function Kl(e,t){throw e=Object.prototype.toString.call(t),Error(b(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Ju(e){var t=e._init;return t(e._payload)}function mf(e){function t(h,m){if(e){var g=h.deletions;g===null?(h.deletions=[m],h.flags|=16):g.push(m)}}function r(h,m){if(!e)return null;for(;m!==null;)t(h,m),m=m.sibling;return null}function n(h,m){for(h=new Map;m!==null;)m.key!==null?h.set(m.key,m):h.set(m.index,m),m=m.sibling;return h}function l(h,m){return h=sr(h,m),h.index=0,h.sibling=null,h}function a(h,m,g){return h.index=g,e?(g=h.alternate,g!==null?(g=g.index,g<m?(h.flags|=2,m):g):(h.flags|=2,m)):(h.flags|=1048576,m)}function o(h){return e&&h.alternate===null&&(h.flags|=2),h}function i(h,m,g,E){return m===null||m.tag!==6?(m=Wo(g,h.mode,E),m.return=h,m):(m=l(m,g),m.return=h,m)}function s(h,m,g,E){var L=g.type;return L===Vr?d(h,m,g.props.children,E,g.key):m!==null&&(m.elementType===L||typeof L=="object"&&L!==null&&L.$$typeof===Yt&&Ju(L)===m.type)?(E=l(m,g.props),E.ref=Tn(h,m,g),E.return=h,E):(E=ha(g.type,g.key,g.props,null,h.mode,E),E.ref=Tn(h,m,g),E.return=h,E)}function c(h,m,g,E){return m===null||m.tag!==4||m.stateNode.containerInfo!==g.containerInfo||m.stateNode.implementation!==g.implementation?(m=Vo(g,h.mode,E),m.return=h,m):(m=l(m,g.children||[]),m.return=h,m)}function d(h,m,g,E,L){return m===null||m.tag!==7?(m=Pr(g,h.mode,E,L),m.return=h,m):(m=l(m,g),m.return=h,m)}function f(h,m,g){if(typeof m=="string"&&m!==""||typeof m=="number")return m=Wo(""+m,h.mode,g),m.return=h,m;if(typeof m=="object"&&m!==null){switch(m.$$typeof){case Dl:return g=ha(m.type,m.key,m.props,null,h.mode,g),g.ref=Tn(h,null,m),g.return=h,g;case Wr:return m=Vo(m,h.mode,g),m.return=h,m;case Yt:var E=m._init;return f(h,E(m._payload),g)}if(Un(m)||_n(m))return m=Pr(m,h.mode,g,null),m.return=h,m;Kl(h,m)}return null}function p(h,m,g,E){var L=m!==null?m.key:null;if(typeof g=="string"&&g!==""||typeof g=="number")return L!==null?null:i(h,m,""+g,E);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case Dl:return g.key===L?s(h,m,g,E):null;case Wr:return g.key===L?c(h,m,g,E):null;case Yt:return L=g._init,p(h,m,L(g._payload),E)}if(Un(g)||_n(g))return L!==null?null:d(h,m,g,E,null);Kl(h,g)}return null}function x(h,m,g,E,L){if(typeof E=="string"&&E!==""||typeof E=="number")return h=h.get(g)||null,i(m,h,""+E,L);if(typeof E=="object"&&E!==null){switch(E.$$typeof){case Dl:return h=h.get(E.key===null?g:E.key)||null,s(m,h,E,L);case Wr:return h=h.get(E.key===null?g:E.key)||null,c(m,h,E,L);case Yt:var w=E._init;return x(h,m,g,w(E._payload),L)}if(Un(E)||_n(E))return h=h.get(g)||null,d(m,h,E,L,null);Kl(m,E)}return null}function S(h,m,g,E){for(var L=null,w=null,C=m,R=m=0,O=null;C!==null&&R<g.length;R++){C.index>R?(O=C,C=null):O=C.sibling;var B=p(h,C,g[R],E);if(B===null){C===null&&(C=O);break}e&&C&&B.alternate===null&&t(h,C),m=a(B,m,R),w===null?L=B:w.sibling=B,w=B,C=O}if(R===g.length)return r(h,C),ie&&kr(h,R),L;if(C===null){for(;R<g.length;R++)C=f(h,g[R],E),C!==null&&(m=a(C,m,R),w===null?L=C:w.sibling=C,w=C);return ie&&kr(h,R),L}for(C=n(h,C);R<g.length;R++)O=x(C,h,R,g[R],E),O!==null&&(e&&O.alternate!==null&&C.delete(O.key===null?R:O.key),m=a(O,m,R),w===null?L=O:w.sibling=O,w=O);return e&&C.forEach(function(se){return t(h,se)}),ie&&kr(h,R),L}function v(h,m,g,E){var L=_n(g);if(typeof L!="function")throw Error(b(150));if(g=L.call(g),g==null)throw Error(b(151));for(var w=L=null,C=m,R=m=0,O=null,B=g.next();C!==null&&!B.done;R++,B=g.next()){C.index>R?(O=C,C=null):O=C.sibling;var se=p(h,C,B.value,E);if(se===null){C===null&&(C=O);break}e&&C&&se.alternate===null&&t(h,C),m=a(se,m,R),w===null?L=se:w.sibling=se,w=se,C=O}if(B.done)return r(h,C),ie&&kr(h,R),L;if(C===null){for(;!B.done;R++,B=g.next())B=f(h,B.value,E),B!==null&&(m=a(B,m,R),w===null?L=B:w.sibling=B,w=B);return ie&&kr(h,R),L}for(C=n(h,C);!B.done;R++,B=g.next())B=x(C,h,R,B.value,E),B!==null&&(e&&B.alternate!==null&&C.delete(B.key===null?R:B.key),m=a(B,m,R),w===null?L=B:w.sibling=B,w=B);return e&&C.forEach(function(Ye){return t(h,Ye)}),ie&&kr(h,R),L}function _(h,m,g,E){if(typeof g=="object"&&g!==null&&g.type===Vr&&g.key===null&&(g=g.props.children),typeof g=="object"&&g!==null){switch(g.$$typeof){case Dl:e:{for(var L=g.key,w=m;w!==null;){if(w.key===L){if(L=g.type,L===Vr){if(w.tag===7){r(h,w.sibling),m=l(w,g.props.children),m.return=h,h=m;break e}}else if(w.elementType===L||typeof L=="object"&&L!==null&&L.$$typeof===Yt&&Ju(L)===w.type){r(h,w.sibling),m=l(w,g.props),m.ref=Tn(h,w,g),m.return=h,h=m;break e}r(h,w);break}else t(h,w);w=w.sibling}g.type===Vr?(m=Pr(g.props.children,h.mode,E,g.key),m.return=h,h=m):(E=ha(g.type,g.key,g.props,null,h.mode,E),E.ref=Tn(h,m,g),E.return=h,h=E)}return o(h);case Wr:e:{for(w=g.key;m!==null;){if(m.key===w)if(m.tag===4&&m.stateNode.containerInfo===g.containerInfo&&m.stateNode.implementation===g.implementation){r(h,m.sibling),m=l(m,g.children||[]),m.return=h,h=m;break e}else{r(h,m);break}else t(h,m);m=m.sibling}m=Vo(g,h.mode,E),m.return=h,h=m}return o(h);case Yt:return w=g._init,_(h,m,w(g._payload),E)}if(Un(g))return S(h,m,g,E);if(_n(g))return v(h,m,g,E);Kl(h,g)}return typeof g=="string"&&g!==""||typeof g=="number"?(g=""+g,m!==null&&m.tag===6?(r(h,m.sibling),m=l(m,g),m.return=h,h=m):(r(h,m),m=Wo(g,h.mode,E),m.return=h,h=m),o(h)):r(h,m)}return _}var hn=mf(!0),gf=mf(!1),ba=hr(null),Ra=null,qr=null,xs=null;function ws(){xs=qr=Ra=null}function ks(e){var t=ba.current;oe(ba),e._currentValue=t}function Li(e,t,r){for(;e!==null;){var n=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,n!==null&&(n.childLanes|=t)):n!==null&&(n.childLanes&t)!==t&&(n.childLanes|=t),e===r)break;e=e.return}}function on(e,t){Ra=e,xs=qr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(We=!0),e.firstContext=null)}function ft(e){var t=e._currentValue;if(xs!==e)if(e={context:e,memoizedValue:t,next:null},qr===null){if(Ra===null)throw Error(b(308));qr=e,Ra.dependencies={lanes:0,firstContext:e}}else qr=qr.next=e;return t}var Nr=null;function Ss(e){Nr===null?Nr=[e]:Nr.push(e)}function yf(e,t,r,n){var l=t.interleaved;return l===null?(r.next=r,Ss(t)):(r.next=l.next,l.next=r),t.interleaved=r,At(e,n)}function At(e,t){e.lanes|=t;var r=e.alternate;for(r!==null&&(r.lanes|=t),r=e,e=e.return;e!==null;)e.childLanes|=t,r=e.alternate,r!==null&&(r.childLanes|=t),r=e,e=e.return;return r.tag===3?r.stateNode:null}var Gt=!1;function js(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function vf(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Ft(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function ar(e,t,r){var n=e.updateQueue;if(n===null)return null;if(n=n.shared,X&2){var l=n.pending;return l===null?t.next=t:(t.next=l.next,l.next=t),n.pending=t,At(e,r)}return l=n.interleaved,l===null?(t.next=t,Ss(n)):(t.next=l.next,l.next=t),n.interleaved=t,At(e,r)}function ia(e,t,r){if(t=t.updateQueue,t!==null&&(t=t.shared,(r&4194240)!==0)){var n=t.lanes;n&=e.pendingLanes,r|=n,t.lanes=r,ss(e,r)}}function Zu(e,t){var r=e.updateQueue,n=e.alternate;if(n!==null&&(n=n.updateQueue,r===n)){var l=null,a=null;if(r=r.firstBaseUpdate,r!==null){do{var o={eventTime:r.eventTime,lane:r.lane,tag:r.tag,payload:r.payload,callback:r.callback,next:null};a===null?l=a=o:a=a.next=o,r=r.next}while(r!==null);a===null?l=a=t:a=a.next=t}else l=a=t;r={baseState:n.baseState,firstBaseUpdate:l,lastBaseUpdate:a,shared:n.shared,effects:n.effects},e.updateQueue=r;return}e=r.lastBaseUpdate,e===null?r.firstBaseUpdate=t:e.next=t,r.lastBaseUpdate=t}function Ta(e,t,r,n){var l=e.updateQueue;Gt=!1;var a=l.firstBaseUpdate,o=l.lastBaseUpdate,i=l.shared.pending;if(i!==null){l.shared.pending=null;var s=i,c=s.next;s.next=null,o===null?a=c:o.next=c,o=s;var d=e.alternate;d!==null&&(d=d.updateQueue,i=d.lastBaseUpdate,i!==o&&(i===null?d.firstBaseUpdate=c:i.next=c,d.lastBaseUpdate=s))}if(a!==null){var f=l.baseState;o=0,d=c=s=null,i=a;do{var p=i.lane,x=i.eventTime;if((n&p)===p){d!==null&&(d=d.next={eventTime:x,lane:0,tag:i.tag,payload:i.payload,callback:i.callback,next:null});e:{var S=e,v=i;switch(p=t,x=r,v.tag){case 1:if(S=v.payload,typeof S=="function"){f=S.call(x,f,p);break e}f=S;break e;case 3:S.flags=S.flags&-65537|128;case 0:if(S=v.payload,p=typeof S=="function"?S.call(x,f,p):S,p==null)break e;f=de({},f,p);break e;case 2:Gt=!0}}i.callback!==null&&i.lane!==0&&(e.flags|=64,p=l.effects,p===null?l.effects=[i]:p.push(i))}else x={eventTime:x,lane:p,tag:i.tag,payload:i.payload,callback:i.callback,next:null},d===null?(c=d=x,s=f):d=d.next=x,o|=p;if(i=i.next,i===null){if(i=l.shared.pending,i===null)break;p=i,i=p.next,p.next=null,l.lastBaseUpdate=p,l.shared.pending=null}}while(!0);if(d===null&&(s=f),l.baseState=s,l.firstBaseUpdate=c,l.lastBaseUpdate=d,t=l.shared.interleaved,t!==null){l=t;do o|=l.lane,l=l.next;while(l!==t)}else a===null&&(l.shared.lanes=0);Mr|=o,e.lanes=o,e.memoizedState=f}}function qu(e,t,r){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var n=e[t],l=n.callback;if(l!==null){if(n.callback=null,n=r,typeof l!="function")throw Error(b(191,l));l.call(n)}}}var Nl={},_t=hr(Nl),hl=hr(Nl),pl=hr(Nl);function Cr(e){if(e===Nl)throw Error(b(174));return e}function Es(e,t){switch(le(pl,t),le(hl,e),le(_t,Nl),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:si(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=si(t,e)}oe(_t),le(_t,t)}function pn(){oe(_t),oe(hl),oe(pl)}function xf(e){Cr(pl.current);var t=Cr(_t.current),r=si(t,e.type);t!==r&&(le(hl,e),le(_t,r))}function Ns(e){hl.current===e&&(oe(_t),oe(hl))}var ue=hr(0);function Ma(e){for(var t=e;t!==null;){if(t.tag===13){var r=t.memoizedState;if(r!==null&&(r=r.dehydrated,r===null||r.data==="$?"||r.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Ao=[];function Cs(){for(var e=0;e<Ao.length;e++)Ao[e]._workInProgressVersionPrimary=null;Ao.length=0}var sa=$t.ReactCurrentDispatcher,Io=$t.ReactCurrentBatchConfig,Tr=0,ce=null,Se=null,Ee=null,za=!1,Zn=!1,ml=0,t0=0;function Te(){throw Error(b(321))}function _s(e,t){if(t===null)return!1;for(var r=0;r<t.length&&r<e.length;r++)if(!kt(e[r],t[r]))return!1;return!0}function Ls(e,t,r,n,l,a){if(Tr=a,ce=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,sa.current=e===null||e.memoizedState===null?a0:o0,e=r(n,l),Zn){a=0;do{if(Zn=!1,ml=0,25<=a)throw Error(b(301));a+=1,Ee=Se=null,t.updateQueue=null,sa.current=i0,e=r(n,l)}while(Zn)}if(sa.current=Fa,t=Se!==null&&Se.next!==null,Tr=0,Ee=Se=ce=null,za=!1,t)throw Error(b(300));return e}function Ps(){var e=ml!==0;return ml=0,e}function Et(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ee===null?ce.memoizedState=Ee=e:Ee=Ee.next=e,Ee}function ht(){if(Se===null){var e=ce.alternate;e=e!==null?e.memoizedState:null}else e=Se.next;var t=Ee===null?ce.memoizedState:Ee.next;if(t!==null)Ee=t,Se=e;else{if(e===null)throw Error(b(310));Se=e,e={memoizedState:Se.memoizedState,baseState:Se.baseState,baseQueue:Se.baseQueue,queue:Se.queue,next:null},Ee===null?ce.memoizedState=Ee=e:Ee=Ee.next=e}return Ee}function gl(e,t){return typeof t=="function"?t(e):t}function $o(e){var t=ht(),r=t.queue;if(r===null)throw Error(b(311));r.lastRenderedReducer=e;var n=Se,l=n.baseQueue,a=r.pending;if(a!==null){if(l!==null){var o=l.next;l.next=a.next,a.next=o}n.baseQueue=l=a,r.pending=null}if(l!==null){a=l.next,n=n.baseState;var i=o=null,s=null,c=a;do{var d=c.lane;if((Tr&d)===d)s!==null&&(s=s.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),n=c.hasEagerState?c.eagerState:e(n,c.action);else{var f={lane:d,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};s===null?(i=s=f,o=n):s=s.next=f,ce.lanes|=d,Mr|=d}c=c.next}while(c!==null&&c!==a);s===null?o=n:s.next=i,kt(n,t.memoizedState)||(We=!0),t.memoizedState=n,t.baseState=o,t.baseQueue=s,r.lastRenderedState=n}if(e=r.interleaved,e!==null){l=e;do a=l.lane,ce.lanes|=a,Mr|=a,l=l.next;while(l!==e)}else l===null&&(r.lanes=0);return[t.memoizedState,r.dispatch]}function Uo(e){var t=ht(),r=t.queue;if(r===null)throw Error(b(311));r.lastRenderedReducer=e;var n=r.dispatch,l=r.pending,a=t.memoizedState;if(l!==null){r.pending=null;var o=l=l.next;do a=e(a,o.action),o=o.next;while(o!==l);kt(a,t.memoizedState)||(We=!0),t.memoizedState=a,t.baseQueue===null&&(t.baseState=a),r.lastRenderedState=a}return[a,n]}function wf(){}function kf(e,t){var r=ce,n=ht(),l=t(),a=!kt(n.memoizedState,l);if(a&&(n.memoizedState=l,We=!0),n=n.queue,bs(Ef.bind(null,r,n,e),[e]),n.getSnapshot!==t||a||Ee!==null&&Ee.memoizedState.tag&1){if(r.flags|=2048,yl(9,jf.bind(null,r,n,l,t),void 0,null),Ne===null)throw Error(b(349));Tr&30||Sf(r,t,l)}return l}function Sf(e,t,r){e.flags|=16384,e={getSnapshot:t,value:r},t=ce.updateQueue,t===null?(t={lastEffect:null,stores:null},ce.updateQueue=t,t.stores=[e]):(r=t.stores,r===null?t.stores=[e]:r.push(e))}function jf(e,t,r,n){t.value=r,t.getSnapshot=n,Nf(t)&&Cf(e)}function Ef(e,t,r){return r(function(){Nf(t)&&Cf(e)})}function Nf(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!kt(e,r)}catch{return!0}}function Cf(e){var t=At(e,1);t!==null&&wt(t,e,1,-1)}function ec(e){var t=Et();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:gl,lastRenderedState:e},t.queue=e,e=e.dispatch=l0.bind(null,ce,e),[t.memoizedState,e]}function yl(e,t,r,n){return e={tag:e,create:t,destroy:r,deps:n,next:null},t=ce.updateQueue,t===null?(t={lastEffect:null,stores:null},ce.updateQueue=t,t.lastEffect=e.next=e):(r=t.lastEffect,r===null?t.lastEffect=e.next=e:(n=r.next,r.next=e,e.next=n,t.lastEffect=e)),e}function _f(){return ht().memoizedState}function ua(e,t,r,n){var l=Et();ce.flags|=e,l.memoizedState=yl(1|t,r,void 0,n===void 0?null:n)}function Za(e,t,r,n){var l=ht();n=n===void 0?null:n;var a=void 0;if(Se!==null){var o=Se.memoizedState;if(a=o.destroy,n!==null&&_s(n,o.deps)){l.memoizedState=yl(t,r,a,n);return}}ce.flags|=e,l.memoizedState=yl(1|t,r,a,n)}function tc(e,t){return ua(8390656,8,e,t)}function bs(e,t){return Za(2048,8,e,t)}function Lf(e,t){return Za(4,2,e,t)}function Pf(e,t){return Za(4,4,e,t)}function bf(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Rf(e,t,r){return r=r!=null?r.concat([e]):null,Za(4,4,bf.bind(null,t,e),r)}function Rs(){}function Tf(e,t){var r=ht();t=t===void 0?null:t;var n=r.memoizedState;return n!==null&&t!==null&&_s(t,n[1])?n[0]:(r.memoizedState=[e,t],e)}function Mf(e,t){var r=ht();t=t===void 0?null:t;var n=r.memoizedState;return n!==null&&t!==null&&_s(t,n[1])?n[0]:(e=e(),r.memoizedState=[e,t],e)}function zf(e,t,r){return Tr&21?(kt(r,t)||(r=Ad(),ce.lanes|=r,Mr|=r,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,We=!0),e.memoizedState=r)}function r0(e,t){var r=te;te=r!==0&&4>r?r:4,e(!0);var n=Io.transition;Io.transition={};try{e(!1),t()}finally{te=r,Io.transition=n}}function Ff(){return ht().memoizedState}function n0(e,t,r){var n=ir(e);if(r={lane:n,action:r,hasEagerState:!1,eagerState:null,next:null},Df(e))Of(t,r);else if(r=yf(e,t,r,n),r!==null){var l=$e();wt(r,e,n,l),Af(r,t,n)}}function l0(e,t,r){var n=ir(e),l={lane:n,action:r,hasEagerState:!1,eagerState:null,next:null};if(Df(e))Of(t,l);else{var a=e.alternate;if(e.lanes===0&&(a===null||a.lanes===0)&&(a=t.lastRenderedReducer,a!==null))try{var o=t.lastRenderedState,i=a(o,r);if(l.hasEagerState=!0,l.eagerState=i,kt(i,o)){var s=t.interleaved;s===null?(l.next=l,Ss(t)):(l.next=s.next,s.next=l),t.interleaved=l;return}}catch{}finally{}r=yf(e,t,l,n),r!==null&&(l=$e(),wt(r,e,n,l),Af(r,t,n))}}function Df(e){var t=e.alternate;return e===ce||t!==null&&t===ce}function Of(e,t){Zn=za=!0;var r=e.pending;r===null?t.next=t:(t.next=r.next,r.next=t),e.pending=t}function Af(e,t,r){if(r&4194240){var n=t.lanes;n&=e.pendingLanes,r|=n,t.lanes=r,ss(e,r)}}var Fa={readContext:ft,useCallback:Te,useContext:Te,useEffect:Te,useImperativeHandle:Te,useInsertionEffect:Te,useLayoutEffect:Te,useMemo:Te,useReducer:Te,useRef:Te,useState:Te,useDebugValue:Te,useDeferredValue:Te,useTransition:Te,useMutableSource:Te,useSyncExternalStore:Te,useId:Te,unstable_isNewReconciler:!1},a0={readContext:ft,useCallback:function(e,t){return Et().memoizedState=[e,t===void 0?null:t],e},useContext:ft,useEffect:tc,useImperativeHandle:function(e,t,r){return r=r!=null?r.concat([e]):null,ua(4194308,4,bf.bind(null,t,e),r)},useLayoutEffect:function(e,t){return ua(4194308,4,e,t)},useInsertionEffect:function(e,t){return ua(4,2,e,t)},useMemo:function(e,t){var r=Et();return t=t===void 0?null:t,e=e(),r.memoizedState=[e,t],e},useReducer:function(e,t,r){var n=Et();return t=r!==void 0?r(t):t,n.memoizedState=n.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},n.queue=e,e=e.dispatch=n0.bind(null,ce,e),[n.memoizedState,e]},useRef:function(e){var t=Et();return e={current:e},t.memoizedState=e},useState:ec,useDebugValue:Rs,useDeferredValue:function(e){return Et().memoizedState=e},useTransition:function(){var e=ec(!1),t=e[0];return e=r0.bind(null,e[1]),Et().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,r){var n=ce,l=Et();if(ie){if(r===void 0)throw Error(b(407));r=r()}else{if(r=t(),Ne===null)throw Error(b(349));Tr&30||Sf(n,t,r)}l.memoizedState=r;var a={value:r,getSnapshot:t};return l.queue=a,tc(Ef.bind(null,n,a,e),[e]),n.flags|=2048,yl(9,jf.bind(null,n,a,r,t),void 0,null),r},useId:function(){var e=Et(),t=Ne.identifierPrefix;if(ie){var r=zt,n=Mt;r=(n&~(1<<32-xt(n)-1)).toString(32)+r,t=":"+t+"R"+r,r=ml++,0<r&&(t+="H"+r.toString(32)),t+=":"}else r=t0++,t=":"+t+"r"+r.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},o0={readContext:ft,useCallback:Tf,useContext:ft,useEffect:bs,useImperativeHandle:Rf,useInsertionEffect:Lf,useLayoutEffect:Pf,useMemo:Mf,useReducer:$o,useRef:_f,useState:function(){return $o(gl)},useDebugValue:Rs,useDeferredValue:function(e){var t=ht();return zf(t,Se.memoizedState,e)},useTransition:function(){var e=$o(gl)[0],t=ht().memoizedState;return[e,t]},useMutableSource:wf,useSyncExternalStore:kf,useId:Ff,unstable_isNewReconciler:!1},i0={readContext:ft,useCallback:Tf,useContext:ft,useEffect:bs,useImperativeHandle:Rf,useInsertionEffect:Lf,useLayoutEffect:Pf,useMemo:Mf,useReducer:Uo,useRef:_f,useState:function(){return Uo(gl)},useDebugValue:Rs,useDeferredValue:function(e){var t=ht();return Se===null?t.memoizedState=e:zf(t,Se.memoizedState,e)},useTransition:function(){var e=Uo(gl)[0],t=ht().memoizedState;return[e,t]},useMutableSource:wf,useSyncExternalStore:kf,useId:Ff,unstable_isNewReconciler:!1};function gt(e,t){if(e&&e.defaultProps){t=de({},t),e=e.defaultProps;for(var r in e)t[r]===void 0&&(t[r]=e[r]);return t}return t}function Pi(e,t,r,n){t=e.memoizedState,r=r(n,t),r=r==null?t:de({},t,r),e.memoizedState=r,e.lanes===0&&(e.updateQueue.baseState=r)}var qa={isMounted:function(e){return(e=e._reactInternals)?Dr(e)===e:!1},enqueueSetState:function(e,t,r){e=e._reactInternals;var n=$e(),l=ir(e),a=Ft(n,l);a.payload=t,r!=null&&(a.callback=r),t=ar(e,a,l),t!==null&&(wt(t,e,l,n),ia(t,e,l))},enqueueReplaceState:function(e,t,r){e=e._reactInternals;var n=$e(),l=ir(e),a=Ft(n,l);a.tag=1,a.payload=t,r!=null&&(a.callback=r),t=ar(e,a,l),t!==null&&(wt(t,e,l,n),ia(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var r=$e(),n=ir(e),l=Ft(r,n);l.tag=2,t!=null&&(l.callback=t),t=ar(e,l,n),t!==null&&(wt(t,e,n,r),ia(t,e,n))}};function rc(e,t,r,n,l,a,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(n,a,o):t.prototype&&t.prototype.isPureReactComponent?!ul(r,n)||!ul(l,a):!0}function If(e,t,r){var n=!1,l=cr,a=t.contextType;return typeof a=="object"&&a!==null?a=ft(a):(l=Qe(t)?br:Fe.current,n=t.contextTypes,a=(n=n!=null)?dn(e,l):cr),t=new t(r,a),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=qa,e.stateNode=t,t._reactInternals=e,n&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=l,e.__reactInternalMemoizedMaskedChildContext=a),t}function nc(e,t,r,n){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(r,n),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(r,n),t.state!==e&&qa.enqueueReplaceState(t,t.state,null)}function bi(e,t,r,n){var l=e.stateNode;l.props=r,l.state=e.memoizedState,l.refs={},js(e);var a=t.contextType;typeof a=="object"&&a!==null?l.context=ft(a):(a=Qe(t)?br:Fe.current,l.context=dn(e,a)),l.state=e.memoizedState,a=t.getDerivedStateFromProps,typeof a=="function"&&(Pi(e,t,a,r),l.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof l.getSnapshotBeforeUpdate=="function"||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(t=l.state,typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount(),t!==l.state&&qa.enqueueReplaceState(l,l.state,null),Ta(e,r,l,n),l.state=e.memoizedState),typeof l.componentDidMount=="function"&&(e.flags|=4194308)}function mn(e,t){try{var r="",n=t;do r+=Fp(n),n=n.return;while(n);var l=r}catch(a){l=`
Error generating stack: `+a.message+`
`+a.stack}return{value:e,source:t,stack:l,digest:null}}function Bo(e,t,r){return{value:e,source:null,stack:r??null,digest:t??null}}function Ri(e,t){try{console.error(t.value)}catch(r){setTimeout(function(){throw r})}}var s0=typeof WeakMap=="function"?WeakMap:Map;function $f(e,t,r){r=Ft(-1,r),r.tag=3,r.payload={element:null};var n=t.value;return r.callback=function(){Oa||(Oa=!0,Ui=n),Ri(e,t)},r}function Uf(e,t,r){r=Ft(-1,r),r.tag=3;var n=e.type.getDerivedStateFromError;if(typeof n=="function"){var l=t.value;r.payload=function(){return n(l)},r.callback=function(){Ri(e,t)}}var a=e.stateNode;return a!==null&&typeof a.componentDidCatch=="function"&&(r.callback=function(){Ri(e,t),typeof n!="function"&&(or===null?or=new Set([this]):or.add(this));var o=t.stack;this.componentDidCatch(t.value,{componentStack:o!==null?o:""})}),r}function lc(e,t,r){var n=e.pingCache;if(n===null){n=e.pingCache=new s0;var l=new Set;n.set(t,l)}else l=n.get(t),l===void 0&&(l=new Set,n.set(t,l));l.has(r)||(l.add(r),e=S0.bind(null,e,t,r),t.then(e,e))}function ac(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function oc(e,t,r,n,l){return e.mode&1?(e.flags|=65536,e.lanes=l,e):(e===t?e.flags|=65536:(e.flags|=128,r.flags|=131072,r.flags&=-52805,r.tag===1&&(r.alternate===null?r.tag=17:(t=Ft(-1,1),t.tag=2,ar(r,t,1))),r.lanes|=1),e)}var u0=$t.ReactCurrentOwner,We=!1;function Ie(e,t,r,n){t.child=e===null?gf(t,null,r,n):hn(t,e.child,r,n)}function ic(e,t,r,n,l){r=r.render;var a=t.ref;return on(t,l),n=Ls(e,t,r,n,a,l),r=Ps(),e!==null&&!We?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,It(e,t,l)):(ie&&r&&gs(t),t.flags|=1,Ie(e,t,n,l),t.child)}function sc(e,t,r,n,l){if(e===null){var a=r.type;return typeof a=="function"&&!Is(a)&&a.defaultProps===void 0&&r.compare===null&&r.defaultProps===void 0?(t.tag=15,t.type=a,Bf(e,t,a,n,l)):(e=ha(r.type,null,n,t,t.mode,l),e.ref=t.ref,e.return=t,t.child=e)}if(a=e.child,!(e.lanes&l)){var o=a.memoizedProps;if(r=r.compare,r=r!==null?r:ul,r(o,n)&&e.ref===t.ref)return It(e,t,l)}return t.flags|=1,e=sr(a,n),e.ref=t.ref,e.return=t,t.child=e}function Bf(e,t,r,n,l){if(e!==null){var a=e.memoizedProps;if(ul(a,n)&&e.ref===t.ref)if(We=!1,t.pendingProps=n=a,(e.lanes&l)!==0)e.flags&131072&&(We=!0);else return t.lanes=e.lanes,It(e,t,l)}return Ti(e,t,r,n,l)}function Hf(e,t,r){var n=t.pendingProps,l=n.children,a=e!==null?e.memoizedState:null;if(n.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},le(tn,Je),Je|=r;else{if(!(r&1073741824))return e=a!==null?a.baseLanes|r:r,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,le(tn,Je),Je|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},n=a!==null?a.baseLanes:r,le(tn,Je),Je|=n}else a!==null?(n=a.baseLanes|r,t.memoizedState=null):n=r,le(tn,Je),Je|=n;return Ie(e,t,l,r),t.child}function Wf(e,t){var r=t.ref;(e===null&&r!==null||e!==null&&e.ref!==r)&&(t.flags|=512,t.flags|=2097152)}function Ti(e,t,r,n,l){var a=Qe(r)?br:Fe.current;return a=dn(t,a),on(t,l),r=Ls(e,t,r,n,a,l),n=Ps(),e!==null&&!We?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,It(e,t,l)):(ie&&n&&gs(t),t.flags|=1,Ie(e,t,r,l),t.child)}function uc(e,t,r,n,l){if(Qe(r)){var a=!0;_a(t)}else a=!1;if(on(t,l),t.stateNode===null)ca(e,t),If(t,r,n),bi(t,r,n,l),n=!0;else if(e===null){var o=t.stateNode,i=t.memoizedProps;o.props=i;var s=o.context,c=r.contextType;typeof c=="object"&&c!==null?c=ft(c):(c=Qe(r)?br:Fe.current,c=dn(t,c));var d=r.getDerivedStateFromProps,f=typeof d=="function"||typeof o.getSnapshotBeforeUpdate=="function";f||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(i!==n||s!==c)&&nc(t,o,n,c),Gt=!1;var p=t.memoizedState;o.state=p,Ta(t,n,o,l),s=t.memoizedState,i!==n||p!==s||Ve.current||Gt?(typeof d=="function"&&(Pi(t,r,d,n),s=t.memoizedState),(i=Gt||rc(t,r,i,n,p,s,c))?(f||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=n,t.memoizedState=s),o.props=n,o.state=s,o.context=c,n=i):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),n=!1)}else{o=t.stateNode,vf(e,t),i=t.memoizedProps,c=t.type===t.elementType?i:gt(t.type,i),o.props=c,f=t.pendingProps,p=o.context,s=r.contextType,typeof s=="object"&&s!==null?s=ft(s):(s=Qe(r)?br:Fe.current,s=dn(t,s));var x=r.getDerivedStateFromProps;(d=typeof x=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(i!==f||p!==s)&&nc(t,o,n,s),Gt=!1,p=t.memoizedState,o.state=p,Ta(t,n,o,l);var S=t.memoizedState;i!==f||p!==S||Ve.current||Gt?(typeof x=="function"&&(Pi(t,r,x,n),S=t.memoizedState),(c=Gt||rc(t,r,c,n,p,S,s)||!1)?(d||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(n,S,s),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(n,S,s)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||i===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||i===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),t.memoizedProps=n,t.memoizedState=S),o.props=n,o.state=S,o.context=s,n=c):(typeof o.componentDidUpdate!="function"||i===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||i===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),n=!1)}return Mi(e,t,r,n,a,l)}function Mi(e,t,r,n,l,a){Wf(e,t);var o=(t.flags&128)!==0;if(!n&&!o)return l&&Yu(t,r,!1),It(e,t,a);n=t.stateNode,u0.current=t;var i=o&&typeof r.getDerivedStateFromError!="function"?null:n.render();return t.flags|=1,e!==null&&o?(t.child=hn(t,e.child,null,a),t.child=hn(t,null,i,a)):Ie(e,t,i,a),t.memoizedState=n.state,l&&Yu(t,r,!0),t.child}function Vf(e){var t=e.stateNode;t.pendingContext?Ku(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Ku(e,t.context,!1),Es(e,t.containerInfo)}function cc(e,t,r,n,l){return fn(),vs(l),t.flags|=256,Ie(e,t,r,n),t.child}var zi={dehydrated:null,treeContext:null,retryLane:0};function Fi(e){return{baseLanes:e,cachePool:null,transitions:null}}function Qf(e,t,r){var n=t.pendingProps,l=ue.current,a=!1,o=(t.flags&128)!==0,i;if((i=o)||(i=e!==null&&e.memoizedState===null?!1:(l&2)!==0),i?(a=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(l|=1),le(ue,l&1),e===null)return _i(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(o=n.children,e=n.fallback,a?(n=t.mode,a=t.child,o={mode:"hidden",children:o},!(n&1)&&a!==null?(a.childLanes=0,a.pendingProps=o):a=ro(o,n,0,null),e=Pr(e,n,r,null),a.return=t,e.return=t,a.sibling=e,t.child=a,t.child.memoizedState=Fi(r),t.memoizedState=zi,e):Ts(t,o));if(l=e.memoizedState,l!==null&&(i=l.dehydrated,i!==null))return c0(e,t,o,n,i,l,r);if(a){a=n.fallback,o=t.mode,l=e.child,i=l.sibling;var s={mode:"hidden",children:n.children};return!(o&1)&&t.child!==l?(n=t.child,n.childLanes=0,n.pendingProps=s,t.deletions=null):(n=sr(l,s),n.subtreeFlags=l.subtreeFlags&14680064),i!==null?a=sr(i,a):(a=Pr(a,o,r,null),a.flags|=2),a.return=t,n.return=t,n.sibling=a,t.child=n,n=a,a=t.child,o=e.child.memoizedState,o=o===null?Fi(r):{baseLanes:o.baseLanes|r,cachePool:null,transitions:o.transitions},a.memoizedState=o,a.childLanes=e.childLanes&~r,t.memoizedState=zi,n}return a=e.child,e=a.sibling,n=sr(a,{mode:"visible",children:n.children}),!(t.mode&1)&&(n.lanes=r),n.return=t,n.sibling=null,e!==null&&(r=t.deletions,r===null?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=n,t.memoizedState=null,n}function Ts(e,t){return t=ro({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Yl(e,t,r,n){return n!==null&&vs(n),hn(t,e.child,null,r),e=Ts(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function c0(e,t,r,n,l,a,o){if(r)return t.flags&256?(t.flags&=-257,n=Bo(Error(b(422))),Yl(e,t,o,n)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(a=n.fallback,l=t.mode,n=ro({mode:"visible",children:n.children},l,0,null),a=Pr(a,l,o,null),a.flags|=2,n.return=t,a.return=t,n.sibling=a,t.child=n,t.mode&1&&hn(t,e.child,null,o),t.child.memoizedState=Fi(o),t.memoizedState=zi,a);if(!(t.mode&1))return Yl(e,t,o,null);if(l.data==="$!"){if(n=l.nextSibling&&l.nextSibling.dataset,n)var i=n.dgst;return n=i,a=Error(b(419)),n=Bo(a,n,void 0),Yl(e,t,o,n)}if(i=(o&e.childLanes)!==0,We||i){if(n=Ne,n!==null){switch(o&-o){case 4:l=2;break;case 16:l=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:l=32;break;case 536870912:l=268435456;break;default:l=0}l=l&(n.suspendedLanes|o)?0:l,l!==0&&l!==a.retryLane&&(a.retryLane=l,At(e,l),wt(n,e,l,-1))}return As(),n=Bo(Error(b(421))),Yl(e,t,o,n)}return l.data==="$?"?(t.flags|=128,t.child=e.child,t=j0.bind(null,e),l._reactRetry=t,null):(e=a.treeContext,et=lr(l.nextSibling),tt=t,ie=!0,vt=null,e!==null&&(st[ut++]=Mt,st[ut++]=zt,st[ut++]=Rr,Mt=e.id,zt=e.overflow,Rr=t),t=Ts(t,n.children),t.flags|=4096,t)}function dc(e,t,r){e.lanes|=t;var n=e.alternate;n!==null&&(n.lanes|=t),Li(e.return,t,r)}function Ho(e,t,r,n,l){var a=e.memoizedState;a===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:n,tail:r,tailMode:l}:(a.isBackwards=t,a.rendering=null,a.renderingStartTime=0,a.last=n,a.tail=r,a.tailMode=l)}function Kf(e,t,r){var n=t.pendingProps,l=n.revealOrder,a=n.tail;if(Ie(e,t,n.children,r),n=ue.current,n&2)n=n&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&dc(e,r,t);else if(e.tag===19)dc(e,r,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}n&=1}if(le(ue,n),!(t.mode&1))t.memoizedState=null;else switch(l){case"forwards":for(r=t.child,l=null;r!==null;)e=r.alternate,e!==null&&Ma(e)===null&&(l=r),r=r.sibling;r=l,r===null?(l=t.child,t.child=null):(l=r.sibling,r.sibling=null),Ho(t,!1,l,r,a);break;case"backwards":for(r=null,l=t.child,t.child=null;l!==null;){if(e=l.alternate,e!==null&&Ma(e)===null){t.child=l;break}e=l.sibling,l.sibling=r,r=l,l=e}Ho(t,!0,r,null,a);break;case"together":Ho(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function ca(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function It(e,t,r){if(e!==null&&(t.dependencies=e.dependencies),Mr|=t.lanes,!(r&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(b(153));if(t.child!==null){for(e=t.child,r=sr(e,e.pendingProps),t.child=r,r.return=t;e.sibling!==null;)e=e.sibling,r=r.sibling=sr(e,e.pendingProps),r.return=t;r.sibling=null}return t.child}function d0(e,t,r){switch(t.tag){case 3:Vf(t),fn();break;case 5:xf(t);break;case 1:Qe(t.type)&&_a(t);break;case 4:Es(t,t.stateNode.containerInfo);break;case 10:var n=t.type._context,l=t.memoizedProps.value;le(ba,n._currentValue),n._currentValue=l;break;case 13:if(n=t.memoizedState,n!==null)return n.dehydrated!==null?(le(ue,ue.current&1),t.flags|=128,null):r&t.child.childLanes?Qf(e,t,r):(le(ue,ue.current&1),e=It(e,t,r),e!==null?e.sibling:null);le(ue,ue.current&1);break;case 19:if(n=(r&t.childLanes)!==0,e.flags&128){if(n)return Kf(e,t,r);t.flags|=128}if(l=t.memoizedState,l!==null&&(l.rendering=null,l.tail=null,l.lastEffect=null),le(ue,ue.current),n)break;return null;case 22:case 23:return t.lanes=0,Hf(e,t,r)}return It(e,t,r)}var Yf,Di,Gf,Xf;Yf=function(e,t){for(var r=t.child;r!==null;){if(r.tag===5||r.tag===6)e.appendChild(r.stateNode);else if(r.tag!==4&&r.child!==null){r.child.return=r,r=r.child;continue}if(r===t)break;for(;r.sibling===null;){if(r.return===null||r.return===t)return;r=r.return}r.sibling.return=r.return,r=r.sibling}};Di=function(){};Gf=function(e,t,r,n){var l=e.memoizedProps;if(l!==n){e=t.stateNode,Cr(_t.current);var a=null;switch(r){case"input":l=li(e,l),n=li(e,n),a=[];break;case"select":l=de({},l,{value:void 0}),n=de({},n,{value:void 0}),a=[];break;case"textarea":l=ii(e,l),n=ii(e,n),a=[];break;default:typeof l.onClick!="function"&&typeof n.onClick=="function"&&(e.onclick=Na)}ui(r,n);var o;r=null;for(c in l)if(!n.hasOwnProperty(c)&&l.hasOwnProperty(c)&&l[c]!=null)if(c==="style"){var i=l[c];for(o in i)i.hasOwnProperty(o)&&(r||(r={}),r[o]="")}else c!=="dangerouslySetInnerHTML"&&c!=="children"&&c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(rl.hasOwnProperty(c)?a||(a=[]):(a=a||[]).push(c,null));for(c in n){var s=n[c];if(i=l!=null?l[c]:void 0,n.hasOwnProperty(c)&&s!==i&&(s!=null||i!=null))if(c==="style")if(i){for(o in i)!i.hasOwnProperty(o)||s&&s.hasOwnProperty(o)||(r||(r={}),r[o]="");for(o in s)s.hasOwnProperty(o)&&i[o]!==s[o]&&(r||(r={}),r[o]=s[o])}else r||(a||(a=[]),a.push(c,r)),r=s;else c==="dangerouslySetInnerHTML"?(s=s?s.__html:void 0,i=i?i.__html:void 0,s!=null&&i!==s&&(a=a||[]).push(c,s)):c==="children"?typeof s!="string"&&typeof s!="number"||(a=a||[]).push(c,""+s):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&(rl.hasOwnProperty(c)?(s!=null&&c==="onScroll"&&ae("scroll",e),a||i===s||(a=[])):(a=a||[]).push(c,s))}r&&(a=a||[]).push("style",r);var c=a;(t.updateQueue=c)&&(t.flags|=4)}};Xf=function(e,t,r,n){r!==n&&(t.flags|=4)};function Mn(e,t){if(!ie)switch(e.tailMode){case"hidden":t=e.tail;for(var r=null;t!==null;)t.alternate!==null&&(r=t),t=t.sibling;r===null?e.tail=null:r.sibling=null;break;case"collapsed":r=e.tail;for(var n=null;r!==null;)r.alternate!==null&&(n=r),r=r.sibling;n===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:n.sibling=null}}function Me(e){var t=e.alternate!==null&&e.alternate.child===e.child,r=0,n=0;if(t)for(var l=e.child;l!==null;)r|=l.lanes|l.childLanes,n|=l.subtreeFlags&14680064,n|=l.flags&14680064,l.return=e,l=l.sibling;else for(l=e.child;l!==null;)r|=l.lanes|l.childLanes,n|=l.subtreeFlags,n|=l.flags,l.return=e,l=l.sibling;return e.subtreeFlags|=n,e.childLanes=r,t}function f0(e,t,r){var n=t.pendingProps;switch(ys(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Me(t),null;case 1:return Qe(t.type)&&Ca(),Me(t),null;case 3:return n=t.stateNode,pn(),oe(Ve),oe(Fe),Cs(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(e===null||e.child===null)&&(Ql(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,vt!==null&&(Wi(vt),vt=null))),Di(e,t),Me(t),null;case 5:Ns(t);var l=Cr(pl.current);if(r=t.type,e!==null&&t.stateNode!=null)Gf(e,t,r,n,l),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!n){if(t.stateNode===null)throw Error(b(166));return Me(t),null}if(e=Cr(_t.current),Ql(t)){n=t.stateNode,r=t.type;var a=t.memoizedProps;switch(n[Nt]=t,n[fl]=a,e=(t.mode&1)!==0,r){case"dialog":ae("cancel",n),ae("close",n);break;case"iframe":case"object":case"embed":ae("load",n);break;case"video":case"audio":for(l=0;l<Hn.length;l++)ae(Hn[l],n);break;case"source":ae("error",n);break;case"img":case"image":case"link":ae("error",n),ae("load",n);break;case"details":ae("toggle",n);break;case"input":wu(n,a),ae("invalid",n);break;case"select":n._wrapperState={wasMultiple:!!a.multiple},ae("invalid",n);break;case"textarea":Su(n,a),ae("invalid",n)}ui(r,a),l=null;for(var o in a)if(a.hasOwnProperty(o)){var i=a[o];o==="children"?typeof i=="string"?n.textContent!==i&&(a.suppressHydrationWarning!==!0&&Vl(n.textContent,i,e),l=["children",i]):typeof i=="number"&&n.textContent!==""+i&&(a.suppressHydrationWarning!==!0&&Vl(n.textContent,i,e),l=["children",""+i]):rl.hasOwnProperty(o)&&i!=null&&o==="onScroll"&&ae("scroll",n)}switch(r){case"input":Ol(n),ku(n,a,!0);break;case"textarea":Ol(n),ju(n);break;case"select":case"option":break;default:typeof a.onClick=="function"&&(n.onclick=Na)}n=l,t.updateQueue=n,n!==null&&(t.flags|=4)}else{o=l.nodeType===9?l:l.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=jd(r)),e==="http://www.w3.org/1999/xhtml"?r==="script"?(e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof n.is=="string"?e=o.createElement(r,{is:n.is}):(e=o.createElement(r),r==="select"&&(o=e,n.multiple?o.multiple=!0:n.size&&(o.size=n.size))):e=o.createElementNS(e,r),e[Nt]=t,e[fl]=n,Yf(e,t,!1,!1),t.stateNode=e;e:{switch(o=ci(r,n),r){case"dialog":ae("cancel",e),ae("close",e),l=n;break;case"iframe":case"object":case"embed":ae("load",e),l=n;break;case"video":case"audio":for(l=0;l<Hn.length;l++)ae(Hn[l],e);l=n;break;case"source":ae("error",e),l=n;break;case"img":case"image":case"link":ae("error",e),ae("load",e),l=n;break;case"details":ae("toggle",e),l=n;break;case"input":wu(e,n),l=li(e,n),ae("invalid",e);break;case"option":l=n;break;case"select":e._wrapperState={wasMultiple:!!n.multiple},l=de({},n,{value:void 0}),ae("invalid",e);break;case"textarea":Su(e,n),l=ii(e,n),ae("invalid",e);break;default:l=n}ui(r,l),i=l;for(a in i)if(i.hasOwnProperty(a)){var s=i[a];a==="style"?Cd(e,s):a==="dangerouslySetInnerHTML"?(s=s?s.__html:void 0,s!=null&&Ed(e,s)):a==="children"?typeof s=="string"?(r!=="textarea"||s!=="")&&nl(e,s):typeof s=="number"&&nl(e,""+s):a!=="suppressContentEditableWarning"&&a!=="suppressHydrationWarning"&&a!=="autoFocus"&&(rl.hasOwnProperty(a)?s!=null&&a==="onScroll"&&ae("scroll",e):s!=null&&rs(e,a,s,o))}switch(r){case"input":Ol(e),ku(e,n,!1);break;case"textarea":Ol(e),ju(e);break;case"option":n.value!=null&&e.setAttribute("value",""+ur(n.value));break;case"select":e.multiple=!!n.multiple,a=n.value,a!=null?rn(e,!!n.multiple,a,!1):n.defaultValue!=null&&rn(e,!!n.multiple,n.defaultValue,!0);break;default:typeof l.onClick=="function"&&(e.onclick=Na)}switch(r){case"button":case"input":case"select":case"textarea":n=!!n.autoFocus;break e;case"img":n=!0;break e;default:n=!1}}n&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Me(t),null;case 6:if(e&&t.stateNode!=null)Xf(e,t,e.memoizedProps,n);else{if(typeof n!="string"&&t.stateNode===null)throw Error(b(166));if(r=Cr(pl.current),Cr(_t.current),Ql(t)){if(n=t.stateNode,r=t.memoizedProps,n[Nt]=t,(a=n.nodeValue!==r)&&(e=tt,e!==null))switch(e.tag){case 3:Vl(n.nodeValue,r,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Vl(n.nodeValue,r,(e.mode&1)!==0)}a&&(t.flags|=4)}else n=(r.nodeType===9?r:r.ownerDocument).createTextNode(n),n[Nt]=t,t.stateNode=n}return Me(t),null;case 13:if(oe(ue),n=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(ie&&et!==null&&t.mode&1&&!(t.flags&128))pf(),fn(),t.flags|=98560,a=!1;else if(a=Ql(t),n!==null&&n.dehydrated!==null){if(e===null){if(!a)throw Error(b(318));if(a=t.memoizedState,a=a!==null?a.dehydrated:null,!a)throw Error(b(317));a[Nt]=t}else fn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Me(t),a=!1}else vt!==null&&(Wi(vt),vt=null),a=!0;if(!a)return t.flags&65536?t:null}return t.flags&128?(t.lanes=r,t):(n=n!==null,n!==(e!==null&&e.memoizedState!==null)&&n&&(t.child.flags|=8192,t.mode&1&&(e===null||ue.current&1?je===0&&(je=3):As())),t.updateQueue!==null&&(t.flags|=4),Me(t),null);case 4:return pn(),Di(e,t),e===null&&cl(t.stateNode.containerInfo),Me(t),null;case 10:return ks(t.type._context),Me(t),null;case 17:return Qe(t.type)&&Ca(),Me(t),null;case 19:if(oe(ue),a=t.memoizedState,a===null)return Me(t),null;if(n=(t.flags&128)!==0,o=a.rendering,o===null)if(n)Mn(a,!1);else{if(je!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(o=Ma(e),o!==null){for(t.flags|=128,Mn(a,!1),n=o.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),t.subtreeFlags=0,n=r,r=t.child;r!==null;)a=r,e=n,a.flags&=14680066,o=a.alternate,o===null?(a.childLanes=0,a.lanes=e,a.child=null,a.subtreeFlags=0,a.memoizedProps=null,a.memoizedState=null,a.updateQueue=null,a.dependencies=null,a.stateNode=null):(a.childLanes=o.childLanes,a.lanes=o.lanes,a.child=o.child,a.subtreeFlags=0,a.deletions=null,a.memoizedProps=o.memoizedProps,a.memoizedState=o.memoizedState,a.updateQueue=o.updateQueue,a.type=o.type,e=o.dependencies,a.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),r=r.sibling;return le(ue,ue.current&1|2),t.child}e=e.sibling}a.tail!==null&&pe()>gn&&(t.flags|=128,n=!0,Mn(a,!1),t.lanes=4194304)}else{if(!n)if(e=Ma(o),e!==null){if(t.flags|=128,n=!0,r=e.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),Mn(a,!0),a.tail===null&&a.tailMode==="hidden"&&!o.alternate&&!ie)return Me(t),null}else 2*pe()-a.renderingStartTime>gn&&r!==1073741824&&(t.flags|=128,n=!0,Mn(a,!1),t.lanes=4194304);a.isBackwards?(o.sibling=t.child,t.child=o):(r=a.last,r!==null?r.sibling=o:t.child=o,a.last=o)}return a.tail!==null?(t=a.tail,a.rendering=t,a.tail=t.sibling,a.renderingStartTime=pe(),t.sibling=null,r=ue.current,le(ue,n?r&1|2:r&1),t):(Me(t),null);case 22:case 23:return Os(),n=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==n&&(t.flags|=8192),n&&t.mode&1?Je&1073741824&&(Me(t),t.subtreeFlags&6&&(t.flags|=8192)):Me(t),null;case 24:return null;case 25:return null}throw Error(b(156,t.tag))}function h0(e,t){switch(ys(t),t.tag){case 1:return Qe(t.type)&&Ca(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return pn(),oe(Ve),oe(Fe),Cs(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Ns(t),null;case 13:if(oe(ue),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(b(340));fn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return oe(ue),null;case 4:return pn(),null;case 10:return ks(t.type._context),null;case 22:case 23:return Os(),null;case 24:return null;default:return null}}var Gl=!1,ze=!1,p0=typeof WeakSet=="function"?WeakSet:Set,z=null;function en(e,t){var r=e.ref;if(r!==null)if(typeof r=="function")try{r(null)}catch(n){he(e,t,n)}else r.current=null}function Oi(e,t,r){try{r()}catch(n){he(e,t,n)}}var fc=!1;function m0(e,t){if(wi=Sa,e=ef(),ms(e)){if("selectionStart"in e)var r={start:e.selectionStart,end:e.selectionEnd};else e:{r=(r=e.ownerDocument)&&r.defaultView||window;var n=r.getSelection&&r.getSelection();if(n&&n.rangeCount!==0){r=n.anchorNode;var l=n.anchorOffset,a=n.focusNode;n=n.focusOffset;try{r.nodeType,a.nodeType}catch{r=null;break e}var o=0,i=-1,s=-1,c=0,d=0,f=e,p=null;t:for(;;){for(var x;f!==r||l!==0&&f.nodeType!==3||(i=o+l),f!==a||n!==0&&f.nodeType!==3||(s=o+n),f.nodeType===3&&(o+=f.nodeValue.length),(x=f.firstChild)!==null;)p=f,f=x;for(;;){if(f===e)break t;if(p===r&&++c===l&&(i=o),p===a&&++d===n&&(s=o),(x=f.nextSibling)!==null)break;f=p,p=f.parentNode}f=x}r=i===-1||s===-1?null:{start:i,end:s}}else r=null}r=r||{start:0,end:0}}else r=null;for(ki={focusedElem:e,selectionRange:r},Sa=!1,z=t;z!==null;)if(t=z,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,z=e;else for(;z!==null;){t=z;try{var S=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(S!==null){var v=S.memoizedProps,_=S.memoizedState,h=t.stateNode,m=h.getSnapshotBeforeUpdate(t.elementType===t.type?v:gt(t.type,v),_);h.__reactInternalSnapshotBeforeUpdate=m}break;case 3:var g=t.stateNode.containerInfo;g.nodeType===1?g.textContent="":g.nodeType===9&&g.documentElement&&g.removeChild(g.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(b(163))}}catch(E){he(t,t.return,E)}if(e=t.sibling,e!==null){e.return=t.return,z=e;break}z=t.return}return S=fc,fc=!1,S}function qn(e,t,r){var n=t.updateQueue;if(n=n!==null?n.lastEffect:null,n!==null){var l=n=n.next;do{if((l.tag&e)===e){var a=l.destroy;l.destroy=void 0,a!==void 0&&Oi(t,r,a)}l=l.next}while(l!==n)}}function eo(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var r=t=t.next;do{if((r.tag&e)===e){var n=r.create;r.destroy=n()}r=r.next}while(r!==t)}}function Ai(e){var t=e.ref;if(t!==null){var r=e.stateNode;switch(e.tag){case 5:e=r;break;default:e=r}typeof t=="function"?t(e):t.current=e}}function Jf(e){var t=e.alternate;t!==null&&(e.alternate=null,Jf(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Nt],delete t[fl],delete t[Ei],delete t[Jm],delete t[Zm])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Zf(e){return e.tag===5||e.tag===3||e.tag===4}function hc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Zf(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Ii(e,t,r){var n=e.tag;if(n===5||n===6)e=e.stateNode,t?r.nodeType===8?r.parentNode.insertBefore(e,t):r.insertBefore(e,t):(r.nodeType===8?(t=r.parentNode,t.insertBefore(e,r)):(t=r,t.appendChild(e)),r=r._reactRootContainer,r!=null||t.onclick!==null||(t.onclick=Na));else if(n!==4&&(e=e.child,e!==null))for(Ii(e,t,r),e=e.sibling;e!==null;)Ii(e,t,r),e=e.sibling}function $i(e,t,r){var n=e.tag;if(n===5||n===6)e=e.stateNode,t?r.insertBefore(e,t):r.appendChild(e);else if(n!==4&&(e=e.child,e!==null))for($i(e,t,r),e=e.sibling;e!==null;)$i(e,t,r),e=e.sibling}var Ce=null,yt=!1;function Qt(e,t,r){for(r=r.child;r!==null;)qf(e,t,r),r=r.sibling}function qf(e,t,r){if(Ct&&typeof Ct.onCommitFiberUnmount=="function")try{Ct.onCommitFiberUnmount(Qa,r)}catch{}switch(r.tag){case 5:ze||en(r,t);case 6:var n=Ce,l=yt;Ce=null,Qt(e,t,r),Ce=n,yt=l,Ce!==null&&(yt?(e=Ce,r=r.stateNode,e.nodeType===8?e.parentNode.removeChild(r):e.removeChild(r)):Ce.removeChild(r.stateNode));break;case 18:Ce!==null&&(yt?(e=Ce,r=r.stateNode,e.nodeType===8?Do(e.parentNode,r):e.nodeType===1&&Do(e,r),il(e)):Do(Ce,r.stateNode));break;case 4:n=Ce,l=yt,Ce=r.stateNode.containerInfo,yt=!0,Qt(e,t,r),Ce=n,yt=l;break;case 0:case 11:case 14:case 15:if(!ze&&(n=r.updateQueue,n!==null&&(n=n.lastEffect,n!==null))){l=n=n.next;do{var a=l,o=a.destroy;a=a.tag,o!==void 0&&(a&2||a&4)&&Oi(r,t,o),l=l.next}while(l!==n)}Qt(e,t,r);break;case 1:if(!ze&&(en(r,t),n=r.stateNode,typeof n.componentWillUnmount=="function"))try{n.props=r.memoizedProps,n.state=r.memoizedState,n.componentWillUnmount()}catch(i){he(r,t,i)}Qt(e,t,r);break;case 21:Qt(e,t,r);break;case 22:r.mode&1?(ze=(n=ze)||r.memoizedState!==null,Qt(e,t,r),ze=n):Qt(e,t,r);break;default:Qt(e,t,r)}}function pc(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var r=e.stateNode;r===null&&(r=e.stateNode=new p0),t.forEach(function(n){var l=E0.bind(null,e,n);r.has(n)||(r.add(n),n.then(l,l))})}}function mt(e,t){var r=t.deletions;if(r!==null)for(var n=0;n<r.length;n++){var l=r[n];try{var a=e,o=t,i=o;e:for(;i!==null;){switch(i.tag){case 5:Ce=i.stateNode,yt=!1;break e;case 3:Ce=i.stateNode.containerInfo,yt=!0;break e;case 4:Ce=i.stateNode.containerInfo,yt=!0;break e}i=i.return}if(Ce===null)throw Error(b(160));qf(a,o,l),Ce=null,yt=!1;var s=l.alternate;s!==null&&(s.return=null),l.return=null}catch(c){he(l,t,c)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)eh(t,e),t=t.sibling}function eh(e,t){var r=e.alternate,n=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(mt(t,e),jt(e),n&4){try{qn(3,e,e.return),eo(3,e)}catch(v){he(e,e.return,v)}try{qn(5,e,e.return)}catch(v){he(e,e.return,v)}}break;case 1:mt(t,e),jt(e),n&512&&r!==null&&en(r,r.return);break;case 5:if(mt(t,e),jt(e),n&512&&r!==null&&en(r,r.return),e.flags&32){var l=e.stateNode;try{nl(l,"")}catch(v){he(e,e.return,v)}}if(n&4&&(l=e.stateNode,l!=null)){var a=e.memoizedProps,o=r!==null?r.memoizedProps:a,i=e.type,s=e.updateQueue;if(e.updateQueue=null,s!==null)try{i==="input"&&a.type==="radio"&&a.name!=null&&kd(l,a),ci(i,o);var c=ci(i,a);for(o=0;o<s.length;o+=2){var d=s[o],f=s[o+1];d==="style"?Cd(l,f):d==="dangerouslySetInnerHTML"?Ed(l,f):d==="children"?nl(l,f):rs(l,d,f,c)}switch(i){case"input":ai(l,a);break;case"textarea":Sd(l,a);break;case"select":var p=l._wrapperState.wasMultiple;l._wrapperState.wasMultiple=!!a.multiple;var x=a.value;x!=null?rn(l,!!a.multiple,x,!1):p!==!!a.multiple&&(a.defaultValue!=null?rn(l,!!a.multiple,a.defaultValue,!0):rn(l,!!a.multiple,a.multiple?[]:"",!1))}l[fl]=a}catch(v){he(e,e.return,v)}}break;case 6:if(mt(t,e),jt(e),n&4){if(e.stateNode===null)throw Error(b(162));l=e.stateNode,a=e.memoizedProps;try{l.nodeValue=a}catch(v){he(e,e.return,v)}}break;case 3:if(mt(t,e),jt(e),n&4&&r!==null&&r.memoizedState.isDehydrated)try{il(t.containerInfo)}catch(v){he(e,e.return,v)}break;case 4:mt(t,e),jt(e);break;case 13:mt(t,e),jt(e),l=e.child,l.flags&8192&&(a=l.memoizedState!==null,l.stateNode.isHidden=a,!a||l.alternate!==null&&l.alternate.memoizedState!==null||(Fs=pe())),n&4&&pc(e);break;case 22:if(d=r!==null&&r.memoizedState!==null,e.mode&1?(ze=(c=ze)||d,mt(t,e),ze=c):mt(t,e),jt(e),n&8192){if(c=e.memoizedState!==null,(e.stateNode.isHidden=c)&&!d&&e.mode&1)for(z=e,d=e.child;d!==null;){for(f=z=d;z!==null;){switch(p=z,x=p.child,p.tag){case 0:case 11:case 14:case 15:qn(4,p,p.return);break;case 1:en(p,p.return);var S=p.stateNode;if(typeof S.componentWillUnmount=="function"){n=p,r=p.return;try{t=n,S.props=t.memoizedProps,S.state=t.memoizedState,S.componentWillUnmount()}catch(v){he(n,r,v)}}break;case 5:en(p,p.return);break;case 22:if(p.memoizedState!==null){gc(f);continue}}x!==null?(x.return=p,z=x):gc(f)}d=d.sibling}e:for(d=null,f=e;;){if(f.tag===5){if(d===null){d=f;try{l=f.stateNode,c?(a=l.style,typeof a.setProperty=="function"?a.setProperty("display","none","important"):a.display="none"):(i=f.stateNode,s=f.memoizedProps.style,o=s!=null&&s.hasOwnProperty("display")?s.display:null,i.style.display=Nd("display",o))}catch(v){he(e,e.return,v)}}}else if(f.tag===6){if(d===null)try{f.stateNode.nodeValue=c?"":f.memoizedProps}catch(v){he(e,e.return,v)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;f.sibling===null;){if(f.return===null||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:mt(t,e),jt(e),n&4&&pc(e);break;case 21:break;default:mt(t,e),jt(e)}}function jt(e){var t=e.flags;if(t&2){try{e:{for(var r=e.return;r!==null;){if(Zf(r)){var n=r;break e}r=r.return}throw Error(b(160))}switch(n.tag){case 5:var l=n.stateNode;n.flags&32&&(nl(l,""),n.flags&=-33);var a=hc(e);$i(e,a,l);break;case 3:case 4:var o=n.stateNode.containerInfo,i=hc(e);Ii(e,i,o);break;default:throw Error(b(161))}}catch(s){he(e,e.return,s)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function g0(e,t,r){z=e,th(e)}function th(e,t,r){for(var n=(e.mode&1)!==0;z!==null;){var l=z,a=l.child;if(l.tag===22&&n){var o=l.memoizedState!==null||Gl;if(!o){var i=l.alternate,s=i!==null&&i.memoizedState!==null||ze;i=Gl;var c=ze;if(Gl=o,(ze=s)&&!c)for(z=l;z!==null;)o=z,s=o.child,o.tag===22&&o.memoizedState!==null?yc(l):s!==null?(s.return=o,z=s):yc(l);for(;a!==null;)z=a,th(a),a=a.sibling;z=l,Gl=i,ze=c}mc(e)}else l.subtreeFlags&8772&&a!==null?(a.return=l,z=a):mc(e)}}function mc(e){for(;z!==null;){var t=z;if(t.flags&8772){var r=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:ze||eo(5,t);break;case 1:var n=t.stateNode;if(t.flags&4&&!ze)if(r===null)n.componentDidMount();else{var l=t.elementType===t.type?r.memoizedProps:gt(t.type,r.memoizedProps);n.componentDidUpdate(l,r.memoizedState,n.__reactInternalSnapshotBeforeUpdate)}var a=t.updateQueue;a!==null&&qu(t,a,n);break;case 3:var o=t.updateQueue;if(o!==null){if(r=null,t.child!==null)switch(t.child.tag){case 5:r=t.child.stateNode;break;case 1:r=t.child.stateNode}qu(t,o,r)}break;case 5:var i=t.stateNode;if(r===null&&t.flags&4){r=i;var s=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":s.autoFocus&&r.focus();break;case"img":s.src&&(r.src=s.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var c=t.alternate;if(c!==null){var d=c.memoizedState;if(d!==null){var f=d.dehydrated;f!==null&&il(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(b(163))}ze||t.flags&512&&Ai(t)}catch(p){he(t,t.return,p)}}if(t===e){z=null;break}if(r=t.sibling,r!==null){r.return=t.return,z=r;break}z=t.return}}function gc(e){for(;z!==null;){var t=z;if(t===e){z=null;break}var r=t.sibling;if(r!==null){r.return=t.return,z=r;break}z=t.return}}function yc(e){for(;z!==null;){var t=z;try{switch(t.tag){case 0:case 11:case 15:var r=t.return;try{eo(4,t)}catch(s){he(t,r,s)}break;case 1:var n=t.stateNode;if(typeof n.componentDidMount=="function"){var l=t.return;try{n.componentDidMount()}catch(s){he(t,l,s)}}var a=t.return;try{Ai(t)}catch(s){he(t,a,s)}break;case 5:var o=t.return;try{Ai(t)}catch(s){he(t,o,s)}}}catch(s){he(t,t.return,s)}if(t===e){z=null;break}var i=t.sibling;if(i!==null){i.return=t.return,z=i;break}z=t.return}}var y0=Math.ceil,Da=$t.ReactCurrentDispatcher,Ms=$t.ReactCurrentOwner,dt=$t.ReactCurrentBatchConfig,X=0,Ne=null,xe=null,Le=0,Je=0,tn=hr(0),je=0,vl=null,Mr=0,to=0,zs=0,el=null,He=null,Fs=0,gn=1/0,Rt=null,Oa=!1,Ui=null,or=null,Xl=!1,er=null,Aa=0,tl=0,Bi=null,da=-1,fa=0;function $e(){return X&6?pe():da!==-1?da:da=pe()}function ir(e){return e.mode&1?X&2&&Le!==0?Le&-Le:e0.transition!==null?(fa===0&&(fa=Ad()),fa):(e=te,e!==0||(e=window.event,e=e===void 0?16:Vd(e.type)),e):1}function wt(e,t,r,n){if(50<tl)throw tl=0,Bi=null,Error(b(185));Sl(e,r,n),(!(X&2)||e!==Ne)&&(e===Ne&&(!(X&2)&&(to|=r),je===4&&Jt(e,Le)),Ke(e,n),r===1&&X===0&&!(t.mode&1)&&(gn=pe()+500,Ja&&pr()))}function Ke(e,t){var r=e.callbackNode;em(e,t);var n=ka(e,e===Ne?Le:0);if(n===0)r!==null&&Cu(r),e.callbackNode=null,e.callbackPriority=0;else if(t=n&-n,e.callbackPriority!==t){if(r!=null&&Cu(r),t===1)e.tag===0?qm(vc.bind(null,e)):df(vc.bind(null,e)),Gm(function(){!(X&6)&&pr()}),r=null;else{switch(Id(n)){case 1:r=is;break;case 4:r=Dd;break;case 16:r=wa;break;case 536870912:r=Od;break;default:r=wa}r=uh(r,rh.bind(null,e))}e.callbackPriority=t,e.callbackNode=r}}function rh(e,t){if(da=-1,fa=0,X&6)throw Error(b(327));var r=e.callbackNode;if(sn()&&e.callbackNode!==r)return null;var n=ka(e,e===Ne?Le:0);if(n===0)return null;if(n&30||n&e.expiredLanes||t)t=Ia(e,n);else{t=n;var l=X;X|=2;var a=lh();(Ne!==e||Le!==t)&&(Rt=null,gn=pe()+500,Lr(e,t));do try{w0();break}catch(i){nh(e,i)}while(!0);ws(),Da.current=a,X=l,xe!==null?t=0:(Ne=null,Le=0,t=je)}if(t!==0){if(t===2&&(l=mi(e),l!==0&&(n=l,t=Hi(e,l))),t===1)throw r=vl,Lr(e,0),Jt(e,n),Ke(e,pe()),r;if(t===6)Jt(e,n);else{if(l=e.current.alternate,!(n&30)&&!v0(l)&&(t=Ia(e,n),t===2&&(a=mi(e),a!==0&&(n=a,t=Hi(e,a))),t===1))throw r=vl,Lr(e,0),Jt(e,n),Ke(e,pe()),r;switch(e.finishedWork=l,e.finishedLanes=n,t){case 0:case 1:throw Error(b(345));case 2:Sr(e,He,Rt);break;case 3:if(Jt(e,n),(n&130023424)===n&&(t=Fs+500-pe(),10<t)){if(ka(e,0)!==0)break;if(l=e.suspendedLanes,(l&n)!==n){$e(),e.pingedLanes|=e.suspendedLanes&l;break}e.timeoutHandle=ji(Sr.bind(null,e,He,Rt),t);break}Sr(e,He,Rt);break;case 4:if(Jt(e,n),(n&4194240)===n)break;for(t=e.eventTimes,l=-1;0<n;){var o=31-xt(n);a=1<<o,o=t[o],o>l&&(l=o),n&=~a}if(n=l,n=pe()-n,n=(120>n?120:480>n?480:1080>n?1080:1920>n?1920:3e3>n?3e3:4320>n?4320:1960*y0(n/1960))-n,10<n){e.timeoutHandle=ji(Sr.bind(null,e,He,Rt),n);break}Sr(e,He,Rt);break;case 5:Sr(e,He,Rt);break;default:throw Error(b(329))}}}return Ke(e,pe()),e.callbackNode===r?rh.bind(null,e):null}function Hi(e,t){var r=el;return e.current.memoizedState.isDehydrated&&(Lr(e,t).flags|=256),e=Ia(e,t),e!==2&&(t=He,He=r,t!==null&&Wi(t)),e}function Wi(e){He===null?He=e:He.push.apply(He,e)}function v0(e){for(var t=e;;){if(t.flags&16384){var r=t.updateQueue;if(r!==null&&(r=r.stores,r!==null))for(var n=0;n<r.length;n++){var l=r[n],a=l.getSnapshot;l=l.value;try{if(!kt(a(),l))return!1}catch{return!1}}}if(r=t.child,t.subtreeFlags&16384&&r!==null)r.return=t,t=r;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Jt(e,t){for(t&=~zs,t&=~to,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var r=31-xt(t),n=1<<r;e[r]=-1,t&=~n}}function vc(e){if(X&6)throw Error(b(327));sn();var t=ka(e,0);if(!(t&1))return Ke(e,pe()),null;var r=Ia(e,t);if(e.tag!==0&&r===2){var n=mi(e);n!==0&&(t=n,r=Hi(e,n))}if(r===1)throw r=vl,Lr(e,0),Jt(e,t),Ke(e,pe()),r;if(r===6)throw Error(b(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Sr(e,He,Rt),Ke(e,pe()),null}function Ds(e,t){var r=X;X|=1;try{return e(t)}finally{X=r,X===0&&(gn=pe()+500,Ja&&pr())}}function zr(e){er!==null&&er.tag===0&&!(X&6)&&sn();var t=X;X|=1;var r=dt.transition,n=te;try{if(dt.transition=null,te=1,e)return e()}finally{te=n,dt.transition=r,X=t,!(X&6)&&pr()}}function Os(){Je=tn.current,oe(tn)}function Lr(e,t){e.finishedWork=null,e.finishedLanes=0;var r=e.timeoutHandle;if(r!==-1&&(e.timeoutHandle=-1,Ym(r)),xe!==null)for(r=xe.return;r!==null;){var n=r;switch(ys(n),n.tag){case 1:n=n.type.childContextTypes,n!=null&&Ca();break;case 3:pn(),oe(Ve),oe(Fe),Cs();break;case 5:Ns(n);break;case 4:pn();break;case 13:oe(ue);break;case 19:oe(ue);break;case 10:ks(n.type._context);break;case 22:case 23:Os()}r=r.return}if(Ne=e,xe=e=sr(e.current,null),Le=Je=t,je=0,vl=null,zs=to=Mr=0,He=el=null,Nr!==null){for(t=0;t<Nr.length;t++)if(r=Nr[t],n=r.interleaved,n!==null){r.interleaved=null;var l=n.next,a=r.pending;if(a!==null){var o=a.next;a.next=l,n.next=o}r.pending=n}Nr=null}return e}function nh(e,t){do{var r=xe;try{if(ws(),sa.current=Fa,za){for(var n=ce.memoizedState;n!==null;){var l=n.queue;l!==null&&(l.pending=null),n=n.next}za=!1}if(Tr=0,Ee=Se=ce=null,Zn=!1,ml=0,Ms.current=null,r===null||r.return===null){je=1,vl=t,xe=null;break}e:{var a=e,o=r.return,i=r,s=t;if(t=Le,i.flags|=32768,s!==null&&typeof s=="object"&&typeof s.then=="function"){var c=s,d=i,f=d.tag;if(!(d.mode&1)&&(f===0||f===11||f===15)){var p=d.alternate;p?(d.updateQueue=p.updateQueue,d.memoizedState=p.memoizedState,d.lanes=p.lanes):(d.updateQueue=null,d.memoizedState=null)}var x=ac(o);if(x!==null){x.flags&=-257,oc(x,o,i,a,t),x.mode&1&&lc(a,c,t),t=x,s=c;var S=t.updateQueue;if(S===null){var v=new Set;v.add(s),t.updateQueue=v}else S.add(s);break e}else{if(!(t&1)){lc(a,c,t),As();break e}s=Error(b(426))}}else if(ie&&i.mode&1){var _=ac(o);if(_!==null){!(_.flags&65536)&&(_.flags|=256),oc(_,o,i,a,t),vs(mn(s,i));break e}}a=s=mn(s,i),je!==4&&(je=2),el===null?el=[a]:el.push(a),a=o;do{switch(a.tag){case 3:a.flags|=65536,t&=-t,a.lanes|=t;var h=$f(a,s,t);Zu(a,h);break e;case 1:i=s;var m=a.type,g=a.stateNode;if(!(a.flags&128)&&(typeof m.getDerivedStateFromError=="function"||g!==null&&typeof g.componentDidCatch=="function"&&(or===null||!or.has(g)))){a.flags|=65536,t&=-t,a.lanes|=t;var E=Uf(a,i,t);Zu(a,E);break e}}a=a.return}while(a!==null)}oh(r)}catch(L){t=L,xe===r&&r!==null&&(xe=r=r.return);continue}break}while(!0)}function lh(){var e=Da.current;return Da.current=Fa,e===null?Fa:e}function As(){(je===0||je===3||je===2)&&(je=4),Ne===null||!(Mr&268435455)&&!(to&268435455)||Jt(Ne,Le)}function Ia(e,t){var r=X;X|=2;var n=lh();(Ne!==e||Le!==t)&&(Rt=null,Lr(e,t));do try{x0();break}catch(l){nh(e,l)}while(!0);if(ws(),X=r,Da.current=n,xe!==null)throw Error(b(261));return Ne=null,Le=0,je}function x0(){for(;xe!==null;)ah(xe)}function w0(){for(;xe!==null&&!Vp();)ah(xe)}function ah(e){var t=sh(e.alternate,e,Je);e.memoizedProps=e.pendingProps,t===null?oh(e):xe=t,Ms.current=null}function oh(e){var t=e;do{var r=t.alternate;if(e=t.return,t.flags&32768){if(r=h0(r,t),r!==null){r.flags&=32767,xe=r;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{je=6,xe=null;return}}else if(r=f0(r,t,Je),r!==null){xe=r;return}if(t=t.sibling,t!==null){xe=t;return}xe=t=e}while(t!==null);je===0&&(je=5)}function Sr(e,t,r){var n=te,l=dt.transition;try{dt.transition=null,te=1,k0(e,t,r,n)}finally{dt.transition=l,te=n}return null}function k0(e,t,r,n){do sn();while(er!==null);if(X&6)throw Error(b(327));r=e.finishedWork;var l=e.finishedLanes;if(r===null)return null;if(e.finishedWork=null,e.finishedLanes=0,r===e.current)throw Error(b(177));e.callbackNode=null,e.callbackPriority=0;var a=r.lanes|r.childLanes;if(tm(e,a),e===Ne&&(xe=Ne=null,Le=0),!(r.subtreeFlags&2064)&&!(r.flags&2064)||Xl||(Xl=!0,uh(wa,function(){return sn(),null})),a=(r.flags&15990)!==0,r.subtreeFlags&15990||a){a=dt.transition,dt.transition=null;var o=te;te=1;var i=X;X|=4,Ms.current=null,m0(e,r),eh(r,e),Um(ki),Sa=!!wi,ki=wi=null,e.current=r,g0(r),Qp(),X=i,te=o,dt.transition=a}else e.current=r;if(Xl&&(Xl=!1,er=e,Aa=l),a=e.pendingLanes,a===0&&(or=null),Gp(r.stateNode),Ke(e,pe()),t!==null)for(n=e.onRecoverableError,r=0;r<t.length;r++)l=t[r],n(l.value,{componentStack:l.stack,digest:l.digest});if(Oa)throw Oa=!1,e=Ui,Ui=null,e;return Aa&1&&e.tag!==0&&sn(),a=e.pendingLanes,a&1?e===Bi?tl++:(tl=0,Bi=e):tl=0,pr(),null}function sn(){if(er!==null){var e=Id(Aa),t=dt.transition,r=te;try{if(dt.transition=null,te=16>e?16:e,er===null)var n=!1;else{if(e=er,er=null,Aa=0,X&6)throw Error(b(331));var l=X;for(X|=4,z=e.current;z!==null;){var a=z,o=a.child;if(z.flags&16){var i=a.deletions;if(i!==null){for(var s=0;s<i.length;s++){var c=i[s];for(z=c;z!==null;){var d=z;switch(d.tag){case 0:case 11:case 15:qn(8,d,a)}var f=d.child;if(f!==null)f.return=d,z=f;else for(;z!==null;){d=z;var p=d.sibling,x=d.return;if(Jf(d),d===c){z=null;break}if(p!==null){p.return=x,z=p;break}z=x}}}var S=a.alternate;if(S!==null){var v=S.child;if(v!==null){S.child=null;do{var _=v.sibling;v.sibling=null,v=_}while(v!==null)}}z=a}}if(a.subtreeFlags&2064&&o!==null)o.return=a,z=o;else e:for(;z!==null;){if(a=z,a.flags&2048)switch(a.tag){case 0:case 11:case 15:qn(9,a,a.return)}var h=a.sibling;if(h!==null){h.return=a.return,z=h;break e}z=a.return}}var m=e.current;for(z=m;z!==null;){o=z;var g=o.child;if(o.subtreeFlags&2064&&g!==null)g.return=o,z=g;else e:for(o=m;z!==null;){if(i=z,i.flags&2048)try{switch(i.tag){case 0:case 11:case 15:eo(9,i)}}catch(L){he(i,i.return,L)}if(i===o){z=null;break e}var E=i.sibling;if(E!==null){E.return=i.return,z=E;break e}z=i.return}}if(X=l,pr(),Ct&&typeof Ct.onPostCommitFiberRoot=="function")try{Ct.onPostCommitFiberRoot(Qa,e)}catch{}n=!0}return n}finally{te=r,dt.transition=t}}return!1}function xc(e,t,r){t=mn(r,t),t=$f(e,t,1),e=ar(e,t,1),t=$e(),e!==null&&(Sl(e,1,t),Ke(e,t))}function he(e,t,r){if(e.tag===3)xc(e,e,r);else for(;t!==null;){if(t.tag===3){xc(t,e,r);break}else if(t.tag===1){var n=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof n.componentDidCatch=="function"&&(or===null||!or.has(n))){e=mn(r,e),e=Uf(t,e,1),t=ar(t,e,1),e=$e(),t!==null&&(Sl(t,1,e),Ke(t,e));break}}t=t.return}}function S0(e,t,r){var n=e.pingCache;n!==null&&n.delete(t),t=$e(),e.pingedLanes|=e.suspendedLanes&r,Ne===e&&(Le&r)===r&&(je===4||je===3&&(Le&130023424)===Le&&500>pe()-Fs?Lr(e,0):zs|=r),Ke(e,t)}function ih(e,t){t===0&&(e.mode&1?(t=$l,$l<<=1,!($l&130023424)&&($l=4194304)):t=1);var r=$e();e=At(e,t),e!==null&&(Sl(e,t,r),Ke(e,r))}function j0(e){var t=e.memoizedState,r=0;t!==null&&(r=t.retryLane),ih(e,r)}function E0(e,t){var r=0;switch(e.tag){case 13:var n=e.stateNode,l=e.memoizedState;l!==null&&(r=l.retryLane);break;case 19:n=e.stateNode;break;default:throw Error(b(314))}n!==null&&n.delete(t),ih(e,r)}var sh;sh=function(e,t,r){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ve.current)We=!0;else{if(!(e.lanes&r)&&!(t.flags&128))return We=!1,d0(e,t,r);We=!!(e.flags&131072)}else We=!1,ie&&t.flags&1048576&&ff(t,Pa,t.index);switch(t.lanes=0,t.tag){case 2:var n=t.type;ca(e,t),e=t.pendingProps;var l=dn(t,Fe.current);on(t,r),l=Ls(null,t,n,e,l,r);var a=Ps();return t.flags|=1,typeof l=="object"&&l!==null&&typeof l.render=="function"&&l.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Qe(n)?(a=!0,_a(t)):a=!1,t.memoizedState=l.state!==null&&l.state!==void 0?l.state:null,js(t),l.updater=qa,t.stateNode=l,l._reactInternals=t,bi(t,n,e,r),t=Mi(null,t,n,!0,a,r)):(t.tag=0,ie&&a&&gs(t),Ie(null,t,l,r),t=t.child),t;case 16:n=t.elementType;e:{switch(ca(e,t),e=t.pendingProps,l=n._init,n=l(n._payload),t.type=n,l=t.tag=C0(n),e=gt(n,e),l){case 0:t=Ti(null,t,n,e,r);break e;case 1:t=uc(null,t,n,e,r);break e;case 11:t=ic(null,t,n,e,r);break e;case 14:t=sc(null,t,n,gt(n.type,e),r);break e}throw Error(b(306,n,""))}return t;case 0:return n=t.type,l=t.pendingProps,l=t.elementType===n?l:gt(n,l),Ti(e,t,n,l,r);case 1:return n=t.type,l=t.pendingProps,l=t.elementType===n?l:gt(n,l),uc(e,t,n,l,r);case 3:e:{if(Vf(t),e===null)throw Error(b(387));n=t.pendingProps,a=t.memoizedState,l=a.element,vf(e,t),Ta(t,n,null,r);var o=t.memoizedState;if(n=o.element,a.isDehydrated)if(a={element:n,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=a,t.memoizedState=a,t.flags&256){l=mn(Error(b(423)),t),t=cc(e,t,n,r,l);break e}else if(n!==l){l=mn(Error(b(424)),t),t=cc(e,t,n,r,l);break e}else for(et=lr(t.stateNode.containerInfo.firstChild),tt=t,ie=!0,vt=null,r=gf(t,null,n,r),t.child=r;r;)r.flags=r.flags&-3|4096,r=r.sibling;else{if(fn(),n===l){t=It(e,t,r);break e}Ie(e,t,n,r)}t=t.child}return t;case 5:return xf(t),e===null&&_i(t),n=t.type,l=t.pendingProps,a=e!==null?e.memoizedProps:null,o=l.children,Si(n,l)?o=null:a!==null&&Si(n,a)&&(t.flags|=32),Wf(e,t),Ie(e,t,o,r),t.child;case 6:return e===null&&_i(t),null;case 13:return Qf(e,t,r);case 4:return Es(t,t.stateNode.containerInfo),n=t.pendingProps,e===null?t.child=hn(t,null,n,r):Ie(e,t,n,r),t.child;case 11:return n=t.type,l=t.pendingProps,l=t.elementType===n?l:gt(n,l),ic(e,t,n,l,r);case 7:return Ie(e,t,t.pendingProps,r),t.child;case 8:return Ie(e,t,t.pendingProps.children,r),t.child;case 12:return Ie(e,t,t.pendingProps.children,r),t.child;case 10:e:{if(n=t.type._context,l=t.pendingProps,a=t.memoizedProps,o=l.value,le(ba,n._currentValue),n._currentValue=o,a!==null)if(kt(a.value,o)){if(a.children===l.children&&!Ve.current){t=It(e,t,r);break e}}else for(a=t.child,a!==null&&(a.return=t);a!==null;){var i=a.dependencies;if(i!==null){o=a.child;for(var s=i.firstContext;s!==null;){if(s.context===n){if(a.tag===1){s=Ft(-1,r&-r),s.tag=2;var c=a.updateQueue;if(c!==null){c=c.shared;var d=c.pending;d===null?s.next=s:(s.next=d.next,d.next=s),c.pending=s}}a.lanes|=r,s=a.alternate,s!==null&&(s.lanes|=r),Li(a.return,r,t),i.lanes|=r;break}s=s.next}}else if(a.tag===10)o=a.type===t.type?null:a.child;else if(a.tag===18){if(o=a.return,o===null)throw Error(b(341));o.lanes|=r,i=o.alternate,i!==null&&(i.lanes|=r),Li(o,r,t),o=a.sibling}else o=a.child;if(o!==null)o.return=a;else for(o=a;o!==null;){if(o===t){o=null;break}if(a=o.sibling,a!==null){a.return=o.return,o=a;break}o=o.return}a=o}Ie(e,t,l.children,r),t=t.child}return t;case 9:return l=t.type,n=t.pendingProps.children,on(t,r),l=ft(l),n=n(l),t.flags|=1,Ie(e,t,n,r),t.child;case 14:return n=t.type,l=gt(n,t.pendingProps),l=gt(n.type,l),sc(e,t,n,l,r);case 15:return Bf(e,t,t.type,t.pendingProps,r);case 17:return n=t.type,l=t.pendingProps,l=t.elementType===n?l:gt(n,l),ca(e,t),t.tag=1,Qe(n)?(e=!0,_a(t)):e=!1,on(t,r),If(t,n,l),bi(t,n,l,r),Mi(null,t,n,!0,e,r);case 19:return Kf(e,t,r);case 22:return Hf(e,t,r)}throw Error(b(156,t.tag))};function uh(e,t){return Fd(e,t)}function N0(e,t,r,n){this.tag=e,this.key=r,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=n,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ct(e,t,r,n){return new N0(e,t,r,n)}function Is(e){return e=e.prototype,!(!e||!e.isReactComponent)}function C0(e){if(typeof e=="function")return Is(e)?1:0;if(e!=null){if(e=e.$$typeof,e===ls)return 11;if(e===as)return 14}return 2}function sr(e,t){var r=e.alternate;return r===null?(r=ct(e.tag,t,e.key,e.mode),r.elementType=e.elementType,r.type=e.type,r.stateNode=e.stateNode,r.alternate=e,e.alternate=r):(r.pendingProps=t,r.type=e.type,r.flags=0,r.subtreeFlags=0,r.deletions=null),r.flags=e.flags&14680064,r.childLanes=e.childLanes,r.lanes=e.lanes,r.child=e.child,r.memoizedProps=e.memoizedProps,r.memoizedState=e.memoizedState,r.updateQueue=e.updateQueue,t=e.dependencies,r.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},r.sibling=e.sibling,r.index=e.index,r.ref=e.ref,r}function ha(e,t,r,n,l,a){var o=2;if(n=e,typeof e=="function")Is(e)&&(o=1);else if(typeof e=="string")o=5;else e:switch(e){case Vr:return Pr(r.children,l,a,t);case ns:o=8,l|=8;break;case ei:return e=ct(12,r,t,l|2),e.elementType=ei,e.lanes=a,e;case ti:return e=ct(13,r,t,l),e.elementType=ti,e.lanes=a,e;case ri:return e=ct(19,r,t,l),e.elementType=ri,e.lanes=a,e;case vd:return ro(r,l,a,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case gd:o=10;break e;case yd:o=9;break e;case ls:o=11;break e;case as:o=14;break e;case Yt:o=16,n=null;break e}throw Error(b(130,e==null?e:typeof e,""))}return t=ct(o,r,t,l),t.elementType=e,t.type=n,t.lanes=a,t}function Pr(e,t,r,n){return e=ct(7,e,n,t),e.lanes=r,e}function ro(e,t,r,n){return e=ct(22,e,n,t),e.elementType=vd,e.lanes=r,e.stateNode={isHidden:!1},e}function Wo(e,t,r){return e=ct(6,e,null,t),e.lanes=r,e}function Vo(e,t,r){return t=ct(4,e.children!==null?e.children:[],e.key,t),t.lanes=r,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function _0(e,t,r,n,l){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=No(0),this.expirationTimes=No(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=No(0),this.identifierPrefix=n,this.onRecoverableError=l,this.mutableSourceEagerHydrationData=null}function $s(e,t,r,n,l,a,o,i,s){return e=new _0(e,t,r,i,s),t===1?(t=1,a===!0&&(t|=8)):t=0,a=ct(3,null,null,t),e.current=a,a.stateNode=e,a.memoizedState={element:n,isDehydrated:r,cache:null,transitions:null,pendingSuspenseBoundaries:null},js(a),e}function L0(e,t,r){var n=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Wr,key:n==null?null:""+n,children:e,containerInfo:t,implementation:r}}function ch(e){if(!e)return cr;e=e._reactInternals;e:{if(Dr(e)!==e||e.tag!==1)throw Error(b(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Qe(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(b(171))}if(e.tag===1){var r=e.type;if(Qe(r))return cf(e,r,t)}return t}function dh(e,t,r,n,l,a,o,i,s){return e=$s(r,n,!0,e,l,a,o,i,s),e.context=ch(null),r=e.current,n=$e(),l=ir(r),a=Ft(n,l),a.callback=t??null,ar(r,a,l),e.current.lanes=l,Sl(e,l,n),Ke(e,n),e}function no(e,t,r,n){var l=t.current,a=$e(),o=ir(l);return r=ch(r),t.context===null?t.context=r:t.pendingContext=r,t=Ft(a,o),t.payload={element:e},n=n===void 0?null:n,n!==null&&(t.callback=n),e=ar(l,t,o),e!==null&&(wt(e,l,o,a),ia(e,l,o)),o}function $a(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function wc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var r=e.retryLane;e.retryLane=r!==0&&r<t?r:t}}function Us(e,t){wc(e,t),(e=e.alternate)&&wc(e,t)}function P0(){return null}var fh=typeof reportError=="function"?reportError:function(e){console.error(e)};function Bs(e){this._internalRoot=e}lo.prototype.render=Bs.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(b(409));no(e,t,null,null)};lo.prototype.unmount=Bs.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;zr(function(){no(null,e,null,null)}),t[Ot]=null}};function lo(e){this._internalRoot=e}lo.prototype.unstable_scheduleHydration=function(e){if(e){var t=Bd();e={blockedOn:null,target:e,priority:t};for(var r=0;r<Xt.length&&t!==0&&t<Xt[r].priority;r++);Xt.splice(r,0,e),r===0&&Wd(e)}};function Hs(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function ao(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function kc(){}function b0(e,t,r,n,l){if(l){if(typeof n=="function"){var a=n;n=function(){var c=$a(o);a.call(c)}}var o=dh(t,n,e,0,null,!1,!1,"",kc);return e._reactRootContainer=o,e[Ot]=o.current,cl(e.nodeType===8?e.parentNode:e),zr(),o}for(;l=e.lastChild;)e.removeChild(l);if(typeof n=="function"){var i=n;n=function(){var c=$a(s);i.call(c)}}var s=$s(e,0,!1,null,null,!1,!1,"",kc);return e._reactRootContainer=s,e[Ot]=s.current,cl(e.nodeType===8?e.parentNode:e),zr(function(){no(t,s,r,n)}),s}function oo(e,t,r,n,l){var a=r._reactRootContainer;if(a){var o=a;if(typeof l=="function"){var i=l;l=function(){var s=$a(o);i.call(s)}}no(t,o,e,l)}else o=b0(r,t,e,l,n);return $a(o)}$d=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var r=Bn(t.pendingLanes);r!==0&&(ss(t,r|1),Ke(t,pe()),!(X&6)&&(gn=pe()+500,pr()))}break;case 13:zr(function(){var n=At(e,1);if(n!==null){var l=$e();wt(n,e,1,l)}}),Us(e,1)}};us=function(e){if(e.tag===13){var t=At(e,134217728);if(t!==null){var r=$e();wt(t,e,134217728,r)}Us(e,134217728)}};Ud=function(e){if(e.tag===13){var t=ir(e),r=At(e,t);if(r!==null){var n=$e();wt(r,e,t,n)}Us(e,t)}};Bd=function(){return te};Hd=function(e,t){var r=te;try{return te=e,t()}finally{te=r}};fi=function(e,t,r){switch(t){case"input":if(ai(e,r),t=r.name,r.type==="radio"&&t!=null){for(r=e;r.parentNode;)r=r.parentNode;for(r=r.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<r.length;t++){var n=r[t];if(n!==e&&n.form===e.form){var l=Xa(n);if(!l)throw Error(b(90));wd(n),ai(n,l)}}}break;case"textarea":Sd(e,r);break;case"select":t=r.value,t!=null&&rn(e,!!r.multiple,t,!1)}};Pd=Ds;bd=zr;var R0={usingClientEntryPoint:!1,Events:[El,Gr,Xa,_d,Ld,Ds]},zn={findFiberByHostInstance:Er,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},T0={bundleType:zn.bundleType,version:zn.version,rendererPackageName:zn.rendererPackageName,rendererConfig:zn.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:$t.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Md(e),e===null?null:e.stateNode},findFiberByHostInstance:zn.findFiberByHostInstance||P0,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Jl=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Jl.isDisabled&&Jl.supportsFiber)try{Qa=Jl.inject(T0),Ct=Jl}catch{}}nt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=R0;nt.createPortal=function(e,t){var r=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Hs(t))throw Error(b(200));return L0(e,t,null,r)};nt.createRoot=function(e,t){if(!Hs(e))throw Error(b(299));var r=!1,n="",l=fh;return t!=null&&(t.unstable_strictMode===!0&&(r=!0),t.identifierPrefix!==void 0&&(n=t.identifierPrefix),t.onRecoverableError!==void 0&&(l=t.onRecoverableError)),t=$s(e,1,!1,null,null,r,!1,n,l),e[Ot]=t.current,cl(e.nodeType===8?e.parentNode:e),new Bs(t)};nt.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(b(188)):(e=Object.keys(e).join(","),Error(b(268,e)));return e=Md(t),e=e===null?null:e.stateNode,e};nt.flushSync=function(e){return zr(e)};nt.hydrate=function(e,t,r){if(!ao(t))throw Error(b(200));return oo(null,e,t,!0,r)};nt.hydrateRoot=function(e,t,r){if(!Hs(e))throw Error(b(405));var n=r!=null&&r.hydratedSources||null,l=!1,a="",o=fh;if(r!=null&&(r.unstable_strictMode===!0&&(l=!0),r.identifierPrefix!==void 0&&(a=r.identifierPrefix),r.onRecoverableError!==void 0&&(o=r.onRecoverableError)),t=dh(t,null,e,1,r??null,l,!1,a,o),e[Ot]=t.current,cl(e),n)for(e=0;e<n.length;e++)r=n[e],l=r._getVersion,l=l(r._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[r,l]:t.mutableSourceEagerHydrationData.push(r,l);return new lo(t)};nt.render=function(e,t,r){if(!ao(t))throw Error(b(200));return oo(null,e,t,!1,r)};nt.unmountComponentAtNode=function(e){if(!ao(e))throw Error(b(40));return e._reactRootContainer?(zr(function(){oo(null,null,e,!1,function(){e._reactRootContainer=null,e[Ot]=null})}),!0):!1};nt.unstable_batchedUpdates=Ds;nt.unstable_renderSubtreeIntoContainer=function(e,t,r,n){if(!ao(r))throw Error(b(200));if(e==null||e._reactInternals===void 0)throw Error(b(38));return oo(e,t,r,!1,n)};nt.version="18.3.1-next-f1338f8080-20240426";function hh(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(hh)}catch(e){console.error(e)}}hh(),fd.exports=nt;var ph=fd.exports,Sc=ph;Zo.createRoot=Sc.createRoot,Zo.hydrateRoot=Sc.hydrateRoot;var Ws={};Object.defineProperty(Ws,"__esModule",{value:!0});Ws.parse=I0;Ws.serialize=$0;const M0=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,z0=/^[\u0021-\u003A\u003C-\u007E]*$/,F0=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,D0=/^[\u0020-\u003A\u003D-\u007E]*$/,O0=Object.prototype.toString,A0=(()=>{const e=function(){};return e.prototype=Object.create(null),e})();function I0(e,t){const r=new A0,n=e.length;if(n<2)return r;const l=(t==null?void 0:t.decode)||U0;let a=0;do{const o=e.indexOf("=",a);if(o===-1)break;const i=e.indexOf(";",a),s=i===-1?n:i;if(o>s){a=e.lastIndexOf(";",o-1)+1;continue}const c=jc(e,a,o),d=Ec(e,o,c),f=e.slice(c,d);if(r[f]===void 0){let p=jc(e,o+1,s),x=Ec(e,s,p);const S=l(e.slice(p,x));r[f]=S}a=s+1}while(a<n);return r}function jc(e,t,r){do{const n=e.charCodeAt(t);if(n!==32&&n!==9)return t}while(++t<r);return r}function Ec(e,t,r){for(;t>r;){const n=e.charCodeAt(--t);if(n!==32&&n!==9)return t+1}return r}function $0(e,t,r){const n=(r==null?void 0:r.encode)||encodeURIComponent;if(!M0.test(e))throw new TypeError(`argument name is invalid: ${e}`);const l=n(t);if(!z0.test(l))throw new TypeError(`argument val is invalid: ${t}`);let a=e+"="+l;if(!r)return a;if(r.maxAge!==void 0){if(!Number.isInteger(r.maxAge))throw new TypeError(`option maxAge is invalid: ${r.maxAge}`);a+="; Max-Age="+r.maxAge}if(r.domain){if(!F0.test(r.domain))throw new TypeError(`option domain is invalid: ${r.domain}`);a+="; Domain="+r.domain}if(r.path){if(!D0.test(r.path))throw new TypeError(`option path is invalid: ${r.path}`);a+="; Path="+r.path}if(r.expires){if(!B0(r.expires)||!Number.isFinite(r.expires.valueOf()))throw new TypeError(`option expires is invalid: ${r.expires}`);a+="; Expires="+r.expires.toUTCString()}if(r.httpOnly&&(a+="; HttpOnly"),r.secure&&(a+="; Secure"),r.partitioned&&(a+="; Partitioned"),r.priority)switch(typeof r.priority=="string"?r.priority.toLowerCase():void 0){case"low":a+="; Priority=Low";break;case"medium":a+="; Priority=Medium";break;case"high":a+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${r.priority}`)}if(r.sameSite)switch(typeof r.sameSite=="string"?r.sameSite.toLowerCase():r.sameSite){case!0:case"strict":a+="; SameSite=Strict";break;case"lax":a+="; SameSite=Lax";break;case"none":a+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${r.sameSite}`)}return a}function U0(e){if(e.indexOf("%")===-1)return e;try{return decodeURIComponent(e)}catch{return e}}function B0(e){return O0.call(e)==="[object Date]"}/**
 * react-router v7.6.3
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */var mh=e=>{throw TypeError(e)},H0=(e,t,r)=>t.has(e)||mh("Cannot "+r),Qo=(e,t,r)=>(H0(e,t,"read from private field"),r?r.call(e):t.get(e)),W0=(e,t,r)=>t.has(e)?mh("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),Nc="popstate";function V0(e={}){function t(n,l){let{pathname:a,search:o,hash:i}=n.location;return xl("",{pathname:a,search:o,hash:i},l.state&&l.state.usr||null,l.state&&l.state.key||"default")}function r(n,l){return typeof l=="string"?l:dr(l)}return K0(t,r,null,e)}function K(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function me(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function Q0(){return Math.random().toString(36).substring(2,10)}function Cc(e,t){return{usr:e.state,key:e.key,idx:t}}function xl(e,t,r=null,n){return{pathname:typeof e=="string"?e:e.pathname,search:"",hash:"",...typeof t=="string"?mr(t):t,state:r,key:t&&t.key||n||Q0()}}function dr({pathname:e="/",search:t="",hash:r=""}){return t&&t!=="?"&&(e+=t.charAt(0)==="?"?t:"?"+t),r&&r!=="#"&&(e+=r.charAt(0)==="#"?r:"#"+r),e}function mr(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substring(r),e=e.substring(0,r));let n=e.indexOf("?");n>=0&&(t.search=e.substring(n),e=e.substring(0,n)),e&&(t.pathname=e)}return t}function K0(e,t,r,n={}){let{window:l=document.defaultView,v5Compat:a=!1}=n,o=l.history,i="POP",s=null,c=d();c==null&&(c=0,o.replaceState({...o.state,idx:c},""));function d(){return(o.state||{idx:null}).idx}function f(){i="POP";let _=d(),h=_==null?null:_-c;c=_,s&&s({action:i,location:v.location,delta:h})}function p(_,h){i="PUSH";let m=xl(v.location,_,h);c=d()+1;let g=Cc(m,c),E=v.createHref(m);try{o.pushState(g,"",E)}catch(L){if(L instanceof DOMException&&L.name==="DataCloneError")throw L;l.location.assign(E)}a&&s&&s({action:i,location:v.location,delta:1})}function x(_,h){i="REPLACE";let m=xl(v.location,_,h);c=d();let g=Cc(m,c),E=v.createHref(m);o.replaceState(g,"",E),a&&s&&s({action:i,location:v.location,delta:0})}function S(_){return gh(_)}let v={get action(){return i},get location(){return e(l,o)},listen(_){if(s)throw new Error("A history only accepts one active listener");return l.addEventListener(Nc,f),s=_,()=>{l.removeEventListener(Nc,f),s=null}},createHref(_){return t(l,_)},createURL:S,encodeLocation(_){let h=S(_);return{pathname:h.pathname,search:h.search,hash:h.hash}},push:p,replace:x,go(_){return o.go(_)}};return v}function gh(e,t=!1){let r="http://localhost";typeof window<"u"&&(r=window.location.origin!=="null"?window.location.origin:window.location.href),K(r,"No window.location.(origin|href) available to create URL");let n=typeof e=="string"?e:dr(e);return n=n.replace(/ $/,"%20"),!t&&n.startsWith("//")&&(n=r+n),new URL(n,r)}var Wn,_c=class{constructor(e){if(W0(this,Wn,new Map),e)for(let[t,r]of e)this.set(t,r)}get(e){if(Qo(this,Wn).has(e))return Qo(this,Wn).get(e);if(e.defaultValue!==void 0)return e.defaultValue;throw new Error("No value found for context")}set(e,t){Qo(this,Wn).set(e,t)}};Wn=new WeakMap;var Y0=new Set(["lazy","caseSensitive","path","id","index","children"]);function G0(e){return Y0.has(e)}var X0=new Set(["lazy","caseSensitive","path","id","index","unstable_middleware","children"]);function J0(e){return X0.has(e)}function Z0(e){return e.index===!0}function Ua(e,t,r=[],n={}){return e.map((l,a)=>{let o=[...r,String(a)],i=typeof l.id=="string"?l.id:o.join("-");if(K(l.index!==!0||!l.children,"Cannot specify children on an index route"),K(!n[i],`Found a route id collision on id "${i}".  Route id's must be globally unique within Data Router usages`),Z0(l)){let s={...l,...t(l),id:i};return n[i]=s,s}else{let s={...l,...t(l),id:i,children:void 0};return n[i]=s,l.children&&(s.children=Ua(l.children,t,o,n)),s}})}function Zt(e,t,r="/"){return pa(e,t,r,!1)}function pa(e,t,r,n){let l=typeof t=="string"?mr(t):t,a=pt(l.pathname||"/",r);if(a==null)return null;let o=yh(e);eg(o);let i=null;for(let s=0;i==null&&s<o.length;++s){let c=dg(a);i=ug(o[s],c,n)}return i}function q0(e,t){let{route:r,pathname:n,params:l}=e;return{id:r.id,pathname:n,params:l,data:t[r.id],handle:r.handle}}function yh(e,t=[],r=[],n=""){let l=(a,o,i)=>{let s={relativePath:i===void 0?a.path||"":i,caseSensitive:a.caseSensitive===!0,childrenIndex:o,route:a};s.relativePath.startsWith("/")&&(K(s.relativePath.startsWith(n),`Absolute route path "${s.relativePath}" nested under path "${n}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),s.relativePath=s.relativePath.slice(n.length));let c=Lt([n,s.relativePath]),d=r.concat(s);a.children&&a.children.length>0&&(K(a.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${c}".`),yh(a.children,t,d,c)),!(a.path==null&&!a.index)&&t.push({path:c,score:ig(c,a.index),routesMeta:d})};return e.forEach((a,o)=>{var i;if(a.path===""||!((i=a.path)!=null&&i.includes("?")))l(a,o);else for(let s of vh(a.path))l(a,o,s)}),t}function vh(e){let t=e.split("/");if(t.length===0)return[];let[r,...n]=t,l=r.endsWith("?"),a=r.replace(/\?$/,"");if(n.length===0)return l?[a,""]:[a];let o=vh(n.join("/")),i=[];return i.push(...o.map(s=>s===""?a:[a,s].join("/"))),l&&i.push(...o),i.map(s=>e.startsWith("/")&&s===""?"/":s)}function eg(e){e.sort((t,r)=>t.score!==r.score?r.score-t.score:sg(t.routesMeta.map(n=>n.childrenIndex),r.routesMeta.map(n=>n.childrenIndex)))}var tg=/^:[\w-]+$/,rg=3,ng=2,lg=1,ag=10,og=-2,Lc=e=>e==="*";function ig(e,t){let r=e.split("/"),n=r.length;return r.some(Lc)&&(n+=og),t&&(n+=ng),r.filter(l=>!Lc(l)).reduce((l,a)=>l+(tg.test(a)?rg:a===""?lg:ag),n)}function sg(e,t){return e.length===t.length&&e.slice(0,-1).every((n,l)=>n===t[l])?e[e.length-1]-t[t.length-1]:0}function ug(e,t,r=!1){let{routesMeta:n}=e,l={},a="/",o=[];for(let i=0;i<n.length;++i){let s=n[i],c=i===n.length-1,d=a==="/"?t:t.slice(a.length)||"/",f=Ba({path:s.relativePath,caseSensitive:s.caseSensitive,end:c},d),p=s.route;if(!f&&c&&r&&!n[n.length-1].route.index&&(f=Ba({path:s.relativePath,caseSensitive:s.caseSensitive,end:!1},d)),!f)return null;Object.assign(l,f.params),o.push({params:l,pathname:Lt([a,f.pathname]),pathnameBase:pg(Lt([a,f.pathnameBase])),route:p}),f.pathnameBase!=="/"&&(a=Lt([a,f.pathnameBase]))}return o}function Ba(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[r,n]=cg(e.path,e.caseSensitive,e.end),l=t.match(r);if(!l)return null;let a=l[0],o=a.replace(/(.)\/+$/,"$1"),i=l.slice(1);return{params:n.reduce((c,{paramName:d,isOptional:f},p)=>{if(d==="*"){let S=i[p]||"";o=a.slice(0,a.length-S.length).replace(/(.)\/+$/,"$1")}const x=i[p];return f&&!x?c[d]=void 0:c[d]=(x||"").replace(/%2F/g,"/"),c},{}),pathname:a,pathnameBase:o,pattern:e}}function cg(e,t=!1,r=!0){me(e==="*"||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let n=[],l="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(o,i,s)=>(n.push({paramName:i,isOptional:s!=null}),s?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(n.push({paramName:"*"}),l+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?l+="\\/*$":e!==""&&e!=="/"&&(l+="(?:(?=\\/|$))"),[new RegExp(l,t?void 0:"i"),n]}function dg(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return me(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function pt(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,n=e.charAt(r);return n&&n!=="/"?null:e.slice(r)||"/"}function fg(e,t="/"){let{pathname:r,search:n="",hash:l=""}=typeof e=="string"?mr(e):e;return{pathname:r?r.startsWith("/")?r:hg(r,t):t,search:mg(n),hash:gg(l)}}function hg(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(l=>{l===".."?r.length>1&&r.pop():l!=="."&&r.push(l)}),r.length>1?r.join("/"):"/"}function Ko(e,t,r,n){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(n)}].  Please separate it out to the \`to.${r}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function xh(e){return e.filter((t,r)=>r===0||t.route.path&&t.route.path.length>0)}function io(e){let t=xh(e);return t.map((r,n)=>n===t.length-1?r.pathname:r.pathnameBase)}function so(e,t,r,n=!1){let l;typeof e=="string"?l=mr(e):(l={...e},K(!l.pathname||!l.pathname.includes("?"),Ko("?","pathname","search",l)),K(!l.pathname||!l.pathname.includes("#"),Ko("#","pathname","hash",l)),K(!l.search||!l.search.includes("#"),Ko("#","search","hash",l)));let a=e===""||l.pathname==="",o=a?"/":l.pathname,i;if(o==null)i=r;else{let f=t.length-1;if(!n&&o.startsWith("..")){let p=o.split("/");for(;p[0]==="..";)p.shift(),f-=1;l.pathname=p.join("/")}i=f>=0?t[f]:"/"}let s=fg(l,i),c=o&&o!=="/"&&o.endsWith("/"),d=(a||o===".")&&r.endsWith("/");return!s.pathname.endsWith("/")&&(c||d)&&(s.pathname+="/"),s}var Lt=e=>e.join("/").replace(/\/\/+/g,"/"),pg=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),mg=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,gg=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e,Ha=class{constructor(e,t,r,n=!1){this.status=e,this.statusText=t||"",this.internal=n,r instanceof Error?(this.data=r.toString(),this.error=r):this.data=r}};function wl(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}var wh=["POST","PUT","PATCH","DELETE"],yg=new Set(wh),vg=["GET",...wh],xg=new Set(vg),wg=new Set([301,302,303,307,308]),kg=new Set([307,308]),Yo={state:"idle",location:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},Sg={state:"idle",data:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},Fn={state:"unblocked",proceed:void 0,reset:void 0,location:void 0},Vs=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,jg=e=>({hasErrorBoundary:!!e.hasErrorBoundary}),kh="remix-router-transitions",Sh=Symbol("ResetLoaderData");function Eg(e){const t=e.window?e.window:typeof window<"u"?window:void 0,r=typeof t<"u"&&typeof t.document<"u"&&typeof t.document.createElement<"u";K(e.routes.length>0,"You must provide a non-empty routes array to createRouter");let n=e.hydrationRouteProperties||[],l=e.mapRouteProperties||jg,a={},o=Ua(e.routes,l,void 0,a),i,s=e.basename||"/",c=e.dataStrategy||Pg,d={unstable_middleware:!1,...e.future},f=null,p=new Set,x=null,S=null,v=null,_=e.hydrationData!=null,h=Zt(o,e.history.location,s),m=!1,g=null,E;if(h==null&&!e.patchRoutesOnNavigation){let y=it(404,{pathname:e.history.location.pathname}),{matches:k,route:N}=$c(o);E=!0,h=k,g={[N.id]:y}}else if(h&&!e.hydrationData&&Rl(h,o,e.history.location.pathname).active&&(h=null),h)if(h.some(y=>y.route.lazy))E=!1;else if(!h.some(y=>y.route.loader))E=!0;else{let y=e.hydrationData?e.hydrationData.loaderData:null,k=e.hydrationData?e.hydrationData.errors:null;if(k){let N=h.findIndex(P=>k[P.route.id]!==void 0);E=h.slice(0,N+1).every(P=>!Qi(P.route,y,k))}else E=h.every(N=>!Qi(N.route,y,k))}else{E=!1,h=[];let y=Rl(null,o,e.history.location.pathname);y.active&&y.matches&&(m=!0,h=y.matches)}let L,w={historyAction:e.history.action,location:e.history.location,matches:h,initialized:E,navigation:Yo,restoreScrollPosition:e.hydrationData!=null?!1:null,preventScrollReset:!1,revalidation:"idle",loaderData:e.hydrationData&&e.hydrationData.loaderData||{},actionData:e.hydrationData&&e.hydrationData.actionData||null,errors:e.hydrationData&&e.hydrationData.errors||g,fetchers:new Map,blockers:new Map},C="POP",R=!1,O,B=!1,se=new Map,Ye=null,Ge=!1,Z=!1,fe=new Set,q=new Map,we=0,T=-1,D=new Map,I=new Set,G=new Map,ee=new Map,ge=new Set,De=new Map,Bt,Oe=null;function Ar(){if(f=e.history.listen(({action:y,location:k,delta:N})=>{if(Bt){Bt(),Bt=void 0;return}me(De.size===0||N!=null,"You are trying to use a blocker on a POP navigation to a location that was not created by @remix-run/router. This will fail silently in production. This can happen if you are navigating outside the router via `window.history.pushState`/`window.location.hash` instead of using router navigation APIs.  This can also happen if you are using createHashRouter and the user manually changes the URL.");let P=uu({currentLocation:w.location,nextLocation:k,historyAction:y});if(P&&N!=null){let M=new Promise(A=>{Bt=A});e.history.go(N*-1),bl(P,{state:"blocked",location:k,proceed(){bl(P,{state:"proceeding",proceed:void 0,reset:void 0,location:k}),M.then(()=>e.history.go(N))},reset(){let A=new Map(w.blockers);A.set(P,Fn),Ae({blockers:A})}});return}return gr(y,k)}),r){$g(t,se);let y=()=>Ug(t,se);t.addEventListener("pagehide",y),Ye=()=>t.removeEventListener("pagehide",y)}return w.initialized||gr("POP",w.location,{initialHydration:!0}),L}function Hh(){f&&f(),Ye&&Ye(),p.clear(),O&&O.abort(),w.fetchers.forEach((y,k)=>go(k)),w.blockers.forEach((y,k)=>su(k))}function Wh(y){return p.add(y),()=>p.delete(y)}function Ae(y,k={}){w={...w,...y};let N=[],P=[];w.fetchers.forEach((M,A)=>{M.state==="idle"&&(ge.has(A)?N.push(A):P.push(A))}),ge.forEach(M=>{!w.fetchers.has(M)&&!q.has(M)&&N.push(M)}),[...p].forEach(M=>M(w,{deletedFetchers:N,viewTransitionOpts:k.viewTransitionOpts,flushSync:k.flushSync===!0})),N.forEach(M=>go(M)),P.forEach(M=>w.fetchers.delete(M))}function Ir(y,k,{flushSync:N}={}){var H,V;let P=w.actionData!=null&&w.navigation.formMethod!=null&&qe(w.navigation.formMethod)&&w.navigation.state==="loading"&&((H=y.state)==null?void 0:H._isRedirect)!==!0,M;k.actionData?Object.keys(k.actionData).length>0?M=k.actionData:M=null:P?M=w.actionData:M=null;let A=k.loaderData?Ac(w.loaderData,k.loaderData,k.matches||[],k.errors):w.loaderData,W=w.blockers;W.size>0&&(W=new Map(W),W.forEach((U,Q)=>W.set(Q,Fn)));let F=R===!0||w.navigation.formMethod!=null&&qe(w.navigation.formMethod)&&((V=y.state)==null?void 0:V._isRedirect)!==!0;i&&(o=i,i=void 0),Ge||C==="POP"||(C==="PUSH"?e.history.push(y,y.state):C==="REPLACE"&&e.history.replace(y,y.state));let $;if(C==="POP"){let U=se.get(w.location.pathname);U&&U.has(y.pathname)?$={currentLocation:w.location,nextLocation:y}:se.has(y.pathname)&&($={currentLocation:y,nextLocation:w.location})}else if(B){let U=se.get(w.location.pathname);U?U.add(y.pathname):(U=new Set([y.pathname]),se.set(w.location.pathname,U)),$={currentLocation:w.location,nextLocation:y}}Ae({...k,actionData:M,loaderData:A,historyAction:C,location:y,initialized:!0,navigation:Yo,revalidation:"idle",restoreScrollPosition:du(y,k.matches||w.matches),preventScrollReset:F,blockers:W},{viewTransitionOpts:$,flushSync:N===!0}),C="POP",R=!1,B=!1,Ge=!1,Z=!1,Oe==null||Oe.resolve(),Oe=null}async function tu(y,k){if(typeof y=="number"){e.history.go(y);return}let N=Vi(w.location,w.matches,s,y,k==null?void 0:k.fromRouteId,k==null?void 0:k.relative),{path:P,submission:M,error:A}=Pc(!1,N,k),W=w.location,F=xl(w.location,P,k&&k.state);F={...F,...e.history.encodeLocation(F)};let $=k&&k.replace!=null?k.replace:void 0,H="PUSH";$===!0?H="REPLACE":$===!1||M!=null&&qe(M.formMethod)&&M.formAction===w.location.pathname+w.location.search&&(H="REPLACE");let V=k&&"preventScrollReset"in k?k.preventScrollReset===!0:void 0,U=(k&&k.flushSync)===!0,Q=uu({currentLocation:W,nextLocation:F,historyAction:H});if(Q){bl(Q,{state:"blocked",location:F,proceed(){bl(Q,{state:"proceeding",proceed:void 0,reset:void 0,location:F}),tu(y,k)},reset(){let ne=new Map(w.blockers);ne.set(Q,Fn),Ae({blockers:ne})}});return}await gr(H,F,{submission:M,pendingError:A,preventScrollReset:V,replace:k&&k.replace,enableViewTransition:k&&k.viewTransition,flushSync:U})}function Vh(){Oe||(Oe=Bg()),mo(),Ae({revalidation:"loading"});let y=Oe.promise;return w.navigation.state==="submitting"?y:w.navigation.state==="idle"?(gr(w.historyAction,w.location,{startUninterruptedRevalidation:!0}),y):(gr(C||w.historyAction,w.navigation.location,{overrideNavigation:w.navigation,enableViewTransition:B===!0}),y)}async function gr(y,k,N){O&&O.abort(),O=null,C=y,Ge=(N&&N.startUninterruptedRevalidation)===!0,tp(w.location,w.matches),R=(N&&N.preventScrollReset)===!0,B=(N&&N.enableViewTransition)===!0;let P=i||o,M=N&&N.overrideNavigation,A=N!=null&&N.initialHydration&&w.matches&&w.matches.length>0&&!m?w.matches:Zt(P,k,s),W=(N&&N.flushSync)===!0;if(A&&w.initialized&&!Z&&Fg(w.location,k)&&!(N&&N.submission&&qe(N.submission.formMethod))){Ir(k,{matches:A},{flushSync:W});return}let F=Rl(A,P,k.pathname);if(F.active&&F.matches&&(A=F.matches),!A){let{error:Re,notFoundMatches:Xe,route:J}=yo(k.pathname);Ir(k,{matches:Xe,loaderData:{},errors:{[J.id]:Re}},{flushSync:W});return}O=new AbortController;let $=Hr(e.history,k,O.signal,N&&N.submission),H=new _c(e.unstable_getContext?await e.unstable_getContext():void 0),V;if(N&&N.pendingError)V=[jr(A).route.id,{type:"error",error:N.pendingError}];else if(N&&N.submission&&qe(N.submission.formMethod)){let Re=await Qh($,k,N.submission,A,H,F.active,N&&N.initialHydration===!0,{replace:N.replace,flushSync:W});if(Re.shortCircuited)return;if(Re.pendingActionResult){let[Xe,J]=Re.pendingActionResult;if(Ze(J)&&wl(J.error)&&J.error.status===404){O=null,Ir(k,{matches:Re.matches,loaderData:{},errors:{[Xe]:J.error}});return}}A=Re.matches||A,V=Re.pendingActionResult,M=Go(k,N.submission),W=!1,F.active=!1,$=Hr(e.history,$.url,$.signal)}let{shortCircuited:U,matches:Q,loaderData:ne,errors:ke}=await Kh($,k,A,H,F.active,M,N&&N.submission,N&&N.fetcherSubmission,N&&N.replace,N&&N.initialHydration===!0,W,V);U||(O=null,Ir(k,{matches:Q||A,...Ic(V),loaderData:ne,errors:ke}))}async function Qh(y,k,N,P,M,A,W,F={}){mo();let $=Ag(k,N);if(Ae({navigation:$},{flushSync:F.flushSync===!0}),A){let U=await Tl(P,k.pathname,y.signal);if(U.type==="aborted")return{shortCircuited:!0};if(U.type==="error"){let Q=jr(U.partialMatches).route.id;return{matches:U.partialMatches,pendingActionResult:[Q,{type:"error",error:U.error}]}}else if(U.matches)P=U.matches;else{let{notFoundMatches:Q,error:ne,route:ke}=yo(k.pathname);return{matches:Q,pendingActionResult:[ke.id,{type:"error",error:ne}]}}}let H,V=Vn(P,k);if(!V.route.action&&!V.route.lazy)H={type:"error",error:it(405,{method:y.method,pathname:k.pathname,routeId:V.route.id})};else{let U=un(l,a,y,P,V,W?[]:n,M),Q=await Sn(y,U,M,null);if(H=Q[V.route.id],!H){for(let ne of P)if(Q[ne.route.id]){H=Q[ne.route.id];break}}if(y.signal.aborted)return{shortCircuited:!0}}if(_r(H)){let U;return F&&F.replace!=null?U=F.replace:U=Fc(H.response.headers.get("Location"),new URL(y.url),s)===w.location.pathname+w.location.search,await yr(y,H,!0,{submission:N,replace:U}),{shortCircuited:!0}}if(Ze(H)){let U=jr(P,V.route.id);return(F&&F.replace)!==!0&&(C="PUSH"),{matches:P,pendingActionResult:[U.route.id,H,V.route.id]}}return{matches:P,pendingActionResult:[V.route.id,H]}}async function Kh(y,k,N,P,M,A,W,F,$,H,V,U){let Q=A||Go(k,W),ne=W||F||Bc(Q),ke=!Ge&&!H;if(M){if(ke){let ot=ru(U);Ae({navigation:Q,...ot!==void 0?{actionData:ot}:{}},{flushSync:V})}let re=await Tl(N,k.pathname,y.signal);if(re.type==="aborted")return{shortCircuited:!0};if(re.type==="error"){let ot=jr(re.partialMatches).route.id;return{matches:re.partialMatches,loaderData:{},errors:{[ot]:re.error}}}else if(re.matches)N=re.matches;else{let{error:ot,notFoundMatches:Vt,route:zl}=yo(k.pathname);return{matches:Vt,loaderData:{},errors:{[zl.id]:ot}}}}let Re=i||o,{dsMatches:Xe,revalidatingFetchers:J}=bc(y,P,l,a,e.history,w,N,ne,k,H?[]:n,H===!0,Z,fe,ge,G,I,Re,s,e.patchRoutesOnNavigation!=null,U);if(T=++we,!e.dataStrategy&&!Xe.some(re=>re.shouldLoad)&&J.length===0){let re=ou();return Ir(k,{matches:N,loaderData:{},errors:U&&Ze(U[1])?{[U[0]]:U[1].error}:null,...Ic(U),...re?{fetchers:new Map(w.fetchers)}:{}},{flushSync:V}),{shortCircuited:!0}}if(ke){let re={};if(!M){re.navigation=Q;let ot=ru(U);ot!==void 0&&(re.actionData=ot)}J.length>0&&(re.fetchers=Yh(J)),Ae(re,{flushSync:V})}J.forEach(re=>{Wt(re.key),re.controller&&q.set(re.key,re.controller)});let jn=()=>J.forEach(re=>Wt(re.key));O&&O.signal.addEventListener("abort",jn);let{loaderResults:vr,fetcherResults:En}=await nu(Xe,J,y,P);if(y.signal.aborted)return{shortCircuited:!0};O&&O.signal.removeEventListener("abort",jn),J.forEach(re=>q.delete(re.key));let at=Zl(vr);if(at)return await yr(y,at.result,!0,{replace:$}),{shortCircuited:!0};if(at=Zl(En),at)return I.add(at.key),await yr(y,at.result,!0,{replace:$}),{shortCircuited:!0};let{loaderData:Nn,errors:Cn}=Oc(w,N,vr,U,J,En);H&&w.errors&&(Cn={...w.errors,...Cn});let vo=ou(),xr=iu(T),Ml=vo||xr||J.length>0;return{matches:N,loaderData:Nn,errors:Cn,...Ml?{fetchers:new Map(w.fetchers)}:{}}}function ru(y){if(y&&!Ze(y[1]))return{[y[0]]:y[1].data};if(w.actionData)return Object.keys(w.actionData).length===0?null:w.actionData}function Yh(y){return y.forEach(k=>{let N=w.fetchers.get(k.key),P=Dn(void 0,N?N.data:void 0);w.fetchers.set(k.key,P)}),new Map(w.fetchers)}async function Gh(y,k,N,P){Wt(y);let M=(P&&P.flushSync)===!0,A=i||o,W=Vi(w.location,w.matches,s,N,k,P==null?void 0:P.relative),F=Zt(A,W,s),$=Rl(F,A,W);if($.active&&$.matches&&(F=$.matches),!F){bt(y,k,it(404,{pathname:W}),{flushSync:M});return}let{path:H,submission:V,error:U}=Pc(!0,W,P);if(U){bt(y,k,U,{flushSync:M});return}let Q=Vn(F,H),ne=new _c(e.unstable_getContext?await e.unstable_getContext():void 0),ke=(P&&P.preventScrollReset)===!0;if(V&&qe(V.formMethod)){await Xh(y,k,H,Q,F,ne,$.active,M,ke,V);return}G.set(y,{routeId:k,path:H}),await Jh(y,k,H,Q,F,ne,$.active,M,ke,V)}async function Xh(y,k,N,P,M,A,W,F,$,H){mo(),G.delete(y);function V(ye){if(!ye.route.action&&!ye.route.lazy){let $r=it(405,{method:H.formMethod,pathname:N,routeId:k});return bt(y,k,$r,{flushSync:F}),!0}return!1}if(!W&&V(P))return;let U=w.fetchers.get(y);Ht(y,Ig(H,U),{flushSync:F});let Q=new AbortController,ne=Hr(e.history,N,Q.signal,H);if(W){let ye=await Tl(M,N,ne.signal,y);if(ye.type==="aborted")return;if(ye.type==="error"){bt(y,k,ye.error,{flushSync:F});return}else if(ye.matches){if(M=ye.matches,P=Vn(M,N),V(P))return}else{bt(y,k,it(404,{pathname:N}),{flushSync:F});return}}q.set(y,Q);let ke=we,Re=un(l,a,ne,M,P,n,A),J=(await Sn(ne,Re,A,y))[P.route.id];if(ne.signal.aborted){q.get(y)===Q&&q.delete(y);return}if(ge.has(y)){if(_r(J)||Ze(J)){Ht(y,Kt(void 0));return}}else{if(_r(J))if(q.delete(y),T>ke){Ht(y,Kt(void 0));return}else return I.add(y),Ht(y,Dn(H)),yr(ne,J,!1,{fetcherSubmission:H,preventScrollReset:$});if(Ze(J)){bt(y,k,J.error);return}}let jn=w.navigation.location||w.location,vr=Hr(e.history,jn,Q.signal),En=i||o,at=w.navigation.state!=="idle"?Zt(En,w.navigation.location,s):w.matches;K(at,"Didn't find any matches after fetcher action");let Nn=++we;D.set(y,Nn);let Cn=Dn(H,J.data);w.fetchers.set(y,Cn);let{dsMatches:vo,revalidatingFetchers:xr}=bc(vr,A,l,a,e.history,w,at,H,jn,n,!1,Z,fe,ge,G,I,En,s,e.patchRoutesOnNavigation!=null,[P.route.id,J]);xr.filter(ye=>ye.key!==y).forEach(ye=>{let $r=ye.key,fu=w.fetchers.get($r),lp=Dn(void 0,fu?fu.data:void 0);w.fetchers.set($r,lp),Wt($r),ye.controller&&q.set($r,ye.controller)}),Ae({fetchers:new Map(w.fetchers)});let Ml=()=>xr.forEach(ye=>Wt(ye.key));Q.signal.addEventListener("abort",Ml);let{loaderResults:re,fetcherResults:ot}=await nu(vo,xr,vr,A);if(Q.signal.aborted)return;if(Q.signal.removeEventListener("abort",Ml),D.delete(y),q.delete(y),xr.forEach(ye=>q.delete(ye.key)),w.fetchers.has(y)){let ye=Kt(J.data);w.fetchers.set(y,ye)}let Vt=Zl(re);if(Vt)return yr(vr,Vt.result,!1,{preventScrollReset:$});if(Vt=Zl(ot),Vt)return I.add(Vt.key),yr(vr,Vt.result,!1,{preventScrollReset:$});let{loaderData:zl,errors:xo}=Oc(w,at,re,void 0,xr,ot);iu(Nn),w.navigation.state==="loading"&&Nn>T?(K(C,"Expected pending action"),O&&O.abort(),Ir(w.navigation.location,{matches:at,loaderData:zl,errors:xo,fetchers:new Map(w.fetchers)})):(Ae({errors:xo,loaderData:Ac(w.loaderData,zl,at,xo),fetchers:new Map(w.fetchers)}),Z=!1)}async function Jh(y,k,N,P,M,A,W,F,$,H){let V=w.fetchers.get(y);Ht(y,Dn(H,V?V.data:void 0),{flushSync:F});let U=new AbortController,Q=Hr(e.history,N,U.signal);if(W){let J=await Tl(M,N,Q.signal,y);if(J.type==="aborted")return;if(J.type==="error"){bt(y,k,J.error,{flushSync:F});return}else if(J.matches)M=J.matches,P=Vn(M,N);else{bt(y,k,it(404,{pathname:N}),{flushSync:F});return}}q.set(y,U);let ne=we,ke=un(l,a,Q,M,P,n,A),Xe=(await Sn(Q,ke,A,y))[P.route.id];if(q.get(y)===U&&q.delete(y),!Q.signal.aborted){if(ge.has(y)){Ht(y,Kt(void 0));return}if(_r(Xe))if(T>ne){Ht(y,Kt(void 0));return}else{I.add(y),await yr(Q,Xe,!1,{preventScrollReset:$});return}if(Ze(Xe)){bt(y,k,Xe.error);return}Ht(y,Kt(Xe.data))}}async function yr(y,k,N,{submission:P,fetcherSubmission:M,preventScrollReset:A,replace:W}={}){k.response.headers.has("X-Remix-Revalidate")&&(Z=!0);let F=k.response.headers.get("Location");K(F,"Expected a Location header on the redirect Response"),F=Fc(F,new URL(y.url),s);let $=xl(w.location,F,{_isRedirect:!0});if(r){let ke=!1;if(k.response.headers.has("X-Remix-Reload-Document"))ke=!0;else if(Vs.test(F)){const Re=gh(F,!0);ke=Re.origin!==t.location.origin||pt(Re.pathname,s)==null}if(ke){W?t.location.replace(F):t.location.assign(F);return}}O=null;let H=W===!0||k.response.headers.has("X-Remix-Replace")?"REPLACE":"PUSH",{formMethod:V,formAction:U,formEncType:Q}=w.navigation;!P&&!M&&V&&U&&Q&&(P=Bc(w.navigation));let ne=P||M;if(kg.has(k.response.status)&&ne&&qe(ne.formMethod))await gr(H,$,{submission:{...ne,formAction:F},preventScrollReset:A||R,enableViewTransition:N?B:void 0});else{let ke=Go($,P);await gr(H,$,{overrideNavigation:ke,fetcherSubmission:M,preventScrollReset:A||R,enableViewTransition:N?B:void 0})}}async function Sn(y,k,N,P){let M,A={};try{M=await bg(c,y,k,P,N,!1)}catch(W){return k.filter(F=>F.shouldLoad).forEach(F=>{A[F.route.id]={type:"error",error:W}}),A}if(y.signal.aborted)return A;for(let[W,F]of Object.entries(M))if(Dg(F)){let $=F.result;A[W]={type:"redirect",response:Mg($,y,W,k,s)}}else A[W]=await Tg(F);return A}async function nu(y,k,N,P){let M=Sn(N,y,P,null),A=Promise.all(k.map(async $=>{if($.matches&&$.match&&$.request&&$.controller){let V=(await Sn($.request,$.matches,P,$.key))[$.match.route.id];return{[$.key]:V}}else return Promise.resolve({[$.key]:{type:"error",error:it(404,{pathname:$.path})}})})),W=await M,F=(await A).reduce(($,H)=>Object.assign($,H),{});return{loaderResults:W,fetcherResults:F}}function mo(){Z=!0,G.forEach((y,k)=>{q.has(k)&&fe.add(k),Wt(k)})}function Ht(y,k,N={}){w.fetchers.set(y,k),Ae({fetchers:new Map(w.fetchers)},{flushSync:(N&&N.flushSync)===!0})}function bt(y,k,N,P={}){let M=jr(w.matches,k);go(y),Ae({errors:{[M.route.id]:N},fetchers:new Map(w.fetchers)},{flushSync:(P&&P.flushSync)===!0})}function lu(y){return ee.set(y,(ee.get(y)||0)+1),ge.has(y)&&ge.delete(y),w.fetchers.get(y)||Sg}function go(y){let k=w.fetchers.get(y);q.has(y)&&!(k&&k.state==="loading"&&D.has(y))&&Wt(y),G.delete(y),D.delete(y),I.delete(y),ge.delete(y),fe.delete(y),w.fetchers.delete(y)}function Zh(y){let k=(ee.get(y)||0)-1;k<=0?(ee.delete(y),ge.add(y)):ee.set(y,k),Ae({fetchers:new Map(w.fetchers)})}function Wt(y){let k=q.get(y);k&&(k.abort(),q.delete(y))}function au(y){for(let k of y){let N=lu(k),P=Kt(N.data);w.fetchers.set(k,P)}}function ou(){let y=[],k=!1;for(let N of I){let P=w.fetchers.get(N);K(P,`Expected fetcher: ${N}`),P.state==="loading"&&(I.delete(N),y.push(N),k=!0)}return au(y),k}function iu(y){let k=[];for(let[N,P]of D)if(P<y){let M=w.fetchers.get(N);K(M,`Expected fetcher: ${N}`),M.state==="loading"&&(Wt(N),D.delete(N),k.push(N))}return au(k),k.length>0}function qh(y,k){let N=w.blockers.get(y)||Fn;return De.get(y)!==k&&De.set(y,k),N}function su(y){w.blockers.delete(y),De.delete(y)}function bl(y,k){let N=w.blockers.get(y)||Fn;K(N.state==="unblocked"&&k.state==="blocked"||N.state==="blocked"&&k.state==="blocked"||N.state==="blocked"&&k.state==="proceeding"||N.state==="blocked"&&k.state==="unblocked"||N.state==="proceeding"&&k.state==="unblocked",`Invalid blocker state transition: ${N.state} -> ${k.state}`);let P=new Map(w.blockers);P.set(y,k),Ae({blockers:P})}function uu({currentLocation:y,nextLocation:k,historyAction:N}){if(De.size===0)return;De.size>1&&me(!1,"A router only supports one blocker at a time");let P=Array.from(De.entries()),[M,A]=P[P.length-1],W=w.blockers.get(M);if(!(W&&W.state==="proceeding")&&A({currentLocation:y,nextLocation:k,historyAction:N}))return M}function yo(y){let k=it(404,{pathname:y}),N=i||o,{matches:P,route:M}=$c(N);return{notFoundMatches:P,route:M,error:k}}function ep(y,k,N){if(x=y,v=k,S=N||null,!_&&w.navigation===Yo){_=!0;let P=du(w.location,w.matches);P!=null&&Ae({restoreScrollPosition:P})}return()=>{x=null,v=null,S=null}}function cu(y,k){return S&&S(y,k.map(P=>q0(P,w.loaderData)))||y.key}function tp(y,k){if(x&&v){let N=cu(y,k);x[N]=v()}}function du(y,k){if(x){let N=cu(y,k),P=x[N];if(typeof P=="number")return P}return null}function Rl(y,k,N){if(e.patchRoutesOnNavigation)if(y){if(Object.keys(y[0].params).length>0)return{active:!0,matches:pa(k,N,s,!0)}}else return{active:!0,matches:pa(k,N,s,!0)||[]};return{active:!1,matches:null}}async function Tl(y,k,N,P){if(!e.patchRoutesOnNavigation)return{type:"success",matches:y};let M=y;for(;;){let A=i==null,W=i||o,F=a;try{await e.patchRoutesOnNavigation({signal:N,path:k,matches:M,fetcherKey:P,patch:(V,U)=>{N.aborted||Rc(V,U,W,F,l)}})}catch(V){return{type:"error",error:V,partialMatches:M}}finally{A&&!N.aborted&&(o=[...o])}if(N.aborted)return{type:"aborted"};let $=Zt(W,k,s);if($)return{type:"success",matches:$};let H=pa(W,k,s,!0);if(!H||M.length===H.length&&M.every((V,U)=>V.route.id===H[U].route.id))return{type:"success",matches:null};M=H}}function rp(y){a={},i=Ua(y,l,void 0,a)}function np(y,k){let N=i==null;Rc(y,k,i||o,a,l),N&&(o=[...o],Ae({}))}return L={get basename(){return s},get future(){return d},get state(){return w},get routes(){return o},get window(){return t},initialize:Ar,subscribe:Wh,enableScrollRestoration:ep,navigate:tu,fetch:Gh,revalidate:Vh,createHref:y=>e.history.createHref(y),encodeLocation:y=>e.history.encodeLocation(y),getFetcher:lu,deleteFetcher:Zh,dispose:Hh,getBlocker:qh,deleteBlocker:su,patchRoutes:np,_internalFetchControllers:q,_internalSetRoutes:rp},L}function Ng(e){return e!=null&&("formData"in e&&e.formData!=null||"body"in e&&e.body!==void 0)}function Vi(e,t,r,n,l,a){let o,i;if(l){o=[];for(let c of t)if(o.push(c),c.route.id===l){i=c;break}}else o=t,i=t[t.length-1];let s=so(n||".",io(o),pt(e.pathname,r)||e.pathname,a==="path");if(n==null&&(s.search=e.search,s.hash=e.hash),(n==null||n===""||n===".")&&i){let c=Qs(s.search);if(i.route.index&&!c)s.search=s.search?s.search.replace(/^\?/,"?index&"):"?index";else if(!i.route.index&&c){let d=new URLSearchParams(s.search),f=d.getAll("index");d.delete("index"),f.filter(x=>x).forEach(x=>d.append("index",x));let p=d.toString();s.search=p?`?${p}`:""}}return r!=="/"&&(s.pathname=s.pathname==="/"?r:Lt([r,s.pathname])),dr(s)}function Pc(e,t,r){if(!r||!Ng(r))return{path:t};if(r.formMethod&&!Og(r.formMethod))return{path:t,error:it(405,{method:r.formMethod})};let n=()=>({path:t,error:it(400,{type:"invalid-body"})}),a=(r.formMethod||"get").toUpperCase(),o=Lh(t);if(r.body!==void 0){if(r.formEncType==="text/plain"){if(!qe(a))return n();let f=typeof r.body=="string"?r.body:r.body instanceof FormData||r.body instanceof URLSearchParams?Array.from(r.body.entries()).reduce((p,[x,S])=>`${p}${x}=${S}
`,""):String(r.body);return{path:t,submission:{formMethod:a,formAction:o,formEncType:r.formEncType,formData:void 0,json:void 0,text:f}}}else if(r.formEncType==="application/json"){if(!qe(a))return n();try{let f=typeof r.body=="string"?JSON.parse(r.body):r.body;return{path:t,submission:{formMethod:a,formAction:o,formEncType:r.formEncType,formData:void 0,json:f,text:void 0}}}catch{return n()}}}K(typeof FormData=="function","FormData is not available in this environment");let i,s;if(r.formData)i=Yi(r.formData),s=r.formData;else if(r.body instanceof FormData)i=Yi(r.body),s=r.body;else if(r.body instanceof URLSearchParams)i=r.body,s=Dc(i);else if(r.body==null)i=new URLSearchParams,s=new FormData;else try{i=new URLSearchParams(r.body),s=Dc(i)}catch{return n()}let c={formMethod:a,formAction:o,formEncType:r&&r.formEncType||"application/x-www-form-urlencoded",formData:s,json:void 0,text:void 0};if(qe(c.formMethod))return{path:t,submission:c};let d=mr(t);return e&&d.search&&Qs(d.search)&&i.append("index",""),d.search=`?${i}`,{path:dr(d),submission:c}}function bc(e,t,r,n,l,a,o,i,s,c,d,f,p,x,S,v,_,h,m,g){var Ge;let E=g?Ze(g[1])?g[1].error:g[1].data:void 0,L=l.createURL(a.location),w=l.createURL(s),C;if(d&&a.errors){let Z=Object.keys(a.errors)[0];C=o.findIndex(fe=>fe.route.id===Z)}else if(g&&Ze(g[1])){let Z=g[0];C=o.findIndex(fe=>fe.route.id===Z)-1}let R=g?g[1].statusCode:void 0,O=R&&R>=400,B={currentUrl:L,currentParams:((Ge=a.matches[0])==null?void 0:Ge.params)||{},nextUrl:w,nextParams:o[0].params,...i,actionResult:E,actionStatus:R},se=o.map((Z,fe)=>{let{route:q}=Z,we=null;if(C!=null&&fe>C?we=!1:q.lazy?we=!0:q.loader==null?we=!1:d?we=Qi(q,a.loaderData,a.errors):Cg(a.loaderData,a.matches[fe],Z)&&(we=!0),we!==null)return Ki(r,n,e,Z,c,t,we);let T=O?!1:f||L.pathname+L.search===w.pathname+w.search||L.search!==w.search||_g(a.matches[fe],Z),D={...B,defaultShouldRevalidate:T},I=Wa(Z,D);return Ki(r,n,e,Z,c,t,I,D)}),Ye=[];return S.forEach((Z,fe)=>{if(d||!o.some(ge=>ge.route.id===Z.routeId)||x.has(fe))return;let q=a.fetchers.get(fe),we=q&&q.state!=="idle"&&q.data===void 0,T=Zt(_,Z.path,h);if(!T){if(m&&we)return;Ye.push({key:fe,routeId:Z.routeId,path:Z.path,matches:null,match:null,request:null,controller:null});return}if(v.has(fe))return;let D=Vn(T,Z.path),I=new AbortController,G=Hr(l,Z.path,I.signal),ee=null;if(p.has(fe))p.delete(fe),ee=un(r,n,G,T,D,c,t);else if(we)f&&(ee=un(r,n,G,T,D,c,t));else{let ge={...B,defaultShouldRevalidate:O?!1:f};Wa(D,ge)&&(ee=un(r,n,G,T,D,c,t,ge))}ee&&Ye.push({key:fe,routeId:Z.routeId,path:Z.path,matches:ee,match:D,request:G,controller:I})}),{dsMatches:se,revalidatingFetchers:Ye}}function Qi(e,t,r){if(e.lazy)return!0;if(!e.loader)return!1;let n=t!=null&&e.id in t,l=r!=null&&r[e.id]!==void 0;return!n&&l?!1:typeof e.loader=="function"&&e.loader.hydrate===!0?!0:!n&&!l}function Cg(e,t,r){let n=!t||r.route.id!==t.route.id,l=!e.hasOwnProperty(r.route.id);return n||l}function _g(e,t){let r=e.route.path;return e.pathname!==t.pathname||r!=null&&r.endsWith("*")&&e.params["*"]!==t.params["*"]}function Wa(e,t){if(e.route.shouldRevalidate){let r=e.route.shouldRevalidate(t);if(typeof r=="boolean")return r}return t.defaultShouldRevalidate}function Rc(e,t,r,n,l){let a;if(e){let s=n[e];K(s,`No route found to patch children into: routeId = ${e}`),s.children||(s.children=[]),a=s.children}else a=r;let o=t.filter(s=>!a.some(c=>jh(s,c))),i=Ua(o,l,[e||"_","patch",String((a==null?void 0:a.length)||"0")],n);a.push(...i)}function jh(e,t){return"id"in e&&"id"in t&&e.id===t.id?!0:e.index===t.index&&e.path===t.path&&e.caseSensitive===t.caseSensitive?(!e.children||e.children.length===0)&&(!t.children||t.children.length===0)?!0:e.children.every((r,n)=>{var l;return(l=t.children)==null?void 0:l.some(a=>jh(r,a))}):!1}var Tc=new WeakMap,Eh=({key:e,route:t,manifest:r,mapRouteProperties:n})=>{let l=r[t.id];if(K(l,"No route found in manifest"),!l.lazy||typeof l.lazy!="object")return;let a=l.lazy[e];if(!a)return;let o=Tc.get(l);o||(o={},Tc.set(l,o));let i=o[e];if(i)return i;let s=(async()=>{let c=G0(e),f=l[e]!==void 0&&e!=="hasErrorBoundary";if(c)me(!c,"Route property "+e+" is not a supported lazy route property. This property will be ignored."),o[e]=Promise.resolve();else if(f)me(!1,`Route "${l.id}" has a static property "${e}" defined. The lazy property will be ignored.`);else{let p=await a();p!=null&&(Object.assign(l,{[e]:p}),Object.assign(l,n(l)))}typeof l.lazy=="object"&&(l.lazy[e]=void 0,Object.values(l.lazy).every(p=>p===void 0)&&(l.lazy=void 0))})();return o[e]=s,s},Mc=new WeakMap;function Lg(e,t,r,n,l){let a=r[e.id];if(K(a,"No route found in manifest"),!e.lazy)return{lazyRoutePromise:void 0,lazyHandlerPromise:void 0};if(typeof e.lazy=="function"){let d=Mc.get(a);if(d)return{lazyRoutePromise:d,lazyHandlerPromise:d};let f=(async()=>{K(typeof e.lazy=="function","No lazy route function found");let p=await e.lazy(),x={};for(let S in p){let v=p[S];if(v===void 0)continue;let _=J0(S),m=a[S]!==void 0&&S!=="hasErrorBoundary";_?me(!_,"Route property "+S+" is not a supported property to be returned from a lazy route function. This property will be ignored."):m?me(!m,`Route "${a.id}" has a static property "${S}" defined but its lazy function is also returning a value for this property. The lazy route property "${S}" will be ignored.`):x[S]=v}Object.assign(a,x),Object.assign(a,{...n(a),lazy:void 0})})();return Mc.set(a,f),f.catch(()=>{}),{lazyRoutePromise:f,lazyHandlerPromise:f}}let o=Object.keys(e.lazy),i=[],s;for(let d of o){if(l&&l.includes(d))continue;let f=Eh({key:d,route:e,manifest:r,mapRouteProperties:n});f&&(i.push(f),d===t&&(s=f))}let c=i.length>0?Promise.all(i).then(()=>{}):void 0;return c==null||c.catch(()=>{}),s==null||s.catch(()=>{}),{lazyRoutePromise:c,lazyHandlerPromise:s}}async function zc(e){let t=e.matches.filter(l=>l.shouldLoad),r={};return(await Promise.all(t.map(l=>l.resolve()))).forEach((l,a)=>{r[t[a].route.id]=l}),r}async function Pg(e){return e.matches.some(t=>t.route.unstable_middleware)?Nh(e,!1,()=>zc(e),(t,r)=>({[r]:{type:"error",result:t}})):zc(e)}async function Nh(e,t,r,n){let{matches:l,request:a,params:o,context:i}=e,s={handlerResult:void 0};try{let c=l.flatMap(f=>f.route.unstable_middleware?f.route.unstable_middleware.map(p=>[f.route.id,p]):[]),d=await Ch({request:a,params:o,context:i},c,t,s,r);return t?d:s.handlerResult}catch(c){if(!s.middlewareError)throw c;let d=await n(s.middlewareError.error,s.middlewareError.routeId);return s.handlerResult?Object.assign(s.handlerResult,d):d}}async function Ch(e,t,r,n,l,a=0){let{request:o}=e;if(o.signal.aborted)throw o.signal.reason?o.signal.reason:new Error(`Request aborted without an \`AbortSignal.reason\`: ${o.method} ${o.url}`);let i=t[a];if(!i)return n.handlerResult=await l(),n.handlerResult;let[s,c]=i,d=!1,f,p=async()=>{if(d)throw new Error("You may only call `next()` once per middleware");d=!0,await Ch(e,t,r,n,l,a+1)};try{let x=await c({request:e.request,params:e.params,context:e.context},p);return d?x===void 0?f:x:p()}catch(x){throw n.middlewareError?n.middlewareError.error!==x&&(n.middlewareError={routeId:s,error:x}):n.middlewareError={routeId:s,error:x},x}}function _h(e,t,r,n,l){let a=Eh({key:"unstable_middleware",route:n.route,manifest:t,mapRouteProperties:e}),o=Lg(n.route,qe(r.method)?"action":"loader",t,e,l);return{middleware:a,route:o.lazyRoutePromise,handler:o.lazyHandlerPromise}}function Ki(e,t,r,n,l,a,o,i=null){let s=!1,c=_h(e,t,r,n,l);return{...n,_lazyPromises:c,shouldLoad:o,unstable_shouldRevalidateArgs:i,unstable_shouldCallHandler(d){return s=!0,i?typeof d=="boolean"?Wa(n,{...i,defaultShouldRevalidate:d}):Wa(n,i):o},resolve(d){return s||o||d&&r.method==="GET"&&(n.route.lazy||n.route.loader)?Rg({request:r,match:n,lazyHandlerPromise:c==null?void 0:c.handler,lazyRoutePromise:c==null?void 0:c.route,handlerOverride:d,scopedContext:a}):Promise.resolve({type:"data",result:void 0})}}}function un(e,t,r,n,l,a,o,i=null){return n.map(s=>s.route.id!==l.route.id?{...s,shouldLoad:!1,unstable_shouldRevalidateArgs:i,unstable_shouldCallHandler:()=>!1,_lazyPromises:_h(e,t,r,s,a),resolve:()=>Promise.resolve({type:"data",result:void 0})}:Ki(e,t,r,s,a,o,!0,i))}async function bg(e,t,r,n,l,a){r.some(c=>{var d;return(d=c._lazyPromises)==null?void 0:d.middleware})&&await Promise.all(r.map(c=>{var d;return(d=c._lazyPromises)==null?void 0:d.middleware}));let o={request:t,params:r[0].params,context:l,matches:r},s=await e({...o,fetcherKey:n,unstable_runClientMiddleware:c=>{let d=o;return Nh(d,!1,()=>c({...d,fetcherKey:n,unstable_runClientMiddleware:()=>{throw new Error("Cannot call `unstable_runClientMiddleware()` from within an `unstable_runClientMiddleware` handler")}}),(f,p)=>({[p]:{type:"error",result:f}}))}});try{await Promise.all(r.flatMap(c=>{var d,f;return[(d=c._lazyPromises)==null?void 0:d.handler,(f=c._lazyPromises)==null?void 0:f.route]}))}catch{}return s}async function Rg({request:e,match:t,lazyHandlerPromise:r,lazyRoutePromise:n,handlerOverride:l,scopedContext:a}){let o,i,s=qe(e.method),c=s?"action":"loader",d=f=>{let p,x=new Promise((_,h)=>p=h);i=()=>p(),e.signal.addEventListener("abort",i);let S=_=>typeof f!="function"?Promise.reject(new Error(`You cannot call the handler for a route which defines a boolean "${c}" [routeId: ${t.route.id}]`)):f({request:e,params:t.params,context:a},..._!==void 0?[_]:[]),v=(async()=>{try{return{type:"data",result:await(l?l(h=>S(h)):S())}}catch(_){return{type:"error",result:_}}})();return Promise.race([v,x])};try{let f=s?t.route.action:t.route.loader;if(r||n)if(f){let p,[x]=await Promise.all([d(f).catch(S=>{p=S}),r,n]);if(p!==void 0)throw p;o=x}else{await r;let p=s?t.route.action:t.route.loader;if(p)[o]=await Promise.all([d(p),n]);else if(c==="action"){let x=new URL(e.url),S=x.pathname+x.search;throw it(405,{method:e.method,pathname:S,routeId:t.route.id})}else return{type:"data",result:void 0}}else if(f)o=await d(f);else{let p=new URL(e.url),x=p.pathname+p.search;throw it(404,{pathname:x})}}catch(f){return{type:"error",result:f}}finally{i&&e.signal.removeEventListener("abort",i)}return o}async function Tg(e){var n,l,a,o,i,s;let{result:t,type:r}=e;if(Ph(t)){let c;try{let d=t.headers.get("Content-Type");d&&/\bapplication\/json\b/.test(d)?t.body==null?c=null:c=await t.json():c=await t.text()}catch(d){return{type:"error",error:d}}return r==="error"?{type:"error",error:new Ha(t.status,t.statusText,c),statusCode:t.status,headers:t.headers}:{type:"data",data:c,statusCode:t.status,headers:t.headers}}return r==="error"?Uc(t)?t.data instanceof Error?{type:"error",error:t.data,statusCode:(n=t.init)==null?void 0:n.status,headers:(l=t.init)!=null&&l.headers?new Headers(t.init.headers):void 0}:{type:"error",error:new Ha(((a=t.init)==null?void 0:a.status)||500,void 0,t.data),statusCode:wl(t)?t.status:void 0,headers:(o=t.init)!=null&&o.headers?new Headers(t.init.headers):void 0}:{type:"error",error:t,statusCode:wl(t)?t.status:void 0}:Uc(t)?{type:"data",data:t.data,statusCode:(i=t.init)==null?void 0:i.status,headers:(s=t.init)!=null&&s.headers?new Headers(t.init.headers):void 0}:{type:"data",data:t}}function Mg(e,t,r,n,l){let a=e.headers.get("Location");if(K(a,"Redirects returned/thrown from loaders/actions must have a Location header"),!Vs.test(a)){let o=n.slice(0,n.findIndex(i=>i.route.id===r)+1);a=Vi(new URL(t.url),o,l,a),e.headers.set("Location",a)}return e}function Fc(e,t,r){if(Vs.test(e)){let n=e,l=n.startsWith("//")?new URL(t.protocol+n):new URL(n),a=pt(l.pathname,r)!=null;if(l.origin===t.origin&&a)return l.pathname+l.search+l.hash}return e}function Hr(e,t,r,n){let l=e.createURL(Lh(t)).toString(),a={signal:r};if(n&&qe(n.formMethod)){let{formMethod:o,formEncType:i}=n;a.method=o.toUpperCase(),i==="application/json"?(a.headers=new Headers({"Content-Type":i}),a.body=JSON.stringify(n.json)):i==="text/plain"?a.body=n.text:i==="application/x-www-form-urlencoded"&&n.formData?a.body=Yi(n.formData):a.body=n.formData}return new Request(l,a)}function Yi(e){let t=new URLSearchParams;for(let[r,n]of e.entries())t.append(r,typeof n=="string"?n:n.name);return t}function Dc(e){let t=new FormData;for(let[r,n]of e.entries())t.append(r,n);return t}function zg(e,t,r,n=!1,l=!1){let a={},o=null,i,s=!1,c={},d=r&&Ze(r[1])?r[1].error:void 0;return e.forEach(f=>{if(!(f.route.id in t))return;let p=f.route.id,x=t[p];if(K(!_r(x),"Cannot handle redirect results in processLoaderData"),Ze(x)){let S=x.error;if(d!==void 0&&(S=d,d=void 0),o=o||{},l)o[p]=S;else{let v=jr(e,p);o[v.route.id]==null&&(o[v.route.id]=S)}n||(a[p]=Sh),s||(s=!0,i=wl(x.error)?x.error.status:500),x.headers&&(c[p]=x.headers)}else a[p]=x.data,x.statusCode&&x.statusCode!==200&&!s&&(i=x.statusCode),x.headers&&(c[p]=x.headers)}),d!==void 0&&r&&(o={[r[0]]:d},r[2]&&(a[r[2]]=void 0)),{loaderData:a,errors:o,statusCode:i||200,loaderHeaders:c}}function Oc(e,t,r,n,l,a){let{loaderData:o,errors:i}=zg(t,r,n);return l.filter(s=>!s.matches||s.matches.some(c=>c.shouldLoad)).forEach(s=>{let{key:c,match:d,controller:f}=s,p=a[c];if(K(p,"Did not find corresponding fetcher result"),!(f&&f.signal.aborted))if(Ze(p)){let x=jr(e.matches,d==null?void 0:d.route.id);i&&i[x.route.id]||(i={...i,[x.route.id]:p.error}),e.fetchers.delete(c)}else if(_r(p))K(!1,"Unhandled fetcher revalidation redirect");else{let x=Kt(p.data);e.fetchers.set(c,x)}}),{loaderData:o,errors:i}}function Ac(e,t,r,n){let l=Object.entries(t).filter(([,a])=>a!==Sh).reduce((a,[o,i])=>(a[o]=i,a),{});for(let a of r){let o=a.route.id;if(!t.hasOwnProperty(o)&&e.hasOwnProperty(o)&&a.route.loader&&(l[o]=e[o]),n&&n.hasOwnProperty(o))break}return l}function Ic(e){return e?Ze(e[1])?{actionData:{}}:{actionData:{[e[0]]:e[1].data}}:{}}function jr(e,t){return(t?e.slice(0,e.findIndex(n=>n.route.id===t)+1):[...e]).reverse().find(n=>n.route.hasErrorBoundary===!0)||e[0]}function $c(e){let t=e.length===1?e[0]:e.find(r=>r.index||!r.path||r.path==="/")||{id:"__shim-error-route__"};return{matches:[{params:{},pathname:"",pathnameBase:"",route:t}],route:t}}function it(e,{pathname:t,routeId:r,method:n,type:l,message:a}={}){let o="Unknown Server Error",i="Unknown @remix-run/router error";return e===400?(o="Bad Request",n&&t&&r?i=`You made a ${n} request to "${t}" but did not provide a \`loader\` for route "${r}", so there is no way to handle the request.`:l==="invalid-body"&&(i="Unable to encode submission body")):e===403?(o="Forbidden",i=`Route "${r}" does not match URL "${t}"`):e===404?(o="Not Found",i=`No route matches URL "${t}"`):e===405&&(o="Method Not Allowed",n&&t&&r?i=`You made a ${n.toUpperCase()} request to "${t}" but did not provide an \`action\` for route "${r}", so there is no way to handle the request.`:n&&(i=`Invalid request method "${n.toUpperCase()}"`)),new Ha(e||500,o,new Error(i),!0)}function Zl(e){let t=Object.entries(e);for(let r=t.length-1;r>=0;r--){let[n,l]=t[r];if(_r(l))return{key:n,result:l}}}function Lh(e){let t=typeof e=="string"?mr(e):e;return dr({...t,hash:""})}function Fg(e,t){return e.pathname!==t.pathname||e.search!==t.search?!1:e.hash===""?t.hash!=="":e.hash===t.hash?!0:t.hash!==""}function Dg(e){return Ph(e.result)&&wg.has(e.result.status)}function Ze(e){return e.type==="error"}function _r(e){return(e&&e.type)==="redirect"}function Uc(e){return typeof e=="object"&&e!=null&&"type"in e&&"data"in e&&"init"in e&&e.type==="DataWithResponseInit"}function Ph(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.headers=="object"&&typeof e.body<"u"}function Og(e){return xg.has(e.toUpperCase())}function qe(e){return yg.has(e.toUpperCase())}function Qs(e){return new URLSearchParams(e).getAll("index").some(t=>t==="")}function Vn(e,t){let r=typeof t=="string"?mr(t).search:t.search;if(e[e.length-1].route.index&&Qs(r||""))return e[e.length-1];let n=xh(e);return n[n.length-1]}function Bc(e){let{formMethod:t,formAction:r,formEncType:n,text:l,formData:a,json:o}=e;if(!(!t||!r||!n)){if(l!=null)return{formMethod:t,formAction:r,formEncType:n,formData:void 0,json:void 0,text:l};if(a!=null)return{formMethod:t,formAction:r,formEncType:n,formData:a,json:void 0,text:void 0};if(o!==void 0)return{formMethod:t,formAction:r,formEncType:n,formData:void 0,json:o,text:void 0}}}function Go(e,t){return t?{state:"loading",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}:{state:"loading",location:e,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0}}function Ag(e,t){return{state:"submitting",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}}function Dn(e,t){return e?{state:"loading",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t}:{state:"loading",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:t}}function Ig(e,t){return{state:"submitting",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t?t.data:void 0}}function Kt(e){return{state:"idle",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:e}}function $g(e,t){try{let r=e.sessionStorage.getItem(kh);if(r){let n=JSON.parse(r);for(let[l,a]of Object.entries(n||{}))a&&Array.isArray(a)&&t.set(l,new Set(a||[]))}}catch{}}function Ug(e,t){if(t.size>0){let r={};for(let[n,l]of t)r[n]=[...l];try{e.sessionStorage.setItem(kh,JSON.stringify(r))}catch(n){me(!1,`Failed to save applied view transitions in sessionStorage (${n}).`)}}}function Bg(){let e,t,r=new Promise((n,l)=>{e=async a=>{n(a);try{await r}catch{}},t=async a=>{l(a);try{await r}catch{}}});return{promise:r,resolve:e,reject:t}}var Or=j.createContext(null);Or.displayName="DataRouter";var Cl=j.createContext(null);Cl.displayName="DataRouterState";var Ks=j.createContext({isTransitioning:!1});Ks.displayName="ViewTransition";var bh=j.createContext(new Map);bh.displayName="Fetchers";var Hg=j.createContext(null);Hg.displayName="Await";var St=j.createContext(null);St.displayName="Navigation";var uo=j.createContext(null);uo.displayName="Location";var Pt=j.createContext({outlet:null,matches:[],isDataRoute:!1});Pt.displayName="Route";var Ys=j.createContext(null);Ys.displayName="RouteError";function Wg(e,{relative:t}={}){K(kn(),"useHref() may be used only in the context of a <Router> component.");let{basename:r,navigator:n}=j.useContext(St),{hash:l,pathname:a,search:o}=_l(e,{relative:t}),i=a;return r!=="/"&&(i=a==="/"?r:Lt([r,a])),n.createHref({pathname:i,search:o,hash:l})}function kn(){return j.useContext(uo)!=null}function Ut(){return K(kn(),"useLocation() may be used only in the context of a <Router> component."),j.useContext(uo).location}var Rh="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function Th(e){j.useContext(St).static||j.useLayoutEffect(e)}function co(){let{isDataRoute:e}=j.useContext(Pt);return e?ny():Vg()}function Vg(){K(kn(),"useNavigate() may be used only in the context of a <Router> component.");let e=j.useContext(Or),{basename:t,navigator:r}=j.useContext(St),{matches:n}=j.useContext(Pt),{pathname:l}=Ut(),a=JSON.stringify(io(n)),o=j.useRef(!1);return Th(()=>{o.current=!0}),j.useCallback((s,c={})=>{if(me(o.current,Rh),!o.current)return;if(typeof s=="number"){r.go(s);return}let d=so(s,JSON.parse(a),l,c.relative==="path");e==null&&t!=="/"&&(d.pathname=d.pathname==="/"?t:Lt([t,d.pathname])),(c.replace?r.replace:r.push)(d,c.state,c)},[t,r,a,l,e])}j.createContext(null);function _l(e,{relative:t}={}){let{matches:r}=j.useContext(Pt),{pathname:n}=Ut(),l=JSON.stringify(io(r));return j.useMemo(()=>so(e,JSON.parse(l),n,t==="path"),[e,l,n,t])}function Qg(e,t,r,n){K(kn(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:l}=j.useContext(St),{matches:a}=j.useContext(Pt),o=a[a.length-1],i=o?o.params:{},s=o?o.pathname:"/",c=o?o.pathnameBase:"/",d=o&&o.route;{let h=d&&d.path||"";Mh(s,!d||h.endsWith("*")||h.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${s}" (under <Route path="${h}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${h}"> to <Route path="${h==="/"?"*":`${h}/*`}">.`)}let f=Ut(),p;p=f;let x=p.pathname||"/",S=x;if(c!=="/"){let h=c.replace(/^\//,"").split("/");S="/"+x.replace(/^\//,"").split("/").slice(h.length).join("/")}let v=Zt(e,{pathname:S});return me(d||v!=null,`No routes matched location "${p.pathname}${p.search}${p.hash}" `),me(v==null||v[v.length-1].route.element!==void 0||v[v.length-1].route.Component!==void 0||v[v.length-1].route.lazy!==void 0,`Matched leaf route at location "${p.pathname}${p.search}${p.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`),Jg(v&&v.map(h=>Object.assign({},h,{params:Object.assign({},i,h.params),pathname:Lt([c,l.encodeLocation?l.encodeLocation(h.pathname).pathname:h.pathname]),pathnameBase:h.pathnameBase==="/"?c:Lt([c,l.encodeLocation?l.encodeLocation(h.pathnameBase).pathname:h.pathnameBase])})),a,r,n)}function Kg(){let e=ry(),t=wl(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,n="rgba(200,200,200, 0.5)",l={padding:"0.5rem",backgroundColor:n},a={padding:"2px 4px",backgroundColor:n},o=null;return console.error("Error handled by React Router default ErrorBoundary:",e),o=j.createElement(j.Fragment,null,j.createElement("p",null,"💿 Hey developer 👋"),j.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",j.createElement("code",{style:a},"ErrorBoundary")," or"," ",j.createElement("code",{style:a},"errorElement")," prop on your route.")),j.createElement(j.Fragment,null,j.createElement("h2",null,"Unexpected Application Error!"),j.createElement("h3",{style:{fontStyle:"italic"}},t),r?j.createElement("pre",{style:l},r):null,o)}var Yg=j.createElement(Kg,null),Gg=class extends j.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||t.revalidation!=="idle"&&e.revalidation==="idle"?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error!==void 0?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return this.state.error!==void 0?j.createElement(Pt.Provider,{value:this.props.routeContext},j.createElement(Ys.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function Xg({routeContext:e,match:t,children:r}){let n=j.useContext(Or);return n&&n.static&&n.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(n.staticContext._deepestRenderedBoundaryId=t.route.id),j.createElement(Pt.Provider,{value:e},r)}function Jg(e,t=[],r=null,n=null){if(e==null){if(!r)return null;if(r.errors)e=r.matches;else if(t.length===0&&!r.initialized&&r.matches.length>0)e=r.matches;else return null}let l=e,a=r==null?void 0:r.errors;if(a!=null){let s=l.findIndex(c=>c.route.id&&(a==null?void 0:a[c.route.id])!==void 0);K(s>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(a).join(",")}`),l=l.slice(0,Math.min(l.length,s+1))}let o=!1,i=-1;if(r)for(let s=0;s<l.length;s++){let c=l[s];if((c.route.HydrateFallback||c.route.hydrateFallbackElement)&&(i=s),c.route.id){let{loaderData:d,errors:f}=r,p=c.route.loader&&!d.hasOwnProperty(c.route.id)&&(!f||f[c.route.id]===void 0);if(c.route.lazy||p){o=!0,i>=0?l=l.slice(0,i+1):l=[l[0]];break}}}return l.reduceRight((s,c,d)=>{let f,p=!1,x=null,S=null;r&&(f=a&&c.route.id?a[c.route.id]:void 0,x=c.route.errorElement||Yg,o&&(i<0&&d===0?(Mh("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),p=!0,S=null):i===d&&(p=!0,S=c.route.hydrateFallbackElement||null)));let v=t.concat(l.slice(0,d+1)),_=()=>{let h;return f?h=x:p?h=S:c.route.Component?h=j.createElement(c.route.Component,null):c.route.element?h=c.route.element:h=s,j.createElement(Xg,{match:c,routeContext:{outlet:s,matches:v,isDataRoute:r!=null},children:h})};return r&&(c.route.ErrorBoundary||c.route.errorElement||d===0)?j.createElement(Gg,{location:r.location,revalidation:r.revalidation,component:x,error:f,children:_(),routeContext:{outlet:null,matches:v,isDataRoute:!0}}):_()},null)}function Gs(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Zg(e){let t=j.useContext(Or);return K(t,Gs(e)),t}function qg(e){let t=j.useContext(Cl);return K(t,Gs(e)),t}function ey(e){let t=j.useContext(Pt);return K(t,Gs(e)),t}function Xs(e){let t=ey(e),r=t.matches[t.matches.length-1];return K(r.route.id,`${e} can only be used on routes that contain a unique "id"`),r.route.id}function ty(){return Xs("useRouteId")}function ry(){var n;let e=j.useContext(Ys),t=qg("useRouteError"),r=Xs("useRouteError");return e!==void 0?e:(n=t.errors)==null?void 0:n[r]}function ny(){let{router:e}=Zg("useNavigate"),t=Xs("useNavigate"),r=j.useRef(!1);return Th(()=>{r.current=!0}),j.useCallback(async(l,a={})=>{me(r.current,Rh),r.current&&(typeof l=="number"?e.navigate(l):await e.navigate(l,{fromRouteId:t,...a}))},[e,t])}var Hc={};function Mh(e,t,r){!t&&!Hc[e]&&(Hc[e]=!0,me(!1,r))}var Wc={};function Vc(e,t){!e&&!Wc[t]&&(Wc[t]=!0,console.warn(t))}function ly(e){let t={hasErrorBoundary:e.hasErrorBoundary||e.ErrorBoundary!=null||e.errorElement!=null};return e.Component&&(e.element&&me(!1,"You should not include both `Component` and `element` on your route - `Component` will be used."),Object.assign(t,{element:j.createElement(e.Component),Component:void 0})),e.HydrateFallback&&(e.hydrateFallbackElement&&me(!1,"You should not include both `HydrateFallback` and `hydrateFallbackElement` on your route - `HydrateFallback` will be used."),Object.assign(t,{hydrateFallbackElement:j.createElement(e.HydrateFallback),HydrateFallback:void 0})),e.ErrorBoundary&&(e.errorElement&&me(!1,"You should not include both `ErrorBoundary` and `errorElement` on your route - `ErrorBoundary` will be used."),Object.assign(t,{errorElement:j.createElement(e.ErrorBoundary),ErrorBoundary:void 0})),t}var ay=["HydrateFallback","hydrateFallbackElement"],oy=class{constructor(){this.status="pending",this.promise=new Promise((e,t)=>{this.resolve=r=>{this.status==="pending"&&(this.status="resolved",e(r))},this.reject=r=>{this.status==="pending"&&(this.status="rejected",t(r))}})}};function iy({router:e,flushSync:t}){let[r,n]=j.useState(e.state),[l,a]=j.useState(),[o,i]=j.useState({isTransitioning:!1}),[s,c]=j.useState(),[d,f]=j.useState(),[p,x]=j.useState(),S=j.useRef(new Map),v=j.useCallback((g,{deletedFetchers:E,flushSync:L,viewTransitionOpts:w})=>{g.fetchers.forEach((R,O)=>{R.data!==void 0&&S.current.set(O,R.data)}),E.forEach(R=>S.current.delete(R)),Vc(L===!1||t!=null,'You provided the `flushSync` option to a router update, but you are not using the `<RouterProvider>` from `react-router/dom` so `ReactDOM.flushSync()` is unavailable.  Please update your app to `import { RouterProvider } from "react-router/dom"` and ensure you have `react-dom` installed as a dependency to use the `flushSync` option.');let C=e.window!=null&&e.window.document!=null&&typeof e.window.document.startViewTransition=="function";if(Vc(w==null||C,"You provided the `viewTransition` option to a router update, but you do not appear to be running in a DOM environment as `window.startViewTransition` is not available."),!w||!C){t&&L?t(()=>n(g)):j.startTransition(()=>n(g));return}if(t&&L){t(()=>{d&&(s&&s.resolve(),d.skipTransition()),i({isTransitioning:!0,flushSync:!0,currentLocation:w.currentLocation,nextLocation:w.nextLocation})});let R=e.window.document.startViewTransition(()=>{t(()=>n(g))});R.finished.finally(()=>{t(()=>{c(void 0),f(void 0),a(void 0),i({isTransitioning:!1})})}),t(()=>f(R));return}d?(s&&s.resolve(),d.skipTransition(),x({state:g,currentLocation:w.currentLocation,nextLocation:w.nextLocation})):(a(g),i({isTransitioning:!0,flushSync:!1,currentLocation:w.currentLocation,nextLocation:w.nextLocation}))},[e.window,t,d,s]);j.useLayoutEffect(()=>e.subscribe(v),[e,v]),j.useEffect(()=>{o.isTransitioning&&!o.flushSync&&c(new oy)},[o]),j.useEffect(()=>{if(s&&l&&e.window){let g=l,E=s.promise,L=e.window.document.startViewTransition(async()=>{j.startTransition(()=>n(g)),await E});L.finished.finally(()=>{c(void 0),f(void 0),a(void 0),i({isTransitioning:!1})}),f(L)}},[l,s,e.window]),j.useEffect(()=>{s&&l&&r.location.key===l.location.key&&s.resolve()},[s,d,r.location,l]),j.useEffect(()=>{!o.isTransitioning&&p&&(a(p.state),i({isTransitioning:!0,flushSync:!1,currentLocation:p.currentLocation,nextLocation:p.nextLocation}),x(void 0))},[o.isTransitioning,p]);let _=j.useMemo(()=>({createHref:e.createHref,encodeLocation:e.encodeLocation,go:g=>e.navigate(g),push:(g,E,L)=>e.navigate(g,{state:E,preventScrollReset:L==null?void 0:L.preventScrollReset}),replace:(g,E,L)=>e.navigate(g,{replace:!0,state:E,preventScrollReset:L==null?void 0:L.preventScrollReset})}),[e]),h=e.basename||"/",m=j.useMemo(()=>({router:e,navigator:_,static:!1,basename:h}),[e,_,h]);return j.createElement(j.Fragment,null,j.createElement(Or.Provider,{value:m},j.createElement(Cl.Provider,{value:r},j.createElement(bh.Provider,{value:S.current},j.createElement(Ks.Provider,{value:o},j.createElement(cy,{basename:h,location:r.location,navigationType:r.historyAction,navigator:_},j.createElement(sy,{routes:e.routes,future:e.future,state:r})))))),null)}var sy=j.memo(uy);function uy({routes:e,future:t,state:r}){return Qg(e,void 0,r,t)}function Qn({to:e,replace:t,state:r,relative:n}){K(kn(),"<Navigate> may be used only in the context of a <Router> component.");let{static:l}=j.useContext(St);me(!l,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:a}=j.useContext(Pt),{pathname:o}=Ut(),i=co(),s=so(e,io(a),o,n==="path"),c=JSON.stringify(s);return j.useEffect(()=>{i(JSON.parse(c),{replace:t,state:r,relative:n})},[i,c,n,t,r]),null}function cy({basename:e="/",children:t=null,location:r,navigationType:n="POP",navigator:l,static:a=!1}){K(!kn(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let o=e.replace(/^\/*/,"/"),i=j.useMemo(()=>({basename:o,navigator:l,static:a,future:{}}),[o,l,a]);typeof r=="string"&&(r=mr(r));let{pathname:s="/",search:c="",hash:d="",state:f=null,key:p="default"}=r,x=j.useMemo(()=>{let S=pt(s,o);return S==null?null:{location:{pathname:S,search:c,hash:d,state:f,key:p},navigationType:n}},[o,s,c,d,f,p,n]);return me(x!=null,`<Router basename="${o}"> is not able to match the URL "${s}${c}${d}" because it does not start with the basename, so the <Router> won't render anything.`),x==null?null:j.createElement(St.Provider,{value:i},j.createElement(uo.Provider,{children:t,value:x}))}var ma="get",ga="application/x-www-form-urlencoded";function fo(e){return e!=null&&typeof e.tagName=="string"}function dy(e){return fo(e)&&e.tagName.toLowerCase()==="button"}function fy(e){return fo(e)&&e.tagName.toLowerCase()==="form"}function hy(e){return fo(e)&&e.tagName.toLowerCase()==="input"}function py(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function my(e,t){return e.button===0&&(!t||t==="_self")&&!py(e)}var ql=null;function gy(){if(ql===null)try{new FormData(document.createElement("form"),0),ql=!1}catch{ql=!0}return ql}var yy=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Xo(e){return e!=null&&!yy.has(e)?(me(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${ga}"`),null):e}function vy(e,t){let r,n,l,a,o;if(fy(e)){let i=e.getAttribute("action");n=i?pt(i,t):null,r=e.getAttribute("method")||ma,l=Xo(e.getAttribute("enctype"))||ga,a=new FormData(e)}else if(dy(e)||hy(e)&&(e.type==="submit"||e.type==="image")){let i=e.form;if(i==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let s=e.getAttribute("formaction")||i.getAttribute("action");if(n=s?pt(s,t):null,r=e.getAttribute("formmethod")||i.getAttribute("method")||ma,l=Xo(e.getAttribute("formenctype"))||Xo(i.getAttribute("enctype"))||ga,a=new FormData(i,e),!gy()){let{name:c,type:d,value:f}=e;if(d==="image"){let p=c?`${c}.`:"";a.append(`${p}x`,"0"),a.append(`${p}y`,"0")}else c&&a.append(c,f)}}else{if(fo(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');r=ma,n=null,l=ga,o=e}return a&&l==="text/plain"&&(o=a,a=void 0),{action:n,method:r.toLowerCase(),encType:l,formData:a,body:o}}function Js(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}async function xy(e,t){if(e.id in t)return t[e.id];try{let r=await import(e.module);return t[e.id]=r,r}catch(r){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(r),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function wy(e){return e==null?!1:e.href==null?e.rel==="preload"&&typeof e.imageSrcSet=="string"&&typeof e.imageSizes=="string":typeof e.rel=="string"&&typeof e.href=="string"}async function ky(e,t,r){let n=await Promise.all(e.map(async l=>{let a=t.routes[l.route.id];if(a){let o=await xy(a,r);return o.links?o.links():[]}return[]}));return Ny(n.flat(1).filter(wy).filter(l=>l.rel==="stylesheet"||l.rel==="preload").map(l=>l.rel==="stylesheet"?{...l,rel:"prefetch",as:"style"}:{...l,rel:"prefetch"}))}function Qc(e,t,r,n,l,a){let o=(s,c)=>r[c]?s.route.id!==r[c].route.id:!0,i=(s,c)=>{var d;return r[c].pathname!==s.pathname||((d=r[c].route.path)==null?void 0:d.endsWith("*"))&&r[c].params["*"]!==s.params["*"]};return a==="assets"?t.filter((s,c)=>o(s,c)||i(s,c)):a==="data"?t.filter((s,c)=>{var f;let d=n.routes[s.route.id];if(!d||!d.hasLoader)return!1;if(o(s,c)||i(s,c))return!0;if(s.route.shouldRevalidate){let p=s.route.shouldRevalidate({currentUrl:new URL(l.pathname+l.search+l.hash,window.origin),currentParams:((f=r[0])==null?void 0:f.params)||{},nextUrl:new URL(e,window.origin),nextParams:s.params,defaultShouldRevalidate:!0});if(typeof p=="boolean")return p}return!0}):[]}function Sy(e,t,{includeHydrateFallback:r}={}){return jy(e.map(n=>{let l=t.routes[n.route.id];if(!l)return[];let a=[l.module];return l.clientActionModule&&(a=a.concat(l.clientActionModule)),l.clientLoaderModule&&(a=a.concat(l.clientLoaderModule)),r&&l.hydrateFallbackModule&&(a=a.concat(l.hydrateFallbackModule)),l.imports&&(a=a.concat(l.imports)),a}).flat(1))}function jy(e){return[...new Set(e)]}function Ey(e){let t={},r=Object.keys(e).sort();for(let n of r)t[n]=e[n];return t}function Ny(e,t){let r=new Set;return new Set(t),e.reduce((n,l)=>{let a=JSON.stringify(Ey(l));return r.has(a)||(r.add(a),n.push({key:a,link:l})),n},[])}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var Cy=new Set([100,101,204,205]);function _y(e,t){let r=typeof e=="string"?new URL(e,typeof window>"u"?"server://singlefetch/":window.location.origin):e;return r.pathname==="/"?r.pathname="_root.data":t&&pt(r.pathname,t)==="/"?r.pathname=`${t.replace(/\/$/,"")}/_root.data`:r.pathname=`${r.pathname.replace(/\/$/,"")}.data`,r}function zh(){let e=j.useContext(Or);return Js(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function Ly(){let e=j.useContext(Cl);return Js(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var Zs=j.createContext(void 0);Zs.displayName="FrameworkContext";function Fh(){let e=j.useContext(Zs);return Js(e,"You must render this element inside a <HydratedRouter> element"),e}function Py(e,t){let r=j.useContext(Zs),[n,l]=j.useState(!1),[a,o]=j.useState(!1),{onFocus:i,onBlur:s,onMouseEnter:c,onMouseLeave:d,onTouchStart:f}=t,p=j.useRef(null);j.useEffect(()=>{if(e==="render"&&o(!0),e==="viewport"){let v=h=>{h.forEach(m=>{o(m.isIntersecting)})},_=new IntersectionObserver(v,{threshold:.5});return p.current&&_.observe(p.current),()=>{_.disconnect()}}},[e]),j.useEffect(()=>{if(n){let v=setTimeout(()=>{o(!0)},100);return()=>{clearTimeout(v)}}},[n]);let x=()=>{l(!0)},S=()=>{l(!1),o(!1)};return r?e!=="intent"?[a,p,{}]:[a,p,{onFocus:On(i,x),onBlur:On(s,S),onMouseEnter:On(c,x),onMouseLeave:On(d,S),onTouchStart:On(f,x)}]:[!1,p,{}]}function On(e,t){return r=>{e&&e(r),r.defaultPrevented||t(r)}}function by({page:e,...t}){let{router:r}=zh(),n=j.useMemo(()=>Zt(r.routes,e,r.basename),[r.routes,e,r.basename]);return n?j.createElement(Ty,{page:e,matches:n,...t}):null}function Ry(e){let{manifest:t,routeModules:r}=Fh(),[n,l]=j.useState([]);return j.useEffect(()=>{let a=!1;return ky(e,t,r).then(o=>{a||l(o)}),()=>{a=!0}},[e,t,r]),n}function Ty({page:e,matches:t,...r}){let n=Ut(),{manifest:l,routeModules:a}=Fh(),{basename:o}=zh(),{loaderData:i,matches:s}=Ly(),c=j.useMemo(()=>Qc(e,t,s,l,n,"data"),[e,t,s,l,n]),d=j.useMemo(()=>Qc(e,t,s,l,n,"assets"),[e,t,s,l,n]),f=j.useMemo(()=>{if(e===n.pathname+n.search+n.hash)return[];let S=new Set,v=!1;if(t.forEach(h=>{var g;let m=l.routes[h.route.id];!m||!m.hasLoader||(!c.some(E=>E.route.id===h.route.id)&&h.route.id in i&&((g=a[h.route.id])!=null&&g.shouldRevalidate)||m.hasClientLoader?v=!0:S.add(h.route.id))}),S.size===0)return[];let _=_y(e,o);return v&&S.size>0&&_.searchParams.set("_routes",t.filter(h=>S.has(h.route.id)).map(h=>h.route.id).join(",")),[_.pathname+_.search]},[o,i,n,l,c,t,e,a]),p=j.useMemo(()=>Sy(d,l),[d,l]),x=Ry(d);return j.createElement(j.Fragment,null,f.map(S=>j.createElement("link",{key:S,rel:"prefetch",as:"fetch",href:S,...r})),p.map(S=>j.createElement("link",{key:S,rel:"modulepreload",href:S,...r})),x.map(({key:S,link:v})=>j.createElement("link",{key:S,...v})))}function My(...e){return t=>{e.forEach(r=>{typeof r=="function"?r(t):r!=null&&(r.current=t)})}}var Dh=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{Dh&&(window.__reactRouterVersion="7.6.3")}catch{}function zy(e,t){return Eg({basename:t==null?void 0:t.basename,unstable_getContext:t==null?void 0:t.unstable_getContext,future:t==null?void 0:t.future,history:V0({window:t==null?void 0:t.window}),hydrationData:Fy(),routes:e,mapRouteProperties:ly,hydrationRouteProperties:ay,dataStrategy:t==null?void 0:t.dataStrategy,patchRoutesOnNavigation:t==null?void 0:t.patchRoutesOnNavigation,window:t==null?void 0:t.window}).initialize()}function Fy(){let e=window==null?void 0:window.__staticRouterHydrationData;return e&&e.errors&&(e={...e,errors:Dy(e.errors)}),e}function Dy(e){if(!e)return null;let t=Object.entries(e),r={};for(let[n,l]of t)if(l&&l.__type==="RouteErrorResponse")r[n]=new Ha(l.status,l.statusText,l.data,l.internal===!0);else if(l&&l.__type==="Error"){if(l.__subType){let a=window[l.__subType];if(typeof a=="function")try{let o=new a(l.message);o.stack="",r[n]=o}catch{}}if(r[n]==null){let a=new Error(l.message);a.stack="",r[n]=a}}else r[n]=l;return r}var Oh=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,yn=j.forwardRef(function({onClick:t,discover:r="render",prefetch:n="none",relative:l,reloadDocument:a,replace:o,state:i,target:s,to:c,preventScrollReset:d,viewTransition:f,...p},x){let{basename:S}=j.useContext(St),v=typeof c=="string"&&Oh.test(c),_,h=!1;if(typeof c=="string"&&v&&(_=c,Dh))try{let O=new URL(window.location.href),B=c.startsWith("//")?new URL(O.protocol+c):new URL(c),se=pt(B.pathname,S);B.origin===O.origin&&se!=null?c=se+B.search+B.hash:h=!0}catch{me(!1,`<Link to="${c}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let m=Wg(c,{relative:l}),[g,E,L]=Py(n,p),w=$y(c,{replace:o,state:i,target:s,preventScrollReset:d,relative:l,viewTransition:f});function C(O){t&&t(O),O.defaultPrevented||w(O)}let R=j.createElement("a",{...p,...L,href:_||m,onClick:h||a?t:C,ref:My(x,E),target:s,"data-discover":!v&&r==="render"?"true":void 0});return g&&!v?j.createElement(j.Fragment,null,R,j.createElement(by,{page:m})):R});yn.displayName="Link";var Oy=j.forwardRef(function({"aria-current":t="page",caseSensitive:r=!1,className:n="",end:l=!1,style:a,to:o,viewTransition:i,children:s,...c},d){let f=_l(o,{relative:c.relative}),p=Ut(),x=j.useContext(Cl),{navigator:S,basename:v}=j.useContext(St),_=x!=null&&Vy(f)&&i===!0,h=S.encodeLocation?S.encodeLocation(f).pathname:f.pathname,m=p.pathname,g=x&&x.navigation&&x.navigation.location?x.navigation.location.pathname:null;r||(m=m.toLowerCase(),g=g?g.toLowerCase():null,h=h.toLowerCase()),g&&v&&(g=pt(g,v)||g);const E=h!=="/"&&h.endsWith("/")?h.length-1:h.length;let L=m===h||!l&&m.startsWith(h)&&m.charAt(E)==="/",w=g!=null&&(g===h||!l&&g.startsWith(h)&&g.charAt(h.length)==="/"),C={isActive:L,isPending:w,isTransitioning:_},R=L?t:void 0,O;typeof n=="function"?O=n(C):O=[n,L?"active":null,w?"pending":null,_?"transitioning":null].filter(Boolean).join(" ");let B=typeof a=="function"?a(C):a;return j.createElement(yn,{...c,"aria-current":R,className:O,ref:d,style:B,to:o,viewTransition:i},typeof s=="function"?s(C):s)});Oy.displayName="NavLink";var Ay=j.forwardRef(({discover:e="render",fetcherKey:t,navigate:r,reloadDocument:n,replace:l,state:a,method:o=ma,action:i,onSubmit:s,relative:c,preventScrollReset:d,viewTransition:f,...p},x)=>{let S=Hy(),v=Wy(i,{relative:c}),_=o.toLowerCase()==="get"?"get":"post",h=typeof i=="string"&&Oh.test(i),m=g=>{if(s&&s(g),g.defaultPrevented)return;g.preventDefault();let E=g.nativeEvent.submitter,L=(E==null?void 0:E.getAttribute("formmethod"))||o;S(E||g.currentTarget,{fetcherKey:t,method:L,navigate:r,replace:l,state:a,relative:c,preventScrollReset:d,viewTransition:f})};return j.createElement("form",{ref:x,method:_,action:v,onSubmit:n?s:m,...p,"data-discover":!h&&e==="render"?"true":void 0})});Ay.displayName="Form";function Iy(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Ah(e){let t=j.useContext(Or);return K(t,Iy(e)),t}function $y(e,{target:t,replace:r,state:n,preventScrollReset:l,relative:a,viewTransition:o}={}){let i=co(),s=Ut(),c=_l(e,{relative:a});return j.useCallback(d=>{if(my(d,t)){d.preventDefault();let f=r!==void 0?r:dr(s)===dr(c);i(e,{replace:f,state:n,preventScrollReset:l,relative:a,viewTransition:o})}},[s,i,c,r,n,t,e,l,a,o])}var Uy=0,By=()=>`__${String(++Uy)}__`;function Hy(){let{router:e}=Ah("useSubmit"),{basename:t}=j.useContext(St),r=ty();return j.useCallback(async(n,l={})=>{let{action:a,method:o,encType:i,formData:s,body:c}=vy(n,t);if(l.navigate===!1){let d=l.fetcherKey||By();await e.fetch(d,r,l.action||a,{preventScrollReset:l.preventScrollReset,formData:s,body:c,formMethod:l.method||o,formEncType:l.encType||i,flushSync:l.flushSync})}else await e.navigate(l.action||a,{preventScrollReset:l.preventScrollReset,formData:s,body:c,formMethod:l.method||o,formEncType:l.encType||i,replace:l.replace,state:l.state,fromRouteId:r,flushSync:l.flushSync,viewTransition:l.viewTransition})},[e,t,r])}function Wy(e,{relative:t}={}){let{basename:r}=j.useContext(St),n=j.useContext(Pt);K(n,"useFormAction must be used inside a RouteContext");let[l]=n.matches.slice(-1),a={..._l(e||".",{relative:t})},o=Ut();if(e==null){a.search=o.search;let i=new URLSearchParams(a.search),s=i.getAll("index");if(s.some(d=>d==="")){i.delete("index"),s.filter(f=>f).forEach(f=>i.append("index",f));let d=i.toString();a.search=d?`?${d}`:""}}return(!e||e===".")&&l.route.index&&(a.search=a.search?a.search.replace(/^\?/,"?index&"):"?index"),r!=="/"&&(a.pathname=a.pathname==="/"?r:Lt([r,a.pathname])),dr(a)}function Vy(e,t={}){let r=j.useContext(Ks);K(r!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:n}=Ah("useViewTransitionState"),l=_l(e,{relative:t.relative});if(!r.isTransitioning)return!1;let a=pt(r.currentLocation.pathname,n)||r.currentLocation.pathname,o=pt(r.nextLocation.pathname,n)||r.nextLocation.pathname;return Ba(l.pathname,o)!=null||Ba(l.pathname,a)!=null}[...Cy];/**
 * react-router v7.6.3
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Qy(e){return j.createElement(iy,{flushSync:ph.flushSync,...e})}const Kc=e=>{let t;const r=new Set,n=(c,d)=>{const f=typeof c=="function"?c(t):c;if(!Object.is(f,t)){const p=t;t=d??(typeof f!="object"||f===null)?f:Object.assign({},t,f),r.forEach(x=>x(t,p))}},l=()=>t,i={setState:n,getState:l,getInitialState:()=>s,subscribe:c=>(r.add(c),()=>r.delete(c))},s=t=e(n,l,i);return i},Ky=e=>e?Kc(e):Kc,Yy=e=>e;function Gy(e,t=Yy){const r=Jo.useSyncExternalStore(e.subscribe,()=>t(e.getState()),()=>t(e.getInitialState()));return Jo.useDebugValue(r),r}const Yc=e=>{const t=Ky(e),r=n=>Gy(t,n);return Object.assign(r,t),r},Ll=e=>e?Yc(e):Yc;function Ih(e,t){let r;try{r=e()}catch{return}return{getItem:l=>{var a;const o=s=>s===null?null:JSON.parse(s,void 0),i=(a=r.getItem(l))!=null?a:null;return i instanceof Promise?i.then(o):o(i)},setItem:(l,a)=>r.setItem(l,JSON.stringify(a,void 0)),removeItem:l=>r.removeItem(l)}}const Gi=e=>t=>{try{const r=e(t);return r instanceof Promise?r:{then(n){return Gi(n)(r)},catch(n){return this}}}catch(r){return{then(n){return this},catch(n){return Gi(n)(r)}}}},Xy=(e,t)=>(r,n,l)=>{let a={storage:Ih(()=>localStorage),partialize:v=>v,version:0,merge:(v,_)=>({..._,...v}),...t},o=!1;const i=new Set,s=new Set;let c=a.storage;if(!c)return e((...v)=>{console.warn(`[zustand persist middleware] Unable to update item '${a.name}', the given storage is currently unavailable.`),r(...v)},n,l);const d=()=>{const v=a.partialize({...n()});return c.setItem(a.name,{state:v,version:a.version})},f=l.setState;l.setState=(v,_)=>{f(v,_),d()};const p=e((...v)=>{r(...v),d()},n,l);l.getInitialState=()=>p;let x;const S=()=>{var v,_;if(!c)return;o=!1,i.forEach(m=>{var g;return m((g=n())!=null?g:p)});const h=((_=a.onRehydrateStorage)==null?void 0:_.call(a,(v=n())!=null?v:p))||void 0;return Gi(c.getItem.bind(c))(a.name).then(m=>{if(m)if(typeof m.version=="number"&&m.version!==a.version){if(a.migrate){const g=a.migrate(m.state,m.version);return g instanceof Promise?g.then(E=>[!0,E]):[!0,g]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,m.state];return[!1,void 0]}).then(m=>{var g;const[E,L]=m;if(x=a.merge(L,(g=n())!=null?g:p),r(x,!0),E)return d()}).then(()=>{h==null||h(x,void 0),x=n(),o=!0,s.forEach(m=>m(x))}).catch(m=>{h==null||h(void 0,m)})};return l.persist={setOptions:v=>{a={...a,...v},v.storage&&(c=v.storage)},clearStorage:()=>{c==null||c.removeItem(a.name)},getOptions:()=>a,rehydrate:()=>S(),hasHydrated:()=>o,onHydrate:v=>(i.add(v),()=>{i.delete(v)}),onFinishHydration:v=>(s.add(v),()=>{s.delete(v)})},a.skipHydration||S(),x||p},Jy=Xy,ho=Ll()(Jy((e,t)=>({theme:"light",toggleTheme:()=>{const r=t().theme==="light"?"dark":"light";e({theme:r}),r==="dark"?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark")},isSidebarOpen:!1,setSidebarOpen:r=>e({isSidebarOpen:r}),toggleSidebar:()=>e(r=>({isSidebarOpen:!r.isSidebarOpen})),currentTaskPage:"todo",setCurrentTaskPage:r=>e({currentTaskPage:r}),backgroundImage:null,setBackgroundImage:r=>e({backgroundImage:r})}),{name:"ui-store",storage:Ih(()=>localStorage),partialize:e=>({theme:e.theme,currentTaskPage:e.currentTaskPage,backgroundImage:e.backgroundImage})})),Zy=()=>{const{theme:e}=ho.getState();e==="dark"?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark")},qy="http://localhost:8000";class ev{constructor(t){hu(this,"baseURL");this.baseURL=t}async request(t,r={}){const n=`${this.baseURL}${t}`,l={headers:{"Content-Type":"application/json",...r.headers},...r},a=localStorage.getItem("auth_token");a&&(l.headers={...l.headers,Authorization:`Token ${a}`});try{const o=await fetch(n,l),i=await o.json();if(!o.ok)throw new Error(i.message||"API request failed");return{data:i,status:o.status,message:i.message}}catch(o){throw console.error("API request error:",o),o}}async get(t){return this.request(t,{method:"GET"})}async post(t,r){return this.request(t,{method:"POST",body:r?JSON.stringify(r):void 0})}async put(t,r){return this.request(t,{method:"PUT",body:r?JSON.stringify(r):void 0})}async delete(t){return this.request(t,{method:"DELETE"})}}const ve=new ev(qy),Br={async login(e){const t=await ve.post("/auth/login/",e);return t.data.access_token&&localStorage.setItem("auth_token",t.data.access_token),t.data},async register(e){const t=await ve.post("/auth/register/",e);return t.data.access_token&&localStorage.setItem("auth_token",t.data.access_token),t.data},async logout(){try{await ve.post("/auth/logout/")}catch(e){console.error("Logout error:",e)}finally{localStorage.removeItem("auth_token")}},async getCurrentUser(){return(await ve.get("/auth/me/")).data},getToken(){return localStorage.getItem("auth_token")},isAuthenticated(){return!!this.getToken()}},Pl=Ll(e=>({user:null,isAuthenticated:!1,isLoading:!1,login:async(t,r)=>{e({isLoading:!0});try{const n=await Br.login({email:t,password:r});e({user:n.user,isAuthenticated:!0,isLoading:!1})}catch(n){throw e({isLoading:!1}),n}},logout:async()=>{try{await Br.logout()}catch(t){console.error("Logout error:",t)}finally{e({user:null,isAuthenticated:!1})}},register:async(t,r,n,l)=>{e({isLoading:!0});try{const a=await Br.register({username:t,email:r,password:n,password_confirm:l});e({user:a.user,isAuthenticated:!0,isLoading:!1})}catch(a){throw e({isLoading:!1}),a}},checkAuth:async()=>{if(!Br.getToken()){e({isAuthenticated:!1,user:null});return}try{const r=await Br.getCurrentUser();e({user:r,isAuthenticated:!0})}catch(r){console.error("Auth check failed:",r),Br.logout(),e({user:null,isAuthenticated:!1})}}})),Pe=({children:e,onClick:t,variant:r="primary",size:n="md",disabled:l=!1,type:a="button",className:o=""})=>{const i="font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800",s={primary:"bg-purple-600 hover:bg-purple-700 text-white focus:ring-purple-500 disabled:bg-purple-400",secondary:"bg-gray-200 hover:bg-gray-300 text-gray-900 focus:ring-gray-500 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-white dark:focus:ring-gray-400",danger:"bg-red-600 hover:bg-red-700 text-white focus:ring-red-500 disabled:bg-red-400"},c={sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"},d=l?"opacity-50 cursor-not-allowed":"",f=`${i} ${s[r]} ${c[n]} ${d} ${o}`.trim();return u.jsx("button",{type:a,onClick:t,disabled:l,className:f,children:e})},Gc=[{key:"achievements",label:"Achievements",icon:"🏆"},{key:"todo",label:"Todo",icon:"✅"},{key:"plans",label:"Plans",icon:"📋"}],tv=()=>{const{toggleSidebar:e,theme:t,toggleTheme:r,currentTaskPage:n,setCurrentTaskPage:l}=ho(),{logout:a}=Pl(),o=async()=>{try{await a()}catch(i){console.error("Logout failed:",i)}};return u.jsx("nav",{className:"bg-purple-700 dark:bg-purple-800 text-white shadow-lg",children:u.jsx("div",{className:"max-w-full mx-auto px-4",children:u.jsxs("div",{className:"flex items-center justify-between h-16",children:[u.jsxs("div",{className:"flex items-center space-x-4",children:[u.jsx("button",{onClick:e,className:"p-2 rounded-md hover:bg-purple-600 dark:hover:bg-purple-700 transition-colors","aria-label":"Toggle sidebar",children:u.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})}),u.jsx("h1",{className:"text-xl font-semibold",children:"Personal Workspace"})]}),u.jsx("div",{className:"hidden md:flex items-center space-x-1",children:u.jsx("div",{className:"flex bg-purple-600 dark:bg-purple-700 rounded-lg p-1",children:Gc.map(i=>u.jsxs("button",{onClick:()=>l(i.key),className:`px-4 py-2 rounded-md text-sm font-medium transition-colors flex items-center space-x-2 ${n===i.key?"bg-white text-purple-700 shadow-sm":"text-white hover:bg-purple-500 dark:hover:bg-purple-600"}`,children:[u.jsx("span",{children:i.icon}),u.jsx("span",{children:i.label})]},i.key))})}),u.jsx("div",{className:"md:hidden",children:u.jsx("select",{value:n,onChange:i=>l(i.target.value),className:"bg-purple-600 dark:bg-purple-700 text-white border border-purple-500 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-purple-300",children:Gc.map(i=>u.jsxs("option",{value:i.key,children:[i.icon," ",i.label]},i.key))})}),u.jsxs("div",{className:"flex items-center space-x-2",children:[u.jsx("button",{onClick:r,className:"p-2 rounded-md hover:bg-purple-600 dark:hover:bg-purple-700 transition-colors","aria-label":"Toggle theme",title:`Switch to ${t==="light"?"dark":"light"} mode`,children:t==="light"?u.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"})}):u.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"})})}),u.jsx("button",{className:"p-2 rounded-md hover:bg-purple-600 dark:hover:bg-purple-700 transition-colors","aria-label":"Background settings",title:"Background settings",children:u.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})})}),u.jsxs(Pe,{onClick:o,variant:"secondary",size:"sm",className:"bg-purple-600 hover:bg-purple-500 dark:bg-purple-700 dark:hover:bg-purple-600 border-purple-500",children:[u.jsx("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"})}),"Logout"]})]})]})})})},rv=[{name:"Task Arrange",icon:"📋",children:[{name:"Achievements",href:"/achievements",icon:"🏆"},{name:"Todo",href:"/todo",icon:"✅"},{name:"Plans",href:"/plans",icon:"📅"}]},{name:"Profile",href:"/profile",icon:"👤"},{name:"Blog",href:"/blog",icon:"📝"}],nv=()=>{var a;const{isSidebarOpen:e,setSidebarOpen:t}=ho(),{user:r}=Pl(),n=Ut(),l=()=>t(!1);return u.jsxs(u.Fragment,{children:[e&&u.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden",onClick:l}),u.jsxs("div",{className:`fixed left-0 top-0 h-full w-80 bg-white dark:bg-gray-800 shadow-xl z-50 transform transition-transform duration-300 ease-in-out ${e?"translate-x-0":"-translate-x-full"}`,children:[u.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700",children:[u.jsxs("div",{className:"flex items-center space-x-3",children:[u.jsx("div",{className:"w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center text-white font-semibold",children:((a=r==null?void 0:r.username)==null?void 0:a.charAt(0).toUpperCase())||"U"}),u.jsxs("div",{children:[u.jsx("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:(r==null?void 0:r.username)||"User"}),u.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:r==null?void 0:r.email})]})]}),u.jsx("button",{onClick:l,className:"p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors",children:u.jsx("svg",{className:"w-5 h-5 text-gray-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),u.jsx("nav",{className:"p-4 space-y-2",children:rv.map(o=>{if(o.children)return u.jsxs("div",{className:"space-y-1",children:[u.jsxs("div",{className:"flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300",children:[u.jsx("span",{className:"mr-3 text-lg",children:o.icon}),o.name]}),u.jsx("div",{className:"ml-6 space-y-1",children:o.children.map(s=>{const c=n.pathname===s.href;return u.jsxs(yn,{to:s.href,onClick:l,className:`flex items-center px-3 py-2 text-sm rounded-md transition-colors ${c?"bg-purple-100 text-purple-700 dark:bg-purple-900 dark:text-purple-300":"text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700"}`,children:[u.jsx("span",{className:"mr-3",children:s.icon}),s.name]},s.name)})})]},o.name);const i=n.pathname===o.href;return u.jsxs(yn,{to:o.href,onClick:l,className:`flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${i?"bg-purple-100 text-purple-700 dark:bg-purple-900 dark:text-purple-300":"text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"}`,children:[u.jsx("span",{className:"mr-3 text-lg",children:o.icon}),o.name]},o.name)})}),u.jsx("div",{className:"absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200 dark:border-gray-700",children:u.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400 text-center",children:"YourWorkspace v2.0"})})]})]})},An=({children:e})=>{const{backgroundImage:t}=ho();return u.jsxs("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[t&&u.jsx("div",{className:"fixed inset-0 bg-cover bg-center bg-no-repeat opacity-20 dark:opacity-10",style:{backgroundImage:`url(${t})`}}),u.jsx(tv,{}),u.jsx(nv,{}),u.jsx("main",{className:"relative",children:e})]})},po=({size:e="md",className:t=""})=>{const r={sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"};return u.jsx("div",{className:`animate-spin rounded-full border-2 border-gray-300 border-t-purple-600 dark:border-gray-600 dark:border-t-purple-400 ${r[e]} ${t}`})},In=({children:e})=>{const{isAuthenticated:t,isLoading:r,checkAuth:n}=Pl();return j.useEffect(()=>{n()},[n]),r?u.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:u.jsxs("div",{className:"text-center",children:[u.jsx(po,{size:"lg"}),u.jsx("p",{className:"mt-4 text-gray-600 dark:text-gray-400",children:"Loading..."})]})}):t?u.jsx(u.Fragment,{children:e}):u.jsx(Qn,{to:"/login",replace:!0})},lv=()=>{const[e,t]=j.useState(""),[r,n]=j.useState(""),[l,a]=j.useState(""),{login:o,isLoading:i,isAuthenticated:s}=Pl(),c=co();j.useEffect(()=>{s&&c("/todo")},[s,c]);const d=async f=>{if(f.preventDefault(),a(""),!e.trim()||!r.trim()){a("Please fill in all fields");return}try{await o(e,r),c("/todo")}catch(p){a(p instanceof Error?p.message:"Login failed")}};return u.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8",children:u.jsxs("div",{className:"max-w-md w-full space-y-8",children:[u.jsxs("div",{children:[u.jsx("div",{className:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-purple-100 dark:bg-purple-900",children:u.jsx("svg",{className:"h-6 w-6 text-purple-600 dark:text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})})}),u.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white",children:"Sign in to your account"}),u.jsx("p",{className:"mt-2 text-center text-sm text-gray-600 dark:text-gray-400",children:"Welcome back to YourWorkspace"})]}),u.jsxs("form",{className:"mt-8 space-y-6",onSubmit:d,children:[l&&u.jsx("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3",children:u.jsx("p",{className:"text-red-800 dark:text-red-200 text-sm",children:l})}),u.jsxs("div",{className:"space-y-4",children:[u.jsxs("div",{children:[u.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Email address"}),u.jsx("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,disabled:i,className:"appearance-none relative block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-700 rounded-md focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm disabled:opacity-50",placeholder:"Enter your email",value:e,onChange:f=>t(f.target.value)})]}),u.jsxs("div",{children:[u.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Password"}),u.jsx("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,disabled:i,className:"appearance-none relative block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-700 rounded-md focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm disabled:opacity-50",placeholder:"Enter your password",value:r,onChange:f=>n(f.target.value)})]})]}),u.jsx("div",{children:u.jsx(Pe,{type:"submit",className:"w-full bg-purple-600 hover:bg-purple-700 focus:ring-purple-500",disabled:i,children:i?u.jsxs("div",{className:"flex items-center justify-center",children:[u.jsx(po,{size:"sm",className:"mr-2"}),"Signing in..."]}):"Sign in"})}),u.jsx("div",{className:"text-center",children:u.jsx(yn,{to:"/register",className:"text-purple-600 hover:text-purple-500 dark:text-purple-400 dark:hover:text-purple-300",children:"Don't have an account? Sign up"})})]})]})})};function _e(e){try{return(typeof e=="string"?new Date(e):e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}catch{return"Invalid date"}}function av(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}function ov(e){return/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/.test(e)}const iv=()=>{const[e,t]=j.useState(""),[r,n]=j.useState(""),[l,a]=j.useState(""),[o,i]=j.useState(""),[s,c]=j.useState(""),{register:d,isLoading:f,isAuthenticated:p}=Pl(),x=co();j.useEffect(()=>{p&&x("/todo")},[p,x]);const S=async v=>{if(v.preventDefault(),c(""),!e.trim()||!r.trim()||!l.trim()||!o.trim()){c("Please fill in all fields");return}if(e.trim().length<3){c("Username must be at least 3 characters long");return}if(!av(r)){c("Please enter a valid email address");return}if(!ov(l)){c("Password must be at least 8 characters with uppercase, lowercase, and number");return}if(l!==o){c("Passwords do not match");return}try{await d(e,r,l,o),x("/todo")}catch(_){c(_ instanceof Error?_.message:"Registration failed")}};return u.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8",children:u.jsxs("div",{className:"max-w-md w-full space-y-8",children:[u.jsxs("div",{children:[u.jsx("div",{className:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-purple-100 dark:bg-purple-900",children:u.jsx("svg",{className:"h-6 w-6 text-purple-600 dark:text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"})})}),u.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white",children:"Create your account"}),u.jsx("p",{className:"mt-2 text-center text-sm text-gray-600 dark:text-gray-400",children:"Join YourWorkspace to manage your tasks and goals"})]}),u.jsxs("form",{className:"mt-8 space-y-6",onSubmit:S,children:[s&&u.jsx("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3",children:u.jsx("p",{className:"text-red-800 dark:text-red-200 text-sm",children:s})}),u.jsxs("div",{className:"space-y-4",children:[u.jsxs("div",{children:[u.jsx("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Username"}),u.jsx("input",{id:"username",name:"username",type:"text",autoComplete:"username",required:!0,disabled:f,className:"appearance-none relative block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-700 rounded-md focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm disabled:opacity-50",placeholder:"Choose a username",value:e,onChange:v=>t(v.target.value)}),u.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:"At least 3 characters"})]}),u.jsxs("div",{children:[u.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Email address"}),u.jsx("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,disabled:f,className:"appearance-none relative block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-700 rounded-md focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm disabled:opacity-50",placeholder:"Enter your email",value:r,onChange:v=>n(v.target.value)})]}),u.jsxs("div",{children:[u.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Password"}),u.jsx("input",{id:"password",name:"password",type:"password",autoComplete:"new-password",required:!0,disabled:f,className:"appearance-none relative block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-700 rounded-md focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm disabled:opacity-50",placeholder:"Create a password",value:l,onChange:v=>a(v.target.value)}),u.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:"At least 8 characters with uppercase, lowercase, and number"})]}),u.jsxs("div",{children:[u.jsx("label",{htmlFor:"confirm-password",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Confirm Password"}),u.jsx("input",{id:"confirm-password",name:"confirm-password",type:"password",autoComplete:"new-password",required:!0,disabled:f,className:"appearance-none relative block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-700 rounded-md focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm disabled:opacity-50",placeholder:"Confirm your password",value:o,onChange:v=>i(v.target.value)})]})]}),u.jsx("div",{children:u.jsx(Pe,{type:"submit",className:"w-full bg-purple-600 hover:bg-purple-700 focus:ring-purple-500",disabled:f,children:f?u.jsxs("div",{className:"flex items-center justify-center",children:[u.jsx(po,{size:"sm",className:"mr-2"}),"Creating account..."]}):"Sign up"})}),u.jsx("div",{className:"text-center",children:u.jsx(yn,{to:"/login",className:"text-purple-600 hover:text-purple-500 dark:text-purple-400 dark:hover:text-purple-300",children:"Already have an account? Sign in"})})]})]})})},qs=({title:e,icon:t,leftPanel:r,rightPanel:n,isLoading:l=!1,error:a=null,onAddNew:o,addButtonText:i="Add New"})=>u.jsx("div",{className:"h-screen flex flex-col bg-gray-50 dark:bg-gray-900",children:u.jsxs("div",{className:"flex-1 flex overflow-hidden",children:[u.jsxs("div",{className:"w-full md:w-96 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col",children:[u.jsx("div",{className:"p-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-750",children:u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsxs("div",{className:"flex items-center space-x-2",children:[u.jsx("span",{className:"text-lg",children:t}),u.jsx("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:e})]}),o&&u.jsx("button",{onClick:o,className:"p-2 text-purple-600 hover:bg-purple-50 dark:hover:bg-purple-900 rounded-md transition-colors",title:i,children:u.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})})})]})}),u.jsx("div",{className:"flex-1 overflow-y-auto",children:l?u.jsx("div",{className:"flex items-center justify-center h-32",children:u.jsx(po,{size:"md"})}):a?u.jsx("div",{className:"p-4 text-center text-red-600 dark:text-red-400",children:u.jsxs("p",{children:["Error: ",a]})}):r})]}),u.jsx("div",{className:"hidden md:flex flex-1 bg-white dark:bg-gray-800",children:u.jsx("div",{className:"w-full overflow-y-auto",children:n})})]})}),ea={async getAchievements(){return(await ve.get("/achievements/")).data},async getAchievement(e){return(await ve.get(`/achievements/${e}/`)).data},async createAchievement(e){return(await ve.post("/achievements/",e)).data},async updateAchievement(e,t){return(await ve.put(`/achievements/${e}/`,t)).data},async deleteAchievement(e){await ve.delete(`/achievements/${e}/`)}},sv=Ll(e=>({achievements:[],selectedAchievement:null,isLoading:!1,error:null,fetchAchievements:async()=>{e({isLoading:!0,error:null});try{const t=await ea.getAchievements();e({achievements:t,isLoading:!1})}catch(t){e({error:t instanceof Error?t.message:"Failed to fetch achievements",isLoading:!1})}},selectAchievement:t=>{e({selectedAchievement:t})},addAchievement:async t=>{e({isLoading:!0,error:null});try{const r=await ea.createAchievement(t);e(n=>({achievements:[...n.achievements,r],isLoading:!1}))}catch(r){e({error:r instanceof Error?r.message:"Failed to create achievement",isLoading:!1})}},updateAchievement:async(t,r)=>{e({isLoading:!0,error:null});try{const n=await ea.updateAchievement(t,r);e(l=>{var a;return{achievements:l.achievements.map(o=>o.id===t?n:o),selectedAchievement:((a=l.selectedAchievement)==null?void 0:a.id)===t?n:l.selectedAchievement,isLoading:!1}})}catch(n){e({error:n instanceof Error?n.message:"Failed to update achievement",isLoading:!1})}},deleteAchievement:async t=>{e({isLoading:!0,error:null});try{await ea.deleteAchievement(t),e(r=>{var n;return{achievements:r.achievements.filter(l=>l.id!==t),selectedAchievement:((n=r.selectedAchievement)==null?void 0:n.id)===t?null:r.selectedAchievement,isLoading:!1}})}catch(r){e({error:r instanceof Error?r.message:"Failed to delete achievement",isLoading:!1})}}})),eu=({isOpen:e,onClose:t,title:r,children:n,size:l="md"})=>{if(!e)return null;const a={sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl"};return u.jsx("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:u.jsxs("div",{className:"flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0",children:[u.jsx("div",{className:"fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75 dark:bg-gray-900 dark:bg-opacity-75",onClick:t}),u.jsxs("div",{className:`inline-block w-full ${a[l]} p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white dark:bg-gray-800 shadow-xl rounded-lg`,children:[r&&u.jsxs("div",{className:"flex items-center justify-between mb-4",children:[u.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white",children:r}),u.jsxs("button",{onClick:t,className:"text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 focus:outline-none",children:[u.jsx("span",{className:"sr-only",children:"Close"}),u.jsx("svg",{className:"w-6 h-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})]})]}),n]})]})})},uv=({achievement:e,isSelected:t,onClick:r,onViewDetails:n})=>u.jsx("div",{onClick:r,className:`p-4 rounded-lg border cursor-pointer transition-all ${t?"border-purple-500 bg-purple-50 dark:bg-purple-900/20 dark:border-purple-400":"border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 bg-white dark:bg-gray-800"}`,children:u.jsxs("div",{className:"flex items-start justify-between",children:[u.jsxs("div",{className:"flex-1 min-w-0",children:[u.jsx("h3",{className:"font-medium text-gray-900 dark:text-white truncate",children:e.title}),e.date_achieved&&u.jsx("div",{className:"flex items-center mt-1",children:u.jsxs("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",children:["Achieved on ",_e(e.date_achieved)]})}),e.core_skills_json&&e.core_skills_json.length>0&&u.jsxs("div",{className:"mt-2",children:[u.jsx("p",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Core Skills:"}),u.jsxs("div",{className:"flex flex-wrap gap-1 mt-1",children:[e.core_skills_json.slice(0,3).map((l,a)=>u.jsx("span",{className:"inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200",children:l},a)),e.core_skills_json.length>3&&u.jsxs("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:["+",e.core_skills_json.length-3," more"]})]})]})]}),n&&u.jsx("button",{onClick:l=>{l.stopPropagation(),n()},className:"md:hidden ml-2 p-2 text-purple-600 hover:bg-purple-50 dark:hover:bg-purple-900 rounded-md transition-colors",title:"View details",children:u.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]})}),Xc=({achievement:e,onEdit:t,onDelete:r})=>u.jsxs("div",{className:"h-full flex flex-col",children:[u.jsx("div",{className:"p-6 border-b border-gray-200 dark:border-gray-700",children:u.jsxs("div",{className:"flex items-start justify-between",children:[u.jsxs("div",{className:"flex-1",children:[u.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e.title}),e.date_achieved&&u.jsx("div",{className:"mt-2",children:u.jsxs("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",children:["Achieved on ",_e(e.date_achieved)]})})]}),u.jsxs("div",{className:"flex space-x-2 ml-4",children:[u.jsxs(Pe,{onClick:t,variant:"secondary",size:"sm",children:[u.jsx("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})}),"Edit"]}),u.jsxs(Pe,{onClick:r,variant:"danger",size:"sm",children:[u.jsx("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})}),"Delete"]})]})]})}),u.jsxs("div",{className:"flex-1 overflow-y-auto p-6 space-y-6",children:[e.core_skills_json&&e.core_skills_json.length>0&&u.jsxs("div",{children:[u.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-3",children:"Core Skills"}),u.jsx("div",{className:"flex flex-wrap gap-2",children:e.core_skills_json.map((n,l)=>u.jsx("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",children:n},l))})]}),e.description&&u.jsxs("div",{children:[u.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-3",children:"Description"}),u.jsx("div",{className:"prose dark:prose-invert max-w-none",children:u.jsx("p",{className:"text-gray-700 dark:text-gray-300 whitespace-pre-wrap",children:e.description})})]}),e.quantifiable_results&&u.jsxs("div",{children:[u.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-3",children:"Quantifiable Results"}),u.jsx("div",{className:"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4",children:u.jsx("p",{className:"text-green-800 dark:text-green-200 whitespace-pre-wrap",children:e.quantifiable_results})})]}),u.jsxs("div",{className:"border-t border-gray-200 dark:border-gray-700 pt-6",children:[u.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-3",children:"Details"}),u.jsxs("dl",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2",children:[u.jsxs("div",{children:[u.jsx("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Created"}),u.jsx("dd",{className:"text-sm text-gray-900 dark:text-white",children:_e(e.created_at)})]}),u.jsxs("div",{children:[u.jsx("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Last Updated"}),u.jsx("dd",{className:"text-sm text-gray-900 dark:text-white",children:_e(e.updated_at)})]})]})]})]})]}),cv=()=>{const{achievements:e,selectedAchievement:t,isLoading:r,error:n,fetchAchievements:l,selectAchievement:a,addAchievement:o,updateAchievement:i,deleteAchievement:s}=sv(),[c,d]=j.useState(!1),[f,p]=j.useState(!1),[x,S]=j.useState(!1);j.useEffect(()=>{l()},[l]);const v=()=>{d(!0)},_=()=>{t&&p(!0)},h=async()=>{t&&window.confirm("Are you sure you want to delete this achievement?")&&(await s(t.id),a(null),S(!1))},m=L=>{a(L),S(!0)},g=u.jsx("div",{className:"space-y-2 p-4",children:e.length===0?u.jsxs("div",{className:"text-center py-8 text-gray-500 dark:text-gray-400",children:[u.jsx("p",{children:"No achievements yet."}),u.jsx("p",{className:"text-sm",children:"Add your first achievement to get started!"})]}):e.map(L=>u.jsx(uv,{achievement:L,isSelected:(t==null?void 0:t.id)===L.id,onClick:()=>a(L),onViewDetails:()=>m(L)},L.id))}),E=t?u.jsx(Xc,{achievement:t,onEdit:_,onDelete:h}):u.jsx("div",{className:"flex items-center justify-center h-full text-gray-500 dark:text-gray-400",children:u.jsxs("div",{className:"text-center",children:[u.jsx("svg",{className:"w-16 h-16 mx-auto mb-4 opacity-50",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})}),u.jsx("p",{className:"text-lg font-medium",children:"Select an achievement"}),u.jsx("p",{className:"text-sm",children:"Choose an achievement from the list to view details"})]})});return u.jsxs(u.Fragment,{children:[u.jsx(qs,{title:"Achievements",icon:"🏆",leftPanel:g,rightPanel:E,isLoading:r,error:n,onAddNew:v,addButtonText:"Add Achievement"}),x&&t&&u.jsx("div",{className:"md:hidden fixed inset-0 bg-black bg-opacity-50 z-50",children:u.jsxs("div",{className:"absolute right-0 top-0 h-full w-full max-w-md bg-white dark:bg-gray-800 shadow-xl",children:[u.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700",children:[u.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Achievement Details"}),u.jsx("button",{onClick:()=>S(!1),className:"p-2 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 rounded-md transition-colors",children:u.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),u.jsx("div",{className:"h-full overflow-y-auto pb-16",children:u.jsx(Xc,{achievement:t,onEdit:()=>{S(!1),_()},onDelete:()=>{S(!1),h()}})})]})}),u.jsx(Jc,{isOpen:c,onClose:()=>d(!1),onSubmit:async L=>{await o(L),d(!1)},title:"Add New Achievement"}),t&&u.jsx(Jc,{isOpen:f,onClose:()=>p(!1),onSubmit:async L=>{await i(t.id,L),p(!1)},title:"Edit Achievement",initialData:t})]})},Jc=({isOpen:e,onClose:t,onSubmit:r,title:n,initialData:l})=>{const[a,o]=j.useState({title:(l==null?void 0:l.title)||"",description:(l==null?void 0:l.description)||"",quantifiable_results:(l==null?void 0:l.quantifiable_results)||"",core_skills_json:(l==null?void 0:l.core_skills_json)||[],date_achieved:(l==null?void 0:l.date_achieved)||""}),[i,s]=j.useState(""),[c,d]=j.useState(!1),f=async v=>{if(v.preventDefault(),!!a.title.trim()){d(!0);try{await r(a),o({title:"",description:"",quantifiable_results:"",core_skills_json:[],date_achieved:""}),s("")}catch(_){console.error("Failed to save achievement:",_)}finally{d(!1)}}},p=()=>{var v;i.trim()&&!((v=a.core_skills_json)!=null&&v.includes(i.trim()))&&(o(_=>({..._,core_skills_json:[..._.core_skills_json||[],i.trim()]})),s(""))},x=v=>{o(_=>{var h;return{..._,core_skills_json:((h=_.core_skills_json)==null?void 0:h.filter(m=>m!==v))||[]}})},S=v=>{v.key==="Enter"&&(v.preventDefault(),p())};return u.jsx(eu,{isOpen:e,onClose:t,title:n,size:"lg",children:u.jsxs("form",{onSubmit:f,className:"space-y-6",children:[u.jsxs("div",{children:[u.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Title *"}),u.jsx("input",{type:"text",value:a.title,onChange:v=>o(_=>({..._,title:v.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white",placeholder:"Enter achievement title",required:!0})]}),u.jsxs("div",{children:[u.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Date Achieved"}),u.jsx("input",{type:"date",value:a.date_achieved,onChange:v=>o(_=>({..._,date_achieved:v.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white"})]}),u.jsxs("div",{children:[u.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Core Skills"}),u.jsxs("div",{className:"flex space-x-2 mb-2",children:[u.jsx("input",{type:"text",value:i,onChange:v=>s(v.target.value),onKeyDown:S,className:"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white",placeholder:"Add a skill"}),u.jsx(Pe,{type:"button",onClick:p,size:"sm",children:"Add"})]}),a.core_skills_json&&a.core_skills_json.length>0&&u.jsx("div",{className:"flex flex-wrap gap-2",children:a.core_skills_json.map((v,_)=>u.jsxs("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-sm bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",children:[v,u.jsx("button",{type:"button",onClick:()=>x(v),className:"ml-1 text-blue-600 hover:text-blue-800 dark:text-blue-300 dark:hover:text-blue-100",children:"×"})]},_))})]}),u.jsxs("div",{children:[u.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Description"}),u.jsx("textarea",{value:a.description,onChange:v=>o(_=>({..._,description:v.target.value})),rows:4,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white",placeholder:"Describe your achievement"})]}),u.jsxs("div",{children:[u.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Quantifiable Results"}),u.jsx("textarea",{value:a.quantifiable_results,onChange:v=>o(_=>({..._,quantifiable_results:v.target.value})),rows:3,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white",placeholder:"Measurable outcomes and results"})]}),u.jsxs("div",{className:"flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700",children:[u.jsx(Pe,{type:"button",onClick:t,variant:"secondary",children:"Cancel"}),u.jsx(Pe,{type:"submit",disabled:c||!a.title.trim(),children:c?"Saving...":"Save Achievement"})]})]})})},wr={async getTodos(){return(await ve.get("/todo/todos/")).data},async getTodo(e){return(await ve.get(`/todo/todos/${e}/`)).data},async createTodo(e){return(await ve.post("/todo/todos/",e)).data},async updateTodo(e,t){return(await ve.put(`/todo/todos/${e}/`,t)).data},async deleteTodo(e){await ve.delete(`/todo/todos/${e}/`)},async markCompleted(e){return(await ve.post(`/todo/todos/${e}/mark_completed/`)).data},async setCurrentFocus(e){const r=(await this.getTodos()).find(n=>n.is_current_focus);return r&&r.id!==e&&await this.updateTodo(r.id,{is_current_focus:!1}),await this.updateTodo(e,{is_current_focus:!0})},async getCurrentFocus(){return(await this.getTodos()).find(t=>t.is_current_focus)||null}},dv=Ll((e,t)=>({todos:[],currentFocus:null,isLoading:!1,error:null,fetchTodos:async()=>{e({isLoading:!0,error:null});try{const r=await wr.getTodos(),n=r.find(l=>l.is_current_focus)||null;e({todos:r,currentFocus:n,isLoading:!1})}catch(r){e({error:r instanceof Error?r.message:"Failed to fetch todos",isLoading:!1})}},addTodo:async r=>{e({isLoading:!0,error:null});try{const n=await wr.createTodo(r);e(l=>({todos:[...l.todos,n],isLoading:!1}))}catch(n){e({error:n instanceof Error?n.message:"Failed to create todo",isLoading:!1})}},updateTodo:async(r,n)=>{e({isLoading:!0,error:null});try{const l=await wr.updateTodo(r,n);e(a=>{var o;return{todos:a.todos.map(i=>i.id===r?l:i),currentFocus:l.is_current_focus?l:((o=a.currentFocus)==null?void 0:o.id)===r?null:a.currentFocus,isLoading:!1}})}catch(l){e({error:l instanceof Error?l.message:"Failed to update todo",isLoading:!1})}},deleteTodo:async r=>{e({isLoading:!0,error:null});try{await wr.deleteTodo(r),e(n=>{var l;return{todos:n.todos.filter(a=>a.id!==r),currentFocus:((l=n.currentFocus)==null?void 0:l.id)===r?null:n.currentFocus,isLoading:!1}})}catch(n){e({error:n instanceof Error?n.message:"Failed to delete todo",isLoading:!1})}},setCurrentFocus:async r=>{e({isLoading:!0,error:null});try{const n=await wr.setCurrentFocus(r);e(l=>({todos:l.todos.map(a=>({...a,is_current_focus:a.id===r})),currentFocus:n,isLoading:!1}))}catch(n){e({error:n instanceof Error?n.message:"Failed to set focus",isLoading:!1})}},markCompleted:async r=>{e({isLoading:!0,error:null});try{const n=await wr.markCompleted(r);e(l=>{var a;return{todos:l.todos.map(o=>o.id===r?n:o),currentFocus:((a=l.currentFocus)==null?void 0:a.id)===r?null:l.currentFocus,isLoading:!1}})}catch(n){e({error:n instanceof Error?n.message:"Failed to mark completed",isLoading:!1})}},clearCompleted:async()=>{e({isLoading:!0,error:null});try{const{todos:r}=t(),n=r.filter(l=>l.status==="completed");await Promise.all(n.map(l=>wr.deleteTodo(l.id))),e(l=>({todos:l.todos.filter(a=>a.status!=="completed"),isLoading:!1}))}catch(r){e({error:r instanceof Error?r.message:"Failed to clear completed",isLoading:!1})}}})),Zc=({isOpen:e,onClose:t,onSubmit:r,title:n,initialData:l})=>{const[a,o]=j.useState({title:(l==null?void 0:l.title)||"",description:(l==null?void 0:l.description)||"",due_date:(l==null?void 0:l.due_date)||"",priority:(l==null?void 0:l.priority)||"medium",is_current_focus:(l==null?void 0:l.is_current_focus)||!1}),[i,s]=j.useState(!1),c=async d=>{if(d.preventDefault(),!!a.title.trim()){s(!0);try{await r(a),l||o({title:"",description:"",due_date:"",priority:"medium",is_current_focus:!1})}catch(f){console.error("Failed to save todo:",f)}finally{s(!1)}}};return u.jsx(eu,{isOpen:e,onClose:t,title:n,size:"lg",children:u.jsxs("form",{onSubmit:c,className:"space-y-6",children:[u.jsxs("div",{children:[u.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Title *"}),u.jsx("input",{type:"text",value:a.title,onChange:d=>o(f=>({...f,title:d.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white",placeholder:"Enter task title",required:!0})]}),u.jsxs("div",{children:[u.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Description"}),u.jsx("textarea",{value:a.description,onChange:d=>o(f=>({...f,description:d.target.value})),rows:3,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white",placeholder:"Describe your task (optional)"})]}),u.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[u.jsxs("div",{children:[u.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Priority"}),u.jsxs("select",{value:a.priority,onChange:d=>o(f=>({...f,priority:d.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white",children:[u.jsx("option",{value:"low",children:"Low Priority"}),u.jsx("option",{value:"medium",children:"Medium Priority"}),u.jsx("option",{value:"high",children:"High Priority"})]})]}),u.jsxs("div",{children:[u.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Due Date"}),u.jsx("input",{type:"date",value:a.due_date,onChange:d=>o(f=>({...f,due_date:d.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white"})]})]}),u.jsxs("div",{className:"flex items-center",children:[u.jsx("input",{type:"checkbox",id:"is_current_focus",checked:a.is_current_focus,onChange:d=>o(f=>({...f,is_current_focus:d.target.checked})),className:"w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500 dark:border-gray-600 dark:bg-gray-700"}),u.jsxs("label",{htmlFor:"is_current_focus",className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:["Set as main focus",u.jsx("span",{className:"block text-xs text-gray-500 dark:text-gray-400",children:"This will replace any existing main focus task"})]})]}),u.jsxs("div",{className:"flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700",children:[u.jsx(Pe,{type:"button",onClick:t,variant:"secondary",children:"Cancel"}),u.jsx(Pe,{type:"submit",disabled:i||!a.title.trim(),children:i?"Saving...":l?"Update Task":"Create Task"})]})]})})},$h=e=>{switch(e){case"high":return"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";case"medium":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";case"low":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"}},fv=e=>{switch(e){case"completed":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"in_progress":return"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";case"deferred":return"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200";default:return"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"}},qc=({todo:e,onEdit:t,onDelete:r,onToggleStatus:n,onSetFocus:l,isFocused:a,isCompleted:o=!1})=>u.jsx("div",{className:`p-3 rounded-lg border transition-all ${a?"border-yellow-400 bg-yellow-50 dark:bg-yellow-900/20 dark:border-yellow-500":o?"border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50":"border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 hover:border-gray-300 dark:hover:border-gray-600"}`,children:u.jsxs("div",{className:"flex items-start space-x-3",children:[u.jsx("button",{onClick:n,className:`mt-1 w-4 h-4 rounded border-2 flex items-center justify-center transition-colors ${e.status==="completed"?"bg-green-500 border-green-500 text-white":"border-gray-300 dark:border-gray-600 hover:border-green-400"}`,children:e.status==="completed"&&u.jsx("svg",{className:"w-3 h-3",fill:"currentColor",viewBox:"0 0 20 20",children:u.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})}),u.jsx("div",{className:"flex-1 min-w-0",children:u.jsxs("div",{className:"flex items-start justify-between",children:[u.jsxs("div",{className:"flex-1",children:[u.jsx("h3",{className:`font-medium ${e.status==="completed"?"line-through text-gray-500 dark:text-gray-400":"text-gray-900 dark:text-white"}`,children:e.title}),e.description&&u.jsx("p",{className:`text-sm mt-1 ${e.status==="completed"?"line-through text-gray-400 dark:text-gray-500":"text-gray-600 dark:text-gray-400"}`,children:e.description}),u.jsxs("div",{className:"flex flex-wrap gap-1 mt-2",children:[u.jsxs("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${$h(e.priority)}`,children:[e.priority," priority"]}),e.due_date&&u.jsxs("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",children:["Due ",_e(e.due_date)]})]})]}),u.jsxs("div",{className:"flex items-center space-x-1 ml-2",children:[!o&&u.jsx("button",{onClick:l,className:`p-1 rounded transition-colors ${a?"text-yellow-600 hover:text-yellow-700":"text-gray-400 hover:text-yellow-500"}`,title:a?"Remove from focus":"Set as main focus",children:u.jsx("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:u.jsx("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"})})}),u.jsx("button",{onClick:t,className:"p-1 text-gray-400 hover:text-blue-500 transition-colors",title:"Edit task",children:u.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})}),u.jsx("button",{onClick:r,className:"p-1 text-gray-400 hover:text-red-500 transition-colors",title:"Delete task",children:u.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})]})})]})}),hv=()=>{const{todos:e,currentFocus:t,isLoading:r,error:n,fetchTodos:l,addTodo:a,updateTodo:o,deleteTodo:i,setCurrentFocus:s,clearCompleted:c}=dv(),[d,f]=j.useState(!1),[p,x]=j.useState(null);j.useEffect(()=>{l()},[l]);const S=()=>{f(!0)},v=C=>{x(C)},_=async C=>{window.confirm("Are you sure you want to delete this task?")&&await i(C.id)},h=async C=>{const R=C.status==="completed"?"pending":"completed";await o(C.id,{status:R})},m=async C=>{C.status!=="completed"&&await s(C.id)},g=e.filter(C=>C.status!=="completed"),E=e.filter(C=>C.status==="completed"),L=u.jsxs("div",{className:"h-full flex flex-col",children:[u.jsx("div",{className:"flex-1 overflow-y-auto",children:u.jsx("div",{className:"p-4 space-y-2",children:g.length===0?u.jsxs("div",{className:"text-center py-8 text-gray-500 dark:text-gray-400",children:[u.jsx("p",{children:"No active tasks."}),u.jsx("p",{className:"text-sm",children:"Add your first task to get started!"})]}):g.map(C=>u.jsx(qc,{todo:C,onEdit:()=>v(C),onDelete:()=>_(C),onToggleStatus:()=>h(C),onSetFocus:()=>m(C),isFocused:(t==null?void 0:t.id)===C.id},C.id))})}),E.length>0&&u.jsx("div",{className:"border-t border-gray-200 dark:border-gray-700",children:u.jsxs("div",{className:"p-4",children:[u.jsxs("div",{className:"flex items-center justify-between mb-3",children:[u.jsxs("h3",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:["PAST (",E.length,")"]}),u.jsx(Pe,{variant:"secondary",size:"sm",onClick:c,className:"text-xs",children:"Clear Completed"})]}),u.jsxs("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:[E.slice(0,5).map(C=>u.jsx(qc,{todo:C,onEdit:()=>v(C),onDelete:()=>_(C),onToggleStatus:()=>h(C),onSetFocus:()=>{},isFocused:!1,isCompleted:!0},C.id)),E.length>5&&u.jsxs("p",{className:"text-xs text-gray-500 dark:text-gray-400 text-center",children:["+",E.length-5," more completed tasks"]})]})]})})]}),w=t?u.jsx(pv,{todo:t,onEdit:()=>v(t),onComplete:()=>h(t)}):u.jsx("div",{className:"flex items-center justify-center h-full text-gray-500 dark:text-gray-400",children:u.jsxs("div",{className:"text-center",children:[u.jsx("svg",{className:"w-16 h-16 mx-auto mb-4 opacity-50",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"})}),u.jsx("p",{className:"text-lg font-medium",children:"Main Focus:"}),u.jsx("p",{className:"text-sm mb-4",children:"No task is currently in focus"}),u.jsx("p",{className:"text-xs",children:"Click the star icon on any task to set it as your main focus"})]})});return u.jsxs(u.Fragment,{children:[u.jsx(qs,{title:"Todo",icon:"✅",leftPanel:L,rightPanel:w,isLoading:r,error:n,onAddNew:S,addButtonText:"Add Task"}),u.jsx(Zc,{isOpen:d,onClose:()=>f(!1),onSubmit:async C=>{await a(C),f(!1)},title:"Add New Task"}),p&&u.jsx(Zc,{isOpen:!!p,onClose:()=>x(null),onSubmit:async C=>{await o(p.id,{title:C.title,description:C.description,due_date:C.due_date,priority:C.priority,is_current_focus:C.is_current_focus}),x(null)},title:"Edit Task",initialData:p})]})},pv=({todo:e,onEdit:t,onComplete:r})=>u.jsxs("div",{className:"h-full flex flex-col",children:[u.jsxs("div",{className:"p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20",children:[u.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[u.jsx("svg",{className:"w-8 h-8 text-yellow-500",fill:"currentColor",viewBox:"0 0 20 20",children:u.jsx("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"})}),u.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Main Focus:"})]}),u.jsxs("div",{className:"flex items-start justify-between",children:[u.jsxs("div",{className:"flex-1",children:[u.jsx("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:e.title}),e.description&&u.jsx("p",{className:"text-gray-700 dark:text-gray-300 mb-3",children:e.description}),u.jsxs("div",{className:"flex flex-wrap gap-2",children:[u.jsxs("span",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${$h(e.priority)}`,children:[e.priority," priority"]}),u.jsx("span",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${fv(e.status)}`,children:e.status.replace("_"," ")}),e.due_date&&u.jsxs("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",children:["Due ",_e(e.due_date)]})]})]}),u.jsxs("div",{className:"flex space-x-2 ml-4",children:[u.jsxs(Pe,{onClick:t,variant:"secondary",size:"sm",children:[u.jsx("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})}),"Edit"]}),u.jsxs(Pe,{onClick:r,className:"bg-green-600 hover:bg-green-700",children:[u.jsx("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),"Complete"]})]})]})]}),u.jsx("div",{className:"flex-1 p-6 overflow-y-auto",children:u.jsxs("div",{className:"space-y-6",children:[u.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4",children:[u.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-3",children:"Focus Session"}),u.jsx("p",{className:"text-gray-600 dark:text-gray-400 text-sm mb-4",children:"This task is currently your main focus. Complete it to move forward with your goals."}),u.jsx("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3",children:u.jsx("p",{className:"text-blue-800 dark:text-blue-200 text-sm",children:"💪 You've got this! Focus on one task at a time for maximum productivity."})})]}),u.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4",children:[u.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-3",children:"Task Details"}),u.jsxs("dl",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2",children:[u.jsxs("div",{children:[u.jsx("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Created"}),u.jsx("dd",{className:"text-sm text-gray-900 dark:text-white",children:_e(e.created_at)})]}),u.jsxs("div",{children:[u.jsx("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Last Updated"}),u.jsx("dd",{className:"text-sm text-gray-900 dark:text-white",children:_e(e.updated_at)})]}),e.due_date&&u.jsxs("div",{children:[u.jsx("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Due Date"}),u.jsx("dd",{className:"text-sm text-gray-900 dark:text-white",children:_e(e.due_date)})]}),u.jsxs("div",{children:[u.jsx("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Priority"}),u.jsx("dd",{className:"text-sm text-gray-900 dark:text-white capitalize",children:e.priority})]})]})]})]})})]}),ta={async getPlans(){return(await ve.get("/plans/")).data},async getPlan(e){return(await ve.get(`/plans/${e}/`)).data},async createPlan(e){return(await ve.post("/plans/",e)).data},async updatePlan(e,t){return(await ve.put(`/plans/${e}/`,t)).data},async deletePlan(e){await ve.delete(`/plans/${e}/`)}},mv=Ll(e=>({plans:[],selectedPlan:null,isLoading:!1,error:null,fetchPlans:async()=>{e({isLoading:!0,error:null});try{const t=await ta.getPlans();e({plans:t,isLoading:!1})}catch(t){e({error:t instanceof Error?t.message:"Failed to fetch plans",isLoading:!1})}},selectPlan:t=>{e({selectedPlan:t})},addPlan:async t=>{e({isLoading:!0,error:null});try{const r=await ta.createPlan(t);e(n=>({plans:[...n.plans,r],isLoading:!1}))}catch(r){e({error:r instanceof Error?r.message:"Failed to create plan",isLoading:!1})}},updatePlan:async(t,r)=>{e({isLoading:!0,error:null});try{const n=await ta.updatePlan(t,r);e(l=>{var a;return{plans:l.plans.map(o=>o.id===t?n:o),selectedPlan:((a=l.selectedPlan)==null?void 0:a.id)===t?n:l.selectedPlan,isLoading:!1}})}catch(n){e({error:n instanceof Error?n.message:"Failed to update plan",isLoading:!1})}},deletePlan:async t=>{e({isLoading:!0,error:null});try{await ta.deletePlan(t),e(r=>{var n;return{plans:r.plans.filter(l=>l.id!==t),selectedPlan:((n=r.selectedPlan)==null?void 0:n.id)===t?null:r.selectedPlan,isLoading:!1}})}catch(r){e({error:r instanceof Error?r.message:"Failed to delete plan",isLoading:!1})}}})),ed=({isOpen:e,onClose:t,onSubmit:r,title:n,initialData:l})=>{const[a,o]=j.useState({title:(l==null?void 0:l.title)||"",description:(l==null?void 0:l.description)||"",goal_type:(l==null?void 0:l.goal_type)||void 0,target_date:(l==null?void 0:l.target_date)||"",status:(l==null?void 0:l.status)||"active"}),[i,s]=j.useState(!1),c=async d=>{if(d.preventDefault(),!(!a.title.trim()||!a.description.trim())){s(!0);try{await r(a),l||o({title:"",description:"",goal_type:void 0,target_date:"",status:"active"})}catch(f){console.error("Failed to save plan:",f)}finally{s(!1)}}};return u.jsx(eu,{isOpen:e,onClose:t,title:n,size:"lg",children:u.jsxs("form",{onSubmit:c,className:"space-y-6",children:[u.jsxs("div",{children:[u.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Title *"}),u.jsx("input",{type:"text",value:a.title,onChange:d=>o(f=>({...f,title:d.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white",placeholder:"Enter plan title",required:!0})]}),u.jsxs("div",{children:[u.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Description *"}),u.jsx("textarea",{value:a.description,onChange:d=>o(f=>({...f,description:d.target.value})),rows:4,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white",placeholder:"Describe your plan in detail",required:!0})]}),u.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[u.jsxs("div",{children:[u.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Goal Type"}),u.jsxs("select",{value:a.goal_type||"",onChange:d=>o(f=>({...f,goal_type:d.target.value?d.target.value:void 0})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white",children:[u.jsx("option",{value:"",children:"Select goal type"}),u.jsx("option",{value:"short_term",children:"Short Term Goal"}),u.jsx("option",{value:"long_term",children:"Long Term Vision"}),u.jsx("option",{value:"skill_development",children:"Skill Development"}),u.jsx("option",{value:"career",children:"Career Goal"}),u.jsx("option",{value:"personal",children:"Personal Goal"}),u.jsx("option",{value:"project",children:"Project Goal"})]})]}),l&&u.jsxs("div",{children:[u.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Status"}),u.jsxs("select",{value:a.status,onChange:d=>o(f=>({...f,status:d.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white",children:[u.jsx("option",{value:"active",children:"Active"}),u.jsx("option",{value:"achieved",children:"Achieved"}),u.jsx("option",{value:"deferred",children:"Deferred"}),u.jsx("option",{value:"abandoned",children:"Abandoned"})]})]})]}),u.jsxs("div",{children:[u.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Target Date"}),u.jsx("input",{type:"date",value:a.target_date,onChange:d=>o(f=>({...f,target_date:d.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white"}),u.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:"Optional: Set a target completion date for this plan"})]}),u.jsxs("div",{className:"flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700",children:[u.jsx(Pe,{type:"button",onClick:t,variant:"secondary",children:"Cancel"}),u.jsx(Pe,{type:"submit",disabled:i||!a.title.trim()||!a.description.trim(),children:i?"Saving...":l?"Update Plan":"Create Plan"})]})]})})},Uh=e=>{switch(e){case"career":return"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";case"skill_development":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"personal":return"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200";case"project":return"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";case"long_term":return"bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200";case"short_term":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"}},Bh=e=>{switch(e){case"achieved":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"active":return"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";case"deferred":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";case"abandoned":return"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"}},gv=({plan:e,isSelected:t,onClick:r})=>{const n=e.target_date&&new Date(e.target_date)<new Date&&e.status==="active";return u.jsx("div",{onClick:r,className:`p-4 rounded-lg border cursor-pointer transition-all ${t?"border-purple-500 bg-purple-50 dark:bg-purple-900/20 dark:border-purple-400":"border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 bg-white dark:bg-gray-800"}`,children:u.jsx("div",{className:"flex items-start justify-between",children:u.jsxs("div",{className:"flex-1 min-w-0",children:[u.jsx("h3",{className:"font-medium text-gray-900 dark:text-white truncate",children:e.title}),u.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2",children:e.description}),u.jsxs("div",{className:"flex flex-wrap gap-1 mt-2",children:[e.goal_type&&u.jsx("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${Uh(e.goal_type)}`,children:e.goal_type.replace("_"," ")}),u.jsx("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${Bh(e.status)}`,children:e.status}),e.target_date&&u.jsx("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${n?"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200":"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"}`,children:n?"Overdue":`Target: ${_e(e.target_date)}`})]})]})})})},yv=({plan:e,onEdit:t,onDelete:r})=>{const n=e.target_date&&new Date(e.target_date)<new Date&&e.status==="active";return u.jsxs("div",{className:"h-full flex flex-col",children:[u.jsx("div",{className:"p-6 border-b border-gray-200 dark:border-gray-700",children:u.jsxs("div",{className:"flex items-start justify-between",children:[u.jsxs("div",{className:"flex-1",children:[u.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e.title}),u.jsxs("div",{className:"flex flex-wrap gap-2 mt-3",children:[e.goal_type&&u.jsx("span",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${Uh(e.goal_type)}`,children:e.goal_type.replace("_"," ")}),u.jsx("span",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${Bh(e.status)}`,children:e.status}),e.target_date&&u.jsx("span",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${n?"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200":"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"}`,children:n?`Overdue since ${_e(e.target_date)}`:`Target: ${_e(e.target_date)}`})]})]}),u.jsxs("div",{className:"flex space-x-2 ml-4",children:[u.jsxs(Pe,{onClick:t,variant:"secondary",size:"sm",children:[u.jsx("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})}),"Edit"]}),u.jsxs(Pe,{onClick:r,variant:"danger",size:"sm",children:[u.jsx("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})}),"Delete"]})]})]})}),u.jsxs("div",{className:"flex-1 overflow-y-auto p-6 space-y-6",children:[u.jsxs("div",{children:[u.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-3",children:"Description"}),u.jsx("div",{className:"prose dark:prose-invert max-w-none",children:u.jsx("p",{className:"text-gray-700 dark:text-gray-300 whitespace-pre-wrap",children:e.description})})]}),e.status==="active"&&u.jsxs("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4",children:[u.jsx("h4",{className:"font-medium text-blue-900 dark:text-blue-100 mb-2",children:"📈 Active Plan"}),u.jsxs("p",{className:"text-blue-800 dark:text-blue-200 text-sm",children:["This plan is currently active. Keep working towards your goal!",e.target_date&&u.jsx("span",{className:"block mt-1",children:n?`⚠️ This plan was due on ${_e(e.target_date)}`:`🎯 Target completion: ${_e(e.target_date)}`})]})]}),e.status==="achieved"&&u.jsxs("div",{className:"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4",children:[u.jsx("h4",{className:"font-medium text-green-900 dark:text-green-100 mb-2",children:"🎉 Achieved!"}),u.jsx("p",{className:"text-green-800 dark:text-green-200 text-sm",children:"Congratulations! You've successfully completed this plan."})]}),u.jsxs("div",{className:"border-t border-gray-200 dark:border-gray-700 pt-6",children:[u.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-3",children:"Details"}),u.jsxs("dl",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2",children:[u.jsxs("div",{children:[u.jsx("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Created"}),u.jsx("dd",{className:"text-sm text-gray-900 dark:text-white",children:_e(e.created_at)})]}),u.jsxs("div",{children:[u.jsx("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Last Updated"}),u.jsx("dd",{className:"text-sm text-gray-900 dark:text-white",children:_e(e.updated_at)})]}),e.target_date&&u.jsxs("div",{children:[u.jsx("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Target Date"}),u.jsx("dd",{className:"text-sm text-gray-900 dark:text-white",children:_e(e.target_date)})]}),u.jsxs("div",{children:[u.jsx("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Status"}),u.jsx("dd",{className:"text-sm text-gray-900 dark:text-white capitalize",children:e.status})]})]})]})]})]})},vv=()=>{const{plans:e,selectedPlan:t,isLoading:r,error:n,fetchPlans:l,selectPlan:a,addPlan:o,updatePlan:i,deletePlan:s}=mv(),[c,d]=j.useState(!1),[f,p]=j.useState(!1);j.useEffect(()=>{l()},[l]);const x=()=>{d(!0)},S=()=>{t&&p(!0)},v=async()=>{t&&window.confirm("Are you sure you want to delete this plan?")&&(await s(t.id),a(null))},_=u.jsx("div",{className:"space-y-2 p-4",children:e.length===0?u.jsxs("div",{className:"text-center py-8 text-gray-500 dark:text-gray-400",children:[u.jsx("p",{children:"No plans yet."}),u.jsx("p",{className:"text-sm",children:"Add your first plan to get started!"})]}):e.map(m=>u.jsx(gv,{plan:m,isSelected:(t==null?void 0:t.id)===m.id,onClick:()=>a(m)},m.id))}),h=t?u.jsx(yv,{plan:t,onEdit:S,onDelete:v}):u.jsx("div",{className:"flex items-center justify-center h-full text-gray-500 dark:text-gray-400",children:u.jsxs("div",{className:"text-center",children:[u.jsxs("svg",{className:"w-16 h-16 mx-auto mb-4 opacity-50",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M9 5H7a2 2 0 00-2 2v6a2 2 0 002 2h6a2 2 0 002-2V7a2 2 0 00-2-2H9z"}),u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M9 9h6m-6 4h6m2 5l-8-8 8-8"})]}),u.jsx("p",{className:"text-lg font-medium",children:"Select a plan"}),u.jsx("p",{className:"text-sm",children:"Choose a plan from the list to view details"})]})});return u.jsxs(u.Fragment,{children:[u.jsx(qs,{title:"Plans",icon:"📋",leftPanel:_,rightPanel:h,isLoading:r,error:n,onAddNew:x,addButtonText:"Add Plan"}),u.jsx(ed,{isOpen:c,onClose:()=>d(!1),onSubmit:async m=>{await o(m),d(!1)},title:"Add New Plan"}),t&&u.jsx(ed,{isOpen:f,onClose:()=>p(!1),onSubmit:async m=>{await i(t.id,{title:m.title,description:m.description,goal_type:m.goal_type,target_date:m.target_date}),p(!1)},title:"Edit Plan",initialData:t})]})},xv=()=>u.jsxs("div",{className:"p-6",children:[u.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-6",children:"Profile"}),u.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:u.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Profile page - Coming soon!"})})]}),wv=()=>u.jsxs("div",{className:"p-6",children:[u.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-6",children:"Blog"}),u.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:u.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Blog page - Coming soon!"})})]}),kv=zy([{path:"/",element:u.jsx(Qn,{to:"/todo",replace:!0})},{path:"/login",element:u.jsx(lv,{})},{path:"/register",element:u.jsx(iv,{})},{path:"/achievements",element:u.jsx(In,{children:u.jsx(An,{children:u.jsx(cv,{})})})},{path:"/todo",element:u.jsx(In,{children:u.jsx(An,{children:u.jsx(hv,{})})})},{path:"/plans",element:u.jsx(In,{children:u.jsx(An,{children:u.jsx(vv,{})})})},{path:"/profile",element:u.jsx(In,{children:u.jsx(An,{children:u.jsx(xv,{})})})},{path:"/blog",element:u.jsx(In,{children:u.jsx(An,{children:u.jsx(wv,{})})})},{path:"/achievement",element:u.jsx(Qn,{to:"/achievements",replace:!0})},{path:"/plan",element:u.jsx(Qn,{to:"/plans",replace:!0})},{path:"/anchor",element:u.jsx(Qn,{to:"/profile",replace:!0})}]);Zy();Zo.createRoot(document.getElementById("root")).render(u.jsx(Jo.StrictMode,{children:u.jsx(Qy,{router:kv})}));
