<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dark Mode Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        gray: {
                            750: '#374151',
                        },
                    },
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
    <div class="min-h-screen">
        <!-- Top Navigation -->
        <nav class="bg-purple-700 dark:bg-purple-800 text-white shadow-lg">
            <div class="max-w-full mx-auto px-4">
                <div class="flex items-center justify-between h-16">
                    <div class="flex items-center space-x-4">
                        <button class="p-2 rounded-md hover:bg-purple-600 dark:hover:bg-purple-700 transition-colors">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                            </svg>
                        </button>
                        <h1 class="text-xl font-semibold">Personal Workspace</h1>
                    </div>
                    
                    <div class="flex items-center space-x-2">
                        <button id="themeToggle" class="p-2 rounded-md hover:bg-purple-600 dark:hover:bg-purple-700 transition-colors">
                            <svg id="lightIcon" class="w-5 h-5 hidden dark:block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                            </svg>
                            <svg id="darkIcon" class="w-5 h-5 block dark:hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                            </svg>
                        </button>
                        
                        <button class="bg-purple-600 hover:bg-purple-500 dark:bg-purple-700 dark:hover:bg-purple-600 text-white px-4 py-2 rounded-md text-sm transition-colors">
                            Logout
                        </button>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="flex h-screen">
            <!-- Left Panel -->
            <div class="w-96 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col">
                <div class="p-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-750">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <span class="text-lg">🏆</span>
                            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Achievements</h2>
                        </div>
                        <button class="p-2 text-purple-600 hover:bg-purple-50 dark:hover:bg-purple-900 rounded-md transition-colors">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                            </svg>
                        </button>
                    </div>
                </div>
                
                <div class="flex-1 overflow-y-auto p-4 space-y-2">
                    <!-- Sample Achievement Item -->
                    <div class="p-4 rounded-lg border border-purple-500 bg-purple-50 dark:bg-purple-900/20 dark:border-purple-400 cursor-pointer transition-all">
                        <h3 class="font-medium text-gray-900 dark:text-white">Completed React Project</h3>
                        <div class="flex items-center mt-1">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                                Achieved on Jul 17, 2025
                            </span>
                        </div>
                        <div class="mt-2">
                            <p class="text-sm font-medium text-gray-700 dark:text-gray-300">Core Skills:</p>
                            <div class="flex flex-wrap gap-1 mt-1">
                                <span class="inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">React</span>
                                <span class="inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">TypeScript</span>
                                <span class="inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">Tailwind</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Another Achievement Item -->
                    <div class="p-4 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 bg-white dark:bg-gray-800 cursor-pointer transition-all">
                        <h3 class="font-medium text-gray-900 dark:text-white">Django API Development</h3>
                        <div class="flex items-center mt-1">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                Achieved on Jul 16, 2025
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Panel -->
            <div class="flex-1 bg-white dark:bg-gray-800">
                <div class="h-full flex flex-col">
                    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Completed React Project</h1>
                        <div class="mt-2">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                                Achieved on Jul 17, 2025
                            </span>
                        </div>
                    </div>
                    
                    <div class="flex-1 overflow-y-auto p-6 space-y-6">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">Core Skills</h3>
                            <div class="flex flex-wrap gap-2">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">React</span>
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">TypeScript</span>
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">Tailwind CSS</span>
                            </div>
                        </div>
                        
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">Description</h3>
                            <p class="text-gray-700 dark:text-gray-300">
                                Successfully built a modern React application with TypeScript and Tailwind CSS. 
                                Implemented responsive design, state management, and API integration.
                            </p>
                        </div>
                        
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">Quantifiable Results</h3>
                            <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                                <p class="text-green-800 dark:text-green-200">
                                    • Reduced development time by 40%<br>
                                    • Achieved 95% test coverage<br>
                                    • Improved user experience metrics by 30%
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const themeToggle = document.getElementById('themeToggle');
        const html = document.documentElement;
        
        // Check for saved theme preference or default to light
        const currentTheme = localStorage.getItem('theme') || 'light';
        
        if (currentTheme === 'dark') {
            html.classList.add('dark');
        }
        
        themeToggle.addEventListener('click', () => {
            html.classList.toggle('dark');
            
            const newTheme = html.classList.contains('dark') ? 'dark' : 'light';
            localStorage.setItem('theme', newTheme);
        });
    </script>
</body>
</html>
